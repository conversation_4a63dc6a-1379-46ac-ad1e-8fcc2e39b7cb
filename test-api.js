// Quick API test to verify Google Gemini integration
const API_KEY = 'AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM';

async function testAPI() {
  const prompt = `Create 5 high-converting and catchy podcast names based on the following user input: A podcast about entrepreneurship and business tips. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`;

  try {
    console.log('Testing Google Gemini API...');
    
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        }
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('API Response:', data);
    
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      const generatedText = data.candidates[0].content.parts[0].text;
      console.log('Generated Text:', generatedText);
      
      const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsedResponse = JSON.parse(jsonMatch[0]);
        console.log('Parsed Response:', parsedResponse);
        
        if (parsedResponse.podcast_names && Array.isArray(parsedResponse.podcast_names)) {
          console.log('✅ API test successful!');
          console.log(`Generated ${parsedResponse.podcast_names.length} podcast names:`);
          parsedResponse.podcast_names.forEach((name, index) => {
            console.log(`${index + 1}. ${name.name} - ${name.description}`);
          });
        } else {
          console.log('❌ Invalid response structure');
        }
      } else {
        console.log('❌ No valid JSON found in response');
      }
    } else {
      console.log('❌ Invalid response format');
    }
  } catch (error) {
    console.error('❌ API test failed:', error);
  }
}

// Run the test if this file is executed directly
if (typeof window === 'undefined') {
  testAPI();
}

// Test script for dynamic name replacement logic
// This simulates the behavior of keeping liked names and replacing disliked ones

console.log('🧪 Testing Dynamic Name Replacement Logic\n');

// Mock initial results
const initialResults = [
  { name: "Business Brief", description: "Professional business podcast" },
  { name: "The Growth Show", description: "Entrepreneurship and growth" },
  { name: "Market Minds", description: "Market analysis and insights" },
  { name: "Startup Stories", description: "Stories from startup founders" },
  { name: "Innovation Hub", description: "Latest in business innovation" }
];

// Mock feedback - user liked some, disliked others
const mockFeedback = [
  { index: 0, liked: true, name: "Business Brief" },    // LIKED - keep
  { index: 1, liked: false, name: "The Growth Show" },  // DISLIKED - replace
  { index: 2, liked: true, name: "Market Minds" },      // LIKED - keep
  { index: 3, liked: false, name: "Startup Stories" },  // DISLIKED - replace
  { index: 4, liked: null, name: "Innovation Hub" }     // NEUTRAL - replace
];

// Mock new names from AI
const newNamesFromAI = [
  { name: "Pro Business", description: "Professional business insights" },
  { name: "Success Metrics", description: "Measuring business success" },
  { name: "Executive Edge", description: "Leadership and strategy" }
];

function simulateDynamicReplacement(results, feedback, newNames) {
  console.log('📊 Initial Results:');
  results.forEach((result, index) => {
    const feedbackItem = feedback.find(f => f.index === index);
    const status = feedbackItem?.liked === true ? '👍 LIKED' : 
                   feedbackItem?.liked === false ? '👎 DISLIKED' : '😐 NEUTRAL';
    console.log(`  ${index + 1}. ${result.name} - ${status}`);
  });

  // Get liked names that should be kept
  const likedNames = results.filter((_, index) => {
    const feedbackItem = feedback.find(f => f.index === index);
    return feedbackItem?.liked === true;
  });

  // Count disliked/neutral names that need replacement
  const dislikedCount = results.filter((_, index) => {
    const feedbackItem = feedback.find(f => f.index === index);
    return feedbackItem?.liked === false || feedbackItem?.liked === null;
  }).length;

  console.log(`\n🔄 Replacement Logic:`);
  console.log(`  • Keeping ${likedNames.length} liked names`);
  console.log(`  • Replacing ${dislikedCount} disliked/neutral names`);
  console.log(`  • Generating ${Math.min(dislikedCount, newNames.length)} new names`);

  // Combine liked names with new names
  const actualNewNamesNeeded = Math.min(dislikedCount, newNames.length);
  const combinedResults = [...likedNames, ...newNames.slice(0, actualNewNamesNeeded)];

  console.log(`\n✨ Final Results (${combinedResults.length} names):`);
  combinedResults.forEach((result, index) => {
    const isKept = likedNames.some(liked => liked.name === result.name);
    const status = isKept ? '⭐ KEPT' : '🆕 NEW';
    console.log(`  ${index + 1}. ${result.name} - ${status}`);
  });

  return combinedResults;
}

// Test Scenario 1: Normal case with some liked, some disliked
console.log('='.repeat(60));
console.log('📋 SCENARIO 1: Mixed Feedback (2 liked, 2 disliked, 1 neutral)');
console.log('='.repeat(60));

const scenario1Result = simulateDynamicReplacement(initialResults, mockFeedback, newNamesFromAI);

// Test Scenario 2: All names liked
console.log('\n' + '='.repeat(60));
console.log('📋 SCENARIO 2: All Names Liked');
console.log('='.repeat(60));

const allLikedFeedback = mockFeedback.map(f => ({ ...f, liked: true }));
const scenario2Result = simulateDynamicReplacement(initialResults, allLikedFeedback, newNamesFromAI);

// Test Scenario 3: All names disliked
console.log('\n' + '='.repeat(60));
console.log('📋 SCENARIO 3: All Names Disliked');
console.log('='.repeat(60));

const allDislikedFeedback = mockFeedback.map(f => ({ ...f, liked: false }));
const scenario3Result = simulateDynamicReplacement(initialResults, allDislikedFeedback, newNamesFromAI);

// Validation
console.log('\n' + '='.repeat(60));
console.log('✅ VALIDATION RESULTS');
console.log('='.repeat(60));

console.log(`Scenario 1: ${scenario1Result.length === 5 ? '✅' : '❌'} Correct total count (5)`);
console.log(`Scenario 1: ${scenario1Result.filter(r => r.name === 'Business Brief' || r.name === 'Market Minds').length === 2 ? '✅' : '❌'} Liked names preserved`);

console.log(`Scenario 2: ${scenario2Result.length === 5 ? '✅' : '❌'} All liked scenario handled`);
console.log(`Scenario 2: ${scenario2Result.filter(r => initialResults.some(init => init.name === r.name)).length === 5 ? '✅' : '❌'} Original names kept when all liked`);

console.log(`Scenario 3: ${scenario3Result.length === Math.min(5, newNamesFromAI.length) ? '✅' : '❌'} All disliked scenario handled`);
console.log(`Scenario 3: ${scenario3Result.every(r => newNamesFromAI.some(newName => newName.name === r.name)) ? '✅' : '❌'} All new names when all disliked`);

console.log('\n🎉 Dynamic Replacement Logic Test Complete!');
console.log('Expected behavior: Liked names persist, disliked names get replaced with AI-generated alternatives.');

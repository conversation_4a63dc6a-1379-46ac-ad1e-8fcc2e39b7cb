// Note: This file is for Framer.com usage only
// The framer imports will be available when used in Framer
// For local development, we'll create a mock version

// @ts-ignore - Framer imports are only available in Framer environment
const addPropertyControls = (component: any, controls: any) => {};
// @ts-ignore - Framer imports are only available in Framer environment
const ControlType = { String: 'string' };
import PodcastNameGenerator from './PodcastNameGenerator';

interface FramerPodcastNameGeneratorProps {
  apiKey?: string;
  width?: number;
  height?: number;
}

export default function FramerPodcastNameGenerator(props: FramerPodcastNameGeneratorProps) {
  return (
    <div style={{ width: '100%', height: '100%' }}>
      <PodcastNameGenerator 
        apiKey={props.apiKey}
        style={{ 
          width: '100%', 
          height: '100%',
          minHeight: '600px'
        }}
      />
    </div>
  );
}

// Framer property controls
addPropertyControls(FramerPodcastNameGenerator, {
  apiKey: {
    type: ControlType.String,
    title: "API Key",
    description: "Google Gemini API Key (optional - uses default if not provided)",
    defaultValue: "AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",
    displayTextArea: true,
  },
});

// Set default props for Framer
FramerPodcastNameGenerator.defaultProps = {
  apiKey: "AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",
  width: 800,
  height: 600,
};

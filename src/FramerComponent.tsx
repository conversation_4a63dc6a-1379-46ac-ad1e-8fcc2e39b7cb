// Note: This file is for Framer.com usage only
// The framer imports will be available when used in Framer
// For local development, we'll create a mock version

// @ts-ignore - Framer imports are only available in Framer environment
const addPropertyControls = (component: any, controls: any) => {};
// @ts-ignore - Framer imports are only available in Framer environment
const ControlType = { String: 'string' };
import PodcastNameGenerator from './PodcastNameGenerator';

interface FramerPodcastNameGeneratorProps {
  width?: number;
  height?: number;
}

export default function FramerPodcastNameGenerator(props: FramerPodcastNameGeneratorProps) {
  return (
    <div style={{ width: '100%', height: '100%' }}>
      <PodcastNameGenerator
        style={{
          width: '100%',
          height: '100%',
          minHeight: '600px'
        }}
      />
    </div>
  );
}

// Framer property controls
addPropertyControls(FramerPodcastNameGenerator, {});

// Set default props for Framer
FramerPodcastNameGenerator.defaultProps = {
  width: 800,
  height: 600,
};

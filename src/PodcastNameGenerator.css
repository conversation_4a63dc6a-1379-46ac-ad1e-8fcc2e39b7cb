.podcast-name-generator {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.generator-container {
  width: 100%;
}

.generator-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  text-align: center;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.generator-subtitle {
  font-size: 1.1rem;
  color: #666;
  text-align: center;
  margin: 0 0 32px 0;
  line-height: 1.5;
}

.input-form {
  margin-bottom: 32px;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.input-field {
  width: 100%;
  padding: 16px 20px;
  font-size: 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.input-field:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-field:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.generate-button {
  align-self: flex-start;
  padding: 14px 28px;
  font-size: 1rem;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 160px;
}

.generate-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.generate-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 20px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  color: #dc2626;
  font-weight: 500;
  margin-bottom: 24px;
}

.error-icon {
  font-size: 1.2rem;
}

.loading-container {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.results-container {
  margin-top: 32px;
}

.results-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 24px 0;
  text-align: center;
}

.results-grid {
  display: grid;
  gap: 20px;
}

.result-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 12px;
}

.result-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  flex: 1;
  line-height: 1.3;
}

.copy-button {
  padding: 8px 12px;
  font-size: 0.875rem;
  font-weight: 500;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
}

.copy-button:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.result-description {
  color: #666;
  line-height: 1.5;
  margin: 0;
  font-size: 0.95rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .podcast-name-generator {
    padding: 16px;
    margin: 16px;
  }
  
  .generator-title {
    font-size: 2rem;
  }
  
  .generator-subtitle {
    font-size: 1rem;
  }
  
  .input-container {
    gap: 12px;
  }
  
  .generate-button {
    align-self: stretch;
    width: 100%;
  }
  
  .result-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .copy-button {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .podcast-name-generator {
    padding: 12px;
    margin: 8px;
  }
  
  .generator-title {
    font-size: 1.75rem;
  }
  
  .input-field {
    padding: 12px 16px;
  }
  
  .generate-button {
    padding: 12px 20px;
  }
  
  .result-card {
    padding: 16px;
  }
}

/* Feedback System Styles */
.result-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.feedback-buttons {
  display: flex;
  gap: 8px;
}

.feedback-button {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  height: 44px;
}

.feedback-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feedback-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.like-button.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #059669;
  color: white;
  transform: scale(1.1);
}

.dislike-button.active {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-color: #dc2626;
  color: white;
  transform: scale(1.1);
}

.result-card.liked {
  border-color: #10b981;
  background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
}

.result-card.disliked {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fef7f7 100%);
  opacity: 0.8;
  transform: scale(0.98);
}

.refinement-section {
  margin-top: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 2px solid #e2e8f0;
  text-align: center;
}

.refinement-info {
  margin-bottom: 16px;
}

.refinement-info p {
  color: #475569;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

.refinement-button {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.refinement-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.refinement-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.results-title {
  position: relative;
}

.generation-counter {
  font-size: 0.8em;
  color: #8b5cf6;
  font-weight: 500;
}

.refinement-info {
  color: #10b981;
  font-size: 0.95rem;
  margin: 8px 0 24px 0;
  font-weight: 500;
  text-align: center;
}

/* Mobile responsiveness for feedback buttons */
@media (max-width: 480px) {
  .result-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .feedback-buttons {
    justify-content: center;
  }

  .feedback-button {
    min-width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }

  .refinement-section {
    padding: 16px;
  }

  .refinement-button {
    padding: 12px 24px;
    font-size: 1rem;
  }
}

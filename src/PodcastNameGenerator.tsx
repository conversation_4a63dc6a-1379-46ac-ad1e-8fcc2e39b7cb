import React, { useState } from 'react';
import './PodcastNameGenerator.css';

interface PodcastName {
  name: string;
  description: string;
  domainStatus?: 'checking' | 'available' | 'taken' | 'error';
  suggestedDomain?: string;
}

interface NameFeedback {
  name: string;
  description: string;
  liked: boolean | null; // true = liked, false = disliked, null = no feedback
  timestamp: number;
  index: number; // Track position in results for UI state
}

interface UserPreferences {
  likedNames: NameFeedback[];
  dislikedNames: NameFeedback[];
  patterns: {
    preferredLength: 'short' | 'medium' | 'long' | null;
    preferredStyle: 'descriptive' | 'creative' | 'professional' | 'playful' | null;
    likedKeywords: string[];
    dislikedKeywords: string[];
    preferredStructure: 'single-word' | 'phrase' | 'question' | 'mixed' | null;
  };
  generationRound: number;
}

interface ApiResponse {
  podcast_names: PodcastName[];
}

interface PodcastNameGeneratorProps {
  apiKey?: string;
  className?: string;
  style?: React.CSSProperties;
}

const PodcastNameGenerator: React.FC<PodcastNameGeneratorProps> = ({
  apiKey = 'AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM',
  className = '',
  style = {}
}) => {
  const [input, setInput] = useState('');
  const [results, setResults] = useState<PodcastName[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  // New feedback system state
  const [feedback, setFeedback] = useState<NameFeedback[]>([]);
  const [preferences, setPreferences] = useState<UserPreferences>({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null,
    },
    generationRound: 0,
  });
  const [showRefinementButton, setShowRefinementButton] = useState(false);
  const [isRefining, setIsRefining] = useState(false);
  const [generationHistory, setGenerationHistory] = useState<PodcastName[][]>([]);
  const [showOnboarding, setShowOnboarding] = useState(true);
  const [hasProvidedFeedback, setHasProvidedFeedback] = useState(false);
  const [domainChecking, setDomainChecking] = useState<Set<number>>(new Set());

  // Domain Availability Functions
  const generateDomainName = (podcastName: string): string => {
    return podcastName
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters
      .replace(/\s+/g, '') // Remove spaces
      .replace(/^the/, '') // Remove "the" prefix
      .substring(0, 50); // Limit length
  };

  const checkDomainAvailability = async (domain: string): Promise<'available' | 'taken' | 'error'> => {
    try {
      // Use a simple DNS lookup approach
      // We'll try to resolve the domain - if it resolves, it's likely taken
      const response = await fetch(`https://dns.google/resolve?name=${domain}&type=A`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        return 'error';
      }

      const data = await response.json();

      // If we get answers, the domain is taken
      if (data.Answer && data.Answer.length > 0) {
        return 'taken';
      }

      // If no answers but no error, it might be available
      return 'available';
    } catch (error) {
      console.warn('Domain check failed:', error);
      return 'error';
    }
  };

  const checkDomainsForResults = async (results: PodcastName[]) => {
    const updatedResults = [...results];

    for (let i = 0; i < updatedResults.length; i++) {
      const suggestedDomain = generateDomainName(updatedResults[i].name);
      updatedResults[i].suggestedDomain = suggestedDomain;
      updatedResults[i].domainStatus = 'checking';

      setDomainChecking(prev => new Set([...prev, i]));
    }

    setResults(updatedResults);

    // Check domains asynchronously
    for (let i = 0; i < updatedResults.length; i++) {
      const domain = `${updatedResults[i].suggestedDomain}.com`;

      try {
        const status = await checkDomainAvailability(domain);

        setResults(prevResults => {
          const newResults = [...prevResults];
          if (newResults[i]) {
            newResults[i].domainStatus = status;
          }
          return newResults;
        });
      } catch (error) {
        setResults(prevResults => {
          const newResults = [...prevResults];
          if (newResults[i]) {
            newResults[i].domainStatus = 'error';
          }
          return newResults;
        });
      } finally {
        setDomainChecking(prev => {
          const newSet = new Set(prev);
          newSet.delete(i);
          return newSet;
        });
      }
    }
  };

  // Learning Algorithm Functions
  const analyzeLength = (liked: NameFeedback[], disliked: NameFeedback[]): 'short' | 'medium' | 'long' | null => {
    if (liked.length === 0) return null;

    const likedLengths = liked.map(n => n.name.split(' ').length);
    const dislikedLengths = disliked.map(n => n.name.split(' ').length);
    const avgLikedLength = likedLengths.reduce((a, b) => a + b, 0) / likedLengths.length;

    // Consider disliked lengths to avoid them
    const avgDislikedLength = dislikedLengths.length > 0
      ? dislikedLengths.reduce((a, b) => a + b, 0) / dislikedLengths.length
      : 0;

    // Prefer lengths different from disliked ones
    if (avgLikedLength <= 2 && avgDislikedLength > 2) return 'short';
    if (avgLikedLength <= 4 && (avgDislikedLength <= 2 || avgDislikedLength > 4)) return 'medium';
    if (avgLikedLength > 4 && avgDislikedLength <= 4) return 'long';

    // Fallback to simple liked analysis
    if (avgLikedLength <= 2) return 'short';
    if (avgLikedLength <= 4) return 'medium';
    return 'long';
  };

  const analyzeStyle = (liked: NameFeedback[], disliked: NameFeedback[]): 'descriptive' | 'creative' | 'professional' | 'playful' | null => {
    if (liked.length === 0) return null;

    // Simple keyword-based style analysis
    const likedDescriptions = liked.map(n => n.description.toLowerCase()).join(' ');
    const dislikedDescriptions = disliked.map(n => n.description.toLowerCase()).join(' ');

    // Check for style preferences while avoiding disliked styles
    if (likedDescriptions.includes('professional') || likedDescriptions.includes('business')) {
      if (!dislikedDescriptions.includes('professional') && !dislikedDescriptions.includes('business')) {
        return 'professional';
      }
    }
    if (likedDescriptions.includes('creative') || likedDescriptions.includes('unique')) {
      if (!dislikedDescriptions.includes('creative') && !dislikedDescriptions.includes('unique')) {
        return 'creative';
      }
    }
    if (likedDescriptions.includes('fun') || likedDescriptions.includes('playful')) {
      if (!dislikedDescriptions.includes('fun') && !dislikedDescriptions.includes('playful')) {
        return 'playful';
      }
    }
    return 'descriptive';
  };

  const extractKeywords = (feedback: NameFeedback[]): string[] => {
    const keywords: string[] = [];
    feedback.forEach(f => {
      // Extract meaningful words from names (excluding common words)
      const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
      const nameWords = f.name.toLowerCase().split(/\s+/).filter(word =>
        word.length > 2 && !commonWords.includes(word)
      );
      keywords.push(...nameWords);
    });

    // Return most frequent keywords
    const frequency: { [key: string]: number } = {};
    keywords.forEach(word => frequency[word] = (frequency[word] || 0) + 1);

    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  };

  const analyzePreferences = (feedback: NameFeedback[]): UserPreferences['patterns'] => {
    const liked = feedback.filter(f => f.liked === true);
    const disliked = feedback.filter(f => f.liked === false);

    return {
      preferredLength: analyzeLength(liked, disliked),
      preferredStyle: analyzeStyle(liked, disliked),
      likedKeywords: extractKeywords(liked),
      dislikedKeywords: extractKeywords(disliked),
      preferredStructure: null, // Simplified for now
    };
  };

  const generateAdaptivePrompt = (input: string, preferences: UserPreferences): string => {
    const basePrompt = `Create 5 high-converting and catchy podcast names based on the following user input: ${input}.`;

    let adaptiveInstructions = '';

    // Add preference-based instructions
    if (preferences.patterns.preferredLength) {
      const lengthMap = {
        'short': '1-2 words',
        'medium': '3-4 words',
        'long': '5+ words'
      };
      adaptiveInstructions += `Focus on ${preferences.patterns.preferredLength} names (${lengthMap[preferences.patterns.preferredLength]}). `;
    }

    if (preferences.patterns.preferredStyle) {
      adaptiveInstructions += `Use a ${preferences.patterns.preferredStyle} style. `;
    }

    if (preferences.patterns.likedKeywords.length > 0) {
      adaptiveInstructions += `Incorporate concepts similar to: ${preferences.patterns.likedKeywords.join(', ')}. `;
    }

    if (preferences.patterns.dislikedKeywords.length > 0) {
      adaptiveInstructions += `Avoid concepts like: ${preferences.patterns.dislikedKeywords.join(', ')}. `;
    }

    // Add examples from liked names if available
    if (preferences.likedNames.length > 0) {
      const examples = preferences.likedNames.slice(-2).map(n => n.name).join('", "');
      adaptiveInstructions += `Generate names with similar appeal to: "${examples}". `;
    }

    return `${basePrompt} ${adaptiveInstructions} Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`;
  };

  const generatePodcastNames = async (useAdaptivePrompt: boolean = false) => {
    if (!input.trim()) {
      setError('Please describe what your podcast is about');
      return;
    }

    setLoading(true);
    setError(null);
    setResults([]);

    // Clear previous feedback when starting fresh (not refining)
    if (!useAdaptivePrompt) {
      setFeedback([]);
      setShowRefinementButton(false);
      setIsRefining(false);
      setShowOnboarding(true);
      setHasProvidedFeedback(false);
    } else {
      setIsRefining(true);
    }

    try {
      const prompt = useAdaptivePrompt
        ? generateAdaptivePrompt(input, preferences)
        : `Create 5 high-converting and catchy podcast names based on the following user input: ${input}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`;

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error('Invalid response format from API');
      }

      const generatedText = data.candidates[0].content.parts[0].text;
      
      // Extract JSON from the response
      const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in API response');
      }

      const parsedResponse: ApiResponse = JSON.parse(jsonMatch[0]);
      
      if (!parsedResponse.podcast_names || !Array.isArray(parsedResponse.podcast_names)) {
        throw new Error('Invalid response structure');
      }

      setResults(parsedResponse.podcast_names);

      // Check domain availability for all results
      checkDomainsForResults(parsedResponse.podcast_names);

      // Update generation history and round counter
      setGenerationHistory(prev => [...prev, parsedResponse.podcast_names]);
      setPreferences(prev => ({
        ...prev,
        generationRound: prev.generationRound + 1
      }));

      // Initialize feedback array for new results
      const initialFeedback: NameFeedback[] = parsedResponse.podcast_names.map((name, index) => ({
        name: name.name,
        description: name.description,
        liked: null,
        timestamp: Date.now(),
        index
      }));
      setFeedback(initialFeedback);

    } catch (err) {
      console.error('Error generating podcast names:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
      setIsRefining(false);
    }
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  // Feedback handling functions
  const handleFeedback = async (index: number, liked: boolean) => {
    const updatedFeedback = [...feedback];
    updatedFeedback[index] = {
      ...updatedFeedback[index],
      liked,
      timestamp: Date.now()
    };
    setFeedback(updatedFeedback);

    // Update preferences with new feedback
    const newPreferences = { ...preferences };
    const feedbackItem = updatedFeedback[index];

    if (liked) {
      // Remove from disliked if it was there
      newPreferences.dislikedNames = newPreferences.dislikedNames.filter(n => n.name !== feedbackItem.name);
      // Add to liked if not already there
      if (!newPreferences.likedNames.find(n => n.name === feedbackItem.name)) {
        newPreferences.likedNames.push(feedbackItem);
      }
    } else {
      // Remove from liked if it was there
      newPreferences.likedNames = newPreferences.likedNames.filter(n => n.name !== feedbackItem.name);
      // Add to disliked if not already there
      if (!newPreferences.dislikedNames.find(n => n.name === feedbackItem.name)) {
        newPreferences.dislikedNames.push(feedbackItem);
      }

      // Immediately generate replacement for disliked name
      if (input.trim()) {
        generateReplacementName(index, newPreferences);
      }
    }

    // Analyze patterns and update preferences
    newPreferences.patterns = analyzePreferences([...newPreferences.likedNames, ...newPreferences.dislikedNames]);
    setPreferences(newPreferences);

    // No longer need refinement button since we do instant replacement
    // Keep this for backward compatibility but don't show the button
    const totalFeedback = newPreferences.likedNames.length + newPreferences.dislikedNames.length;
    if (totalFeedback >= 2) {
      setShowRefinementButton(false); // Changed to false for instant replacement
    }

    // Track that user has provided feedback
    if (!hasProvidedFeedback) {
      setHasProvidedFeedback(true);
      setShowOnboarding(false);
    }
  };

  const generateReplacementName = async (dislikedIndex: number, currentPreferences: any) => {
    try {
      // Set loading state for this specific index
      setDomainChecking(prev => new Set([...prev, dislikedIndex]));

      // Generate adaptive prompt with current preferences
      const adaptivePrompt = generateAdaptivePrompt(input, currentPreferences);

      const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' + apiKey, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: adaptivePrompt.replace('Create 5', 'Create 1') // Only need 1 replacement
            }]
          }]
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (!content) {
        throw new Error('No content received from API');
      }

      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      if (!parsedResponse.podcast_names || !Array.isArray(parsedResponse.podcast_names) || parsedResponse.podcast_names.length === 0) {
        throw new Error('Invalid response format');
      }

      const newName = parsedResponse.podcast_names[0];

      // Replace the disliked name with the new one
      setResults(prev => {
        const updated = [...prev];
        updated[dislikedIndex] = newName;
        return updated;
      });

      // Check domain for the new name
      const domainName = generateDomainName(newName.name);
      const domainStatus = await checkDomainAvailability(domainName);

      // Update the new name with domain info
      setResults(prev => {
        const updated = [...prev];
        updated[dislikedIndex] = {
          ...updated[dislikedIndex],
          suggestedDomain: domainName,
          domainStatus: domainStatus
        };
        return updated;
      });

      // Remove feedback for the replaced name (it's now a new name)
      setFeedback(prev => {
        const updated = [...prev];
        updated[dislikedIndex] = {
          name: newName.name,
          description: newName.description,
          liked: null,
          timestamp: Date.now(),
          index: dislikedIndex
        };
        return updated;
      });

    } catch (err) {
      console.error('Error generating replacement name:', err);
      // Could show a subtle error indicator, but don't disrupt the flow
    } finally {
      // Remove loading state
      setDomainChecking(prev => {
        const updated = new Set(prev);
        updated.delete(dislikedIndex);
        return updated;
      });
    }
  };

  const handleRefinement = () => {
    if (input.trim()) {
      generateRefinedNames(input);
    }
  };

  const generateRefinedNames = async (userInput: string) => {
    setLoading(true);
    setError('');
    setIsRefining(true);

    try {
      // Get current liked and disliked names
      const likedNames = results.filter((_, index) => {
        const feedbackItem = feedback.find(f => f.index === index);
        return feedbackItem?.liked === true;
      });

      const dislikedCount = results.filter((_, index) => {
        const feedbackItem = feedback.find(f => f.index === index);
        return feedbackItem?.liked === false;
      }).length;

      // Calculate how many new names we need
      const newNamesNeeded = Math.max(1, dislikedCount); // At least 1 new name
      const totalNamesNeeded = Math.min(5, likedNames.length + newNamesNeeded);
      const actualNewNamesNeeded = totalNamesNeeded - likedNames.length;

      if (actualNewNamesNeeded <= 0) {
        // All names are liked, generate 1 new name to add variety
        const adaptivePrompt = generateAdaptivePrompt(userInput, preferences);
        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' + apiKey, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: adaptivePrompt.replace('Create 5', 'Create 1')
              }]
            }]
          })
        });

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status}`);
        }

        const data = await response.json();
        const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

        if (!content) {
          throw new Error('No content received from API');
        }

        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error('No valid JSON found in response');
        }

        const parsedResponse = JSON.parse(jsonMatch[0]);
        if (!parsedResponse.podcast_names || !Array.isArray(parsedResponse.podcast_names)) {
          throw new Error('Invalid response format');
        }

        // Combine liked names with 1 new name, keeping total at 5
        const combinedResults = [...likedNames, ...parsedResponse.podcast_names].slice(0, 5);
        setResults(combinedResults);
        checkDomainsForResults(combinedResults);
      } else {
        // Generate only the needed number of new names
        const adaptivePrompt = generateAdaptivePrompt(userInput, preferences);
        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' + apiKey, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: adaptivePrompt.replace('Create 5', `Create ${actualNewNamesNeeded}`)
              }]
            }]
          })
        });

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status}`);
        }

        const data = await response.json();
        const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

        if (!content) {
          throw new Error('No content received from API');
        }

        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error('No valid JSON found in response');
        }

        const parsedResponse = JSON.parse(jsonMatch[0]);
        if (!parsedResponse.podcast_names || !Array.isArray(parsedResponse.podcast_names)) {
          throw new Error('Invalid response format');
        }

        // Combine liked names with new names
        const combinedResults = [...likedNames, ...parsedResponse.podcast_names];
        setResults(combinedResults);
        checkDomainsForResults(combinedResults);
      }

      // Update generation history and round counter
      setGenerationHistory(prev => [...prev, results]);
      setPreferences(prev => ({
        ...prev,
        generationRound: prev.generationRound + 1
      }));

      // Reset feedback for new names only (keep feedback for liked names)
      setFeedback(prevFeedback => {
        return prevFeedback.filter(f => {
          const originalResult = results[f.index];
          return likedNames.some(liked => liked.name === originalResult?.name);
        });
      });

      // Update feedback indices to match new results array
      setFeedback(prevFeedback => {
        return prevFeedback.map(f => {
          const originalResult = results[f.index];
          const newIndex = likedNames.findIndex(liked => liked.name === originalResult?.name);
          return newIndex >= 0 ? { ...f, index: newIndex } : f;
        }).filter(f => f.index >= 0);
      });

    } catch (err) {
      console.error('Error generating refined names:', err);
      setError(err instanceof Error ? err.message : 'Failed to generate refined names. Please try again.');
    } finally {
      setLoading(false);
      setIsRefining(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    generatePodcastNames();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      generatePodcastNames();
    }
  };

  return (
    <div className={`podcast-name-generator ${className}`} style={style}>
      <div className="generator-container">
        <h2 className="generator-title">Podcast Name Generator</h2>
        <p className="generator-subtitle">
          Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI
        </p>

        {showOnboarding && (
          <div className="onboarding-banner">
            <div className="onboarding-content">
              <span className="onboarding-icon">💡</span>
              <div className="onboarding-text">
                <strong>Pro Tip:</strong> Use the 👍👎 buttons to rate names. Liked names stay, disliked names get instantly replaced with better suggestions!
              </div>
            </div>
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="input-form">
          <div className="input-container">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Describe what your podcast is about"
              className="input-field"
              rows={3}
              disabled={loading}
            />
            <button
              type="submit"
              disabled={loading || !input.trim()}
              className="generate-button"
            >
              {loading ? 'Generating...' : 'Generate Names'}
            </button>
          </div>
        </form>

        {error && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            {error}
          </div>
        )}

        {loading && (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>{isRefining ? 'Generating better names based on your preferences...' : 'Generating creative podcast names...'}</p>
          </div>
        )}

        {results.length > 0 && (
          <div className="results-container">
            <h3 className="results-title">
              {preferences.generationRound > 1 ? 'Refined Podcast Name Suggestions' : 'Your Podcast Name Suggestions'}
              {generationHistory.length > 1 && (
                <span className="generation-counter"> (Round {preferences.generationRound})</span>
              )}
            </h3>
            {preferences.generationRound > 1 && (
              <p className="refinement-info">
                ✨ These names are tailored based on your preferences from {generationHistory.length - 1} previous round{generationHistory.length > 2 ? 's' : ''}
              </p>
            )}

            {preferences.generationRound === 1 && !hasProvidedFeedback && (
              <div className="feedback-hint">
                <div className="feedback-hint-content">
                  <span className="feedback-hint-icon">👆</span>
                  <p><strong>Like what you see?</strong> Use the 👍👎 buttons below! Liked names will stay, disliked names get instantly replaced with new ones!</p>
                </div>
              </div>
            )}
            <div className="results-grid">
              {results.map((result, index) => {
                const feedbackItem = feedback.find(f => f.index === index);
                const isLiked = feedbackItem?.liked === true;
                const isDisliked = feedbackItem?.liked === false;

                return (
                  <div
                    key={index}
                    className={`result-card ${isLiked ? 'liked' : ''} ${isDisliked ? 'disliked' : ''}`}
                  >
                    <div className="result-header">
                      <h4 className="result-name">
                        {result.name}
                        {isLiked && preferences.generationRound > 1 && (
                          <span className="kept-indicator" title="This name was kept from your previous selection">
                            ⭐
                          </span>
                        )}
                      </h4>
                      <div className="result-actions">
                        <div className="feedback-buttons">
                          <button
                            onClick={() => handleFeedback(index, true)}
                            className={`feedback-button like-button ${isLiked ? 'active' : ''}`}
                            title="I like this name"
                            disabled={loading}
                          >
                            👍
                          </button>
                          <button
                            onClick={() => handleFeedback(index, false)}
                            className={`feedback-button dislike-button ${isDisliked ? 'active' : ''} ${domainChecking.has(index) ? 'loading' : ''}`}
                            title={domainChecking.has(index) ? "Generating replacement..." : "I don't like this name"}
                            disabled={loading || domainChecking.has(index)}
                          >
                            {domainChecking.has(index) ? '🔄' : '👎'}
                          </button>
                        </div>
                        <button
                          onClick={() => copyToClipboard(result.name, index)}
                          className="copy-button"
                          title="Copy podcast name"
                        >
                          {copiedIndex === index ? '✓ Copied!' : '📋 Copy'}
                        </button>
                      </div>
                    </div>
                    <p className="result-description">{result.description}</p>

                    {result.suggestedDomain && (
                      <div className="domain-info">
                        <div className="domain-name">
                          <span className="domain-label">Domain:</span>
                          <code className="domain-text">{result.suggestedDomain}.com</code>
                        </div>
                        <div className={`domain-status ${result.domainStatus}`}>
                          {(result.domainStatus === 'checking' || domainChecking.has(index)) && (
                            <>
                              <span className="domain-spinner">⏳</span>
                              <span>Checking...</span>
                            </>
                          )}
                          {result.domainStatus === 'available' && (
                            <>
                              <span className="domain-icon available">✅</span>
                              <span>Available</span>
                            </>
                          )}
                          {result.domainStatus === 'taken' && (
                            <>
                              <span className="domain-icon taken">❌</span>
                              <span>Taken</span>
                            </>
                          )}
                          {result.domainStatus === 'error' && (
                            <>
                              <span className="domain-icon error">⚠️</span>
                              <span>Check manually</span>
                            </>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {showRefinementButton && !loading && (
              <div className="refinement-section">
                <div className="refinement-info">
                  <p>💡 I'll keep your liked names and replace the disliked ones with better suggestions!</p>
                </div>
                <button
                  onClick={handleRefinement}
                  className="refinement-button"
                  disabled={loading}
                >
                  {isRefining ? '🔄 Refining...' : '🎯 Replace Disliked Names'}
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PodcastNameGenerator;

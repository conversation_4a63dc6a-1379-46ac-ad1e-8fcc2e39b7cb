import React, { useState } from 'react';
import './PodcastNameGenerator.css';

interface PodcastName {
  name: string;
  description: string;
  domainStatus?: 'checking' | 'available' | 'taken' | 'error';
  suggestedDomain?: string;
}

interface NameFeedback {
  name: string;
  description: string;
  liked: boolean | null; // true = liked, false = disliked, null = no feedback
  timestamp: number;
  index: number; // Track position in results for UI state
}

interface UserPreferences {
  likedNames: NameFeedback[];
  dislikedNames: NameFeedback[];
  patterns: {
    preferredLength: 'short' | 'medium' | 'long' | null;
    preferredStyle: 'descriptive' | 'creative' | 'professional' | 'playful' | null;
    likedKeywords: string[];
    dislikedKeywords: string[];
    preferredStructure: 'single-word' | 'phrase' | 'question' | 'mixed' | null;
  };

}

interface ApiResponse {
  podcast_names: PodcastName[];
}

interface PodcastNameGeneratorProps {
  className?: string;
  style?: React.CSSProperties;
}

const PodcastNameGenerator: React.FC<PodcastNameGeneratorProps> = ({
  className = '',
  style = {}
}) => {
  const [input, setInput] = useState('');
  const [results, setResults] = useState<PodcastName[]>([]);
  const [favorites, setFavorites] = useState<PodcastName[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  // New feedback system state
  const [feedback, setFeedback] = useState<NameFeedback[]>([]);
  const [preferences, setPreferences] = useState<UserPreferences>({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null,
    },

  });
  const [showRefinementButton, setShowRefinementButton] = useState(false);
  const [isRefining, setIsRefining] = useState(false);
  // const [generationHistory, setGenerationHistory] = useState<PodcastName[][]>([]);
  // const [showOnboarding, setShowOnboarding] = useState(true);
  const [hasProvidedFeedback, setHasProvidedFeedback] = useState(false);
  const [domainChecking, setDomainChecking] = useState<Set<number>>(new Set());

  // UX improvement state - prevent layout shifts and allow rapid feedback
  const [pendingReplacements, setPendingReplacements] = useState<Set<number>>(new Set());
  const [flyingToFavorites, setFlyingToFavorites] = useState<Set<number>>(new Set());
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Usage limit tracking
  const [_usageCount, setUsageCount] = useState(0);
  const [isLimitReached, setIsLimitReached] = useState(false);
  const USAGE_LIMIT = 100;

  // Usage tracking functions
  const getUsageKey = () => {
    // Create a simple fingerprint based on browser characteristics
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx!.textBaseline = 'top';
    ctx!.font = '14px Arial';
    ctx!.fillText('Usage tracking', 2, 2);
    const fingerprint = canvas.toDataURL();

    // Combine with other browser characteristics
    const userAgent = navigator.userAgent;
    const language = navigator.language;
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    // Create a hash-like identifier
    const combined = fingerprint + userAgent + language + timezone;
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    return `podcast_usage_${Math.abs(hash)}`;
  };

  const checkUsageLimit = (): boolean => {
    const usageKey = getUsageKey();
    const today = new Date().toDateString();
    const storageKey = `${usageKey}_${today}`;

    const stored = localStorage.getItem(storageKey);
    const currentCount = stored ? parseInt(stored, 10) : 0;

    setUsageCount(currentCount);

    if (currentCount >= USAGE_LIMIT) {
      setIsLimitReached(true);
      return false;
    }

    return true;
  };

  const incrementUsageCount = (count: number = 1) => {
    const usageKey = getUsageKey();
    const today = new Date().toDateString();
    const storageKey = `${usageKey}_${today}`;

    const stored = localStorage.getItem(storageKey);
    const currentCount = stored ? parseInt(stored, 10) : 0;
    const newCount = currentCount + count;

    localStorage.setItem(storageKey, newCount.toString());
    setUsageCount(newCount);

    if (newCount >= USAGE_LIMIT) {
      setIsLimitReached(true);
    }
  };

  // Initialize usage tracking on component mount
  React.useEffect(() => {
    checkUsageLimit();
  }, []);

  // Domain Availability Functions with Best Practices
  const generateDomainName = (podcastName: string): string => {
    // Clean the name first
    let cleanName = podcastName
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters
      .replace(/\s+/g, ' ') // Normalize spaces
      .replace(/^the\s+/, '') // Remove "the" prefix
      .trim();

    // Split into words and filter out common stop words
    const stopWords = ['the', 'and', 'for', 'with', 'from', 'show', 'podcast', 'cast', 'of', 'in', 'on', 'at', 'to', 'a', 'an'];
    const allWords = cleanName.split(' ');
    const meaningfulWords = allWords.filter(word =>
      word.length > 2 && !stopWords.includes(word)
    );

    // Strategy 1: Single meaningful word (6-15 chars)
    if (meaningfulWords.length === 1) {
      const word = meaningfulWords[0];
      if (word.length >= 6 && word.length <= 15) {
        return word;
      }
      // If too short, add context
      if (word.length < 6) {
        return word + 'pod';
      }
      // If too long, try natural shortening
      return getNaturalShortening(word);
    }

    // Strategy 2: Two meaningful words combined (aim for 6-15 chars)
    if (meaningfulWords.length >= 2) {
      const word1 = meaningfulWords[0];
      const word2 = meaningfulWords[1];

      // Try direct combination first
      const combined = word1 + word2;
      if (combined.length >= 6 && combined.length <= 15) {
        return combined;
      }

      // Try with natural shortenings
      const short1 = getNaturalShortening(word1);
      const short2 = getNaturalShortening(word2);
      const shortCombined = short1 + short2;

      if (shortCombined.length >= 6 && shortCombined.length <= 15) {
        return shortCombined;
      }

      // Use hyphen for readability if still too long
      if (shortCombined.length > 15) {
        return short1 + '-' + short2;
      }
    }

    // Strategy 3: Use first meaningful word + context
    if (meaningfulWords.length > 0) {
      const mainWord = getNaturalShortening(meaningfulWords[0]);

      // Try different suffixes
      const suffixes = ['cast', 'pod', 'show', 'talk'];
      for (const suffix of suffixes) {
        const candidate = mainWord + suffix;
        if (candidate.length >= 6 && candidate.length <= 15) {
          return candidate;
        }
      }

      // Return just the main word if it's good length
      if (mainWord.length >= 6 && mainWord.length <= 15) {
        return mainWord;
      }
    }

    // Fallback: Use first word from original
    const firstWord = allWords[0];
    if (firstWord && firstWord.length >= 3) {
      const shortened = getNaturalShortening(firstWord);
      return shortened + 'pod';
    }

    // Ultimate fallback
    return 'podcast' + Math.random().toString(36).substring(2, 5);
  };

  // Helper function to naturally shorten words while maintaining readability
  const getNaturalShortening = (word: string): string => {
    if (word.length <= 8) return word;

    // High-quality, brandable abbreviations for common podcast terms
    const naturalShortenings: { [key: string]: string } = {
      // Business & Professional
      'business': 'biz',
      'entrepreneur': 'entre',
      'marketing': 'market',
      'finance': 'fin',
      'startup': 'start',
      'leadership': 'lead',
      'strategy': 'strat',
      'success': 'win',
      'growth': 'grow',
      'innovation': 'innov',
      'management': 'manage',

      // Technology
      'technology': 'tech',
      'development': 'dev',
      'digital': 'digi',
      'software': 'soft',
      'coding': 'code',
      'programming': 'prog',

      // Content & Media
      'stories': 'story',
      'journey': 'path',
      'adventure': 'quest',
      'creative': 'create',
      'entertainment': 'fun',
      'education': 'learn',
      'knowledge': 'know',
      'wisdom': 'wise',

      // Lifestyle & Personal
      'lifestyle': 'life',
      'wellness': 'well',
      'fitness': 'fit',
      'health': 'heal',
      'mindset': 'mind',
      'motivation': 'motive',
      'inspiration': 'inspire',

      // Geographic & Cultural
      'american': 'usa',
      'european': 'euro',
      'international': 'global',
      'community': 'comm',
      'culture': 'cult',
      'society': 'social'
    };

    // Check for natural shortening first
    if (naturalShortenings[word]) {
      return naturalShortenings[word];
    }

    // For compound words, try to split and use first part
    const compoundPrefixes = ['super', 'mega', 'ultra', 'micro', 'mini', 'multi'];
    for (const prefix of compoundPrefixes) {
      if (word.startsWith(prefix) && word.length > prefix.length + 3) {
        const remainder = word.substring(prefix.length);
        if (remainder.length <= 8) {
          return remainder;
        }
      }
    }

    // For words ending in common suffixes, remove them
    const suffixesToRemove = ['ing', 'tion', 'sion', 'ness', 'ment', 'able', 'ible'];
    for (const suffix of suffixesToRemove) {
      if (word.endsWith(suffix) && word.length > suffix.length + 4) {
        const base = word.substring(0, word.length - suffix.length);
        if (base.length >= 4 && base.length <= 8) {
          return base;
        }
      }
    }

    // If word is still too long, take first 8 characters (readable length)
    if (word.length > 8) {
      return word.substring(0, 8);
    }

    return word;
  };

  const checkDomainAvailability = async (domain: string): Promise<'available' | 'taken' | 'error'> => {
    try {
      // Use a simple DNS lookup approach
      // We'll try to resolve the domain - if it resolves, it's likely taken
      const response = await fetch(`https://dns.google/resolve?name=${domain}&type=A`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        return 'error';
      }

      const data = await response.json();

      // If we get answers, the domain is taken
      if (data.Answer && data.Answer.length > 0) {
        return 'taken';
      }

      // If no answers but no error, it might be available
      return 'available';
    } catch (error) {
      console.warn('Domain check failed:', error);
      return 'error';
    }
  };

  const checkDomainsForResults = async (results: PodcastName[]) => {
    const updatedResults = [...results];

    for (let i = 0; i < updatedResults.length; i++) {
      const suggestedDomain = generateDomainName(updatedResults[i].name);
      updatedResults[i].suggestedDomain = suggestedDomain;
      updatedResults[i].domainStatus = 'checking';

      setDomainChecking(prev => new Set([...prev, i]));
    }

    setResults(updatedResults);

    // Check domains asynchronously
    for (let i = 0; i < updatedResults.length; i++) {
      const domain = `${updatedResults[i].suggestedDomain}.com`;

      try {
        const status = await checkDomainAvailability(domain);

        setResults(prevResults => {
          const newResults = [...prevResults];
          if (newResults[i]) {
            newResults[i].domainStatus = status;
          }
          return newResults;
        });
      } catch (error) {
        setResults(prevResults => {
          const newResults = [...prevResults];
          if (newResults[i]) {
            newResults[i].domainStatus = 'error';
          }
          return newResults;
        });
      } finally {
        setDomainChecking(prev => {
          const newSet = new Set(prev);
          newSet.delete(i);
          return newSet;
        });
      }
    }
  };

  // Learning Algorithm Functions
  const analyzeLength = (liked: NameFeedback[], disliked: NameFeedback[]): 'short' | 'medium' | 'long' | null => {
    if (liked.length === 0) return null;

    const likedLengths = liked.map(n => n.name.split(' ').length);
    const dislikedLengths = disliked.map(n => n.name.split(' ').length);
    const avgLikedLength = likedLengths.reduce((a, b) => a + b, 0) / likedLengths.length;

    // Consider disliked lengths to avoid them
    const avgDislikedLength = dislikedLengths.length > 0
      ? dislikedLengths.reduce((a, b) => a + b, 0) / dislikedLengths.length
      : 0;

    // Prefer lengths different from disliked ones
    if (avgLikedLength <= 2 && avgDislikedLength > 2) return 'short';
    if (avgLikedLength <= 4 && (avgDislikedLength <= 2 || avgDislikedLength > 4)) return 'medium';
    if (avgLikedLength > 4 && avgDislikedLength <= 4) return 'long';

    // Fallback to simple liked analysis
    if (avgLikedLength <= 2) return 'short';
    if (avgLikedLength <= 4) return 'medium';
    return 'long';
  };

  const analyzeStyle = (liked: NameFeedback[], disliked: NameFeedback[]): 'descriptive' | 'creative' | 'professional' | 'playful' | null => {
    if (liked.length === 0) return null;

    // Simple keyword-based style analysis
    const likedDescriptions = liked.map(n => n.description.toLowerCase()).join(' ');
    const dislikedDescriptions = disliked.map(n => n.description.toLowerCase()).join(' ');

    // Check for style preferences while avoiding disliked styles
    if (likedDescriptions.includes('professional') || likedDescriptions.includes('business')) {
      if (!dislikedDescriptions.includes('professional') && !dislikedDescriptions.includes('business')) {
        return 'professional';
      }
    }
    if (likedDescriptions.includes('creative') || likedDescriptions.includes('unique')) {
      if (!dislikedDescriptions.includes('creative') && !dislikedDescriptions.includes('unique')) {
        return 'creative';
      }
    }
    if (likedDescriptions.includes('fun') || likedDescriptions.includes('playful')) {
      if (!dislikedDescriptions.includes('fun') && !dislikedDescriptions.includes('playful')) {
        return 'playful';
      }
    }
    return 'descriptive';
  };

  const extractKeywords = (feedback: NameFeedback[]): string[] => {
    const keywords: string[] = [];
    feedback.forEach(f => {
      // Extract meaningful words from names (excluding common words)
      const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
      const nameWords = f.name.toLowerCase().split(/\s+/).filter(word =>
        word.length > 2 && !commonWords.includes(word)
      );
      keywords.push(...nameWords);
    });

    // Return most frequent keywords
    const frequency: { [key: string]: number } = {};
    keywords.forEach(word => frequency[word] = (frequency[word] || 0) + 1);

    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  };

  const analyzePreferences = (feedback: NameFeedback[]): UserPreferences['patterns'] => {
    const liked = feedback.filter(f => f.liked === true);
    const disliked = feedback.filter(f => f.liked === false);

    return {
      preferredLength: analyzeLength(liked, disliked),
      preferredStyle: analyzeStyle(liked, disliked),
      likedKeywords: extractKeywords(liked),
      dislikedKeywords: extractKeywords(disliked),
      preferredStructure: null, // Simplified for now
    };
  };

  const generateAdaptivePrompt = (input: string, preferences: UserPreferences): string => {
    // Collect all existing names to avoid duplicates
    const existingNames = [
      ...preferences.likedNames.map(n => n.name.toLowerCase()),
      ...preferences.dislikedNames.map(n => n.name.toLowerCase()),
      ...results.map(r => r.name.toLowerCase())
    ];

    const basePrompt = `Create 4 unique, high-converting podcast names for: ${input}`;

    let qualityInstructions = `

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;

    // Add existing names to avoid
    if (existingNames.length > 0) {
      qualityInstructions += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${existingNames.map(name => `- ${name}`).join('\n')}
Do not create names that are similar to, variations of, or could be confused with any of the above.`;
    }

    let adaptiveInstructions = '';

    // Add preference-based instructions
    if (preferences.patterns.preferredLength) {
      const lengthMap = {
        'short': '1-2 words (punchy and memorable)',
        'medium': '2-3 words (balanced and brandable)',
        'long': '3-4 words (descriptive but still catchy)'
      };
      adaptiveInstructions += `\nFocus on ${lengthMap[preferences.patterns.preferredLength]}. `;
    }

    if (preferences.patterns.preferredStyle) {
      const styleMap = {
        'descriptive': 'clear, straightforward names that explain the content',
        'creative': 'imaginative, metaphorical, or playful names',
        'professional': 'authoritative, business-focused names',
        'playful': 'fun, energetic, engaging names'
      };
      adaptiveInstructions += `Use ${styleMap[preferences.patterns.preferredStyle] || preferences.patterns.preferredStyle}. `;
    }

    if (preferences.patterns.likedKeywords.length > 0) {
      adaptiveInstructions += `\nIncorporate themes similar to: ${preferences.patterns.likedKeywords.join(', ')}. `;
    }

    if (preferences.patterns.dislikedKeywords.length > 0) {
      adaptiveInstructions += `\nAvoid themes like: ${preferences.patterns.dislikedKeywords.join(', ')}. `;
    }

    // Add examples from liked names if available
    if (preferences.likedNames.length > 0) {
      const examples = preferences.likedNames.slice(-2).map(n => n.name).join('", "');
      adaptiveInstructions += `\nGenerate names with similar appeal to: "${examples}" (but completely different concepts). `;
    }

    return `${basePrompt}${qualityInstructions}${adaptiveInstructions}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;
  };

  const generateSingleNamePrompt = (input: string, preferences: UserPreferences, count: number = 1): string => {
    // Collect all existing names to avoid duplicates
    const existingNames = [
      ...preferences.likedNames.map(n => n.name.toLowerCase()),
      ...preferences.dislikedNames.map(n => n.name.toLowerCase()),
      ...results.map(r => r.name.toLowerCase())
    ];

    const basePrompt = `Create ${count} unique, high-converting podcast name${count > 1 ? 's' : ''} for: ${input}`;

    let qualityInstructions = `

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;

    // Add existing names to avoid
    if (existingNames.length > 0) {
      qualityInstructions += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${existingNames.map(name => `- ${name}`).join('\n')}
Do not create names similar to any of the above.`;
    }

    let adaptiveInstructions = '';
    if (preferences.patterns.likedKeywords.length > 0) {
      adaptiveInstructions += `\nIncorporate themes similar to: ${preferences.patterns.likedKeywords.join(', ')}. `;
    }

    if (preferences.patterns.dislikedKeywords.length > 0) {
      adaptiveInstructions += `\nAvoid themes like: ${preferences.patterns.dislikedKeywords.join(', ')}. `;
    }

    return `${basePrompt}${qualityInstructions}${adaptiveInstructions}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${count > 1 ? ', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}' : ''}]}`;
  };

  const generatePodcastNames = async (useAdaptivePrompt: boolean = false) => {
    if (!input.trim()) {
      setError('Please describe what your podcast is about');
      return;
    }

    // Check usage limit before generating
    if (!checkUsageLimit()) {
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);
    setResults([]);

    // Clear previous feedback when starting fresh (not refining)
    if (!useAdaptivePrompt) {
      setFeedback([]);
      setShowRefinementButton(false);
      setIsRefining(false);
      // setShowOnboarding(true);
      setHasProvidedFeedback(false);
    } else {
      setIsRefining(true);
    }

    try {
      const prompt = useAdaptivePrompt
        ? generateAdaptivePrompt(input, preferences)
        : `Create 4 unique, high-converting podcast names for: ${input}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;

      const response = await fetch('https://api.yttranscribe.com/podcastNameGenerator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error('Invalid response format from API');
      }

      const generatedText = data.candidates[0].content.parts[0].text;

      // Extract JSON from the response
      const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in API response');
      }

      const parsedResponse: ApiResponse = JSON.parse(jsonMatch[0]);

      if (!parsedResponse.podcast_names || !Array.isArray(parsedResponse.podcast_names)) {
        throw new Error('Invalid response structure');
      }

      setResults(parsedResponse.podcast_names);

      // Increment usage count (4 names generated)
      incrementUsageCount(4);

      // Check domain availability for all results
      checkDomainsForResults(parsedResponse.podcast_names);

      // Update generation history
      // setGenerationHistory(prev => [...prev, parsedResponse.podcast_names]);

      // Initialize feedback array for new results
      const initialFeedback: NameFeedback[] = parsedResponse.podcast_names.map((name, index) => ({
        name: name.name,
        description: name.description,
        liked: null,
        timestamp: Date.now(),
        index
      }));
      setFeedback(initialFeedback);

    } catch (err) {
      console.error('Error generating podcast names:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
      setIsRefining(false);
    }
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  // Feedback handling functions
  const handleFeedback = async (index: number, liked: boolean) => {
    const currentName = results[index];
    if (!currentName) return;

    if (liked) {
      // Prevent any scrolling during the entire process
      const currentScrollY = window.scrollY;
      const preventScroll = () => window.scrollTo(0, currentScrollY);

      // Add scroll prevention listener
      window.addEventListener('scroll', preventScroll, { passive: false });

      // Start flying animation immediately
      setFlyingToFavorites(prev => new Set([...prev, index]));

      // Show success message
      setSuccessMessage(`"${currentName.name}" added to favorites!`);
      setTimeout(() => setSuccessMessage(null), 2000);

      // Add to favorites after a brief delay to let animation start
      setTimeout(() => {
        setFavorites(prev => {
          // Check if already in favorites
          if (prev.find(fav => fav.name === currentName.name)) {
            return prev;
          }
          return [...prev, currentName];
        });

        // Ensure scroll position is maintained after state update
        setTimeout(() => {
          window.scrollTo(0, currentScrollY);
        }, 0);
      }, 100);

      // Remove flying animation and scroll prevention after animation completes
      setTimeout(() => {
        setFlyingToFavorites(prev => {
          const newSet = new Set(prev);
          newSet.delete(index);
          return newSet;
        });

        // Remove scroll prevention
        window.removeEventListener('scroll', preventScroll);

        // Final scroll position enforcement
        window.scrollTo(0, currentScrollY);
      }, 700); // Match animation duration

      // Update preferences
      setPreferences(prev => {
        const newPreferences = { ...prev };
        // Remove from disliked if it was there
        newPreferences.dislikedNames = newPreferences.dislikedNames.filter(n => n.name !== currentName.name);
        // Add to liked if not already there
        if (!newPreferences.likedNames.find(n => n.name === currentName.name)) {
          newPreferences.likedNames.push({
            name: currentName.name,
            description: currentName.description,
            liked: true,
            timestamp: Date.now(),
            index: index
          });
        }
        // Analyze patterns and update preferences
        newPreferences.patterns = analyzePreferences([...newPreferences.likedNames, ...newPreferences.dislikedNames]);
        return newPreferences;
      });

      // For liked items: mark as pending replacement to generate new suggestion
      setPendingReplacements(prev => new Set([...prev, index]));

    } else {
      // Prevent scrolling for disliked items too
      const currentScrollY = window.scrollY;

      // For disliked items: mark as pending replacement (will show gray/loading state)
      setPendingReplacements(prev => new Set([...prev, index]));

      // Update preferences for disliked item
      setPreferences(prev => {
        const newPreferences = { ...prev };
        // Remove from liked if it was there
        newPreferences.likedNames = newPreferences.likedNames.filter(n => n.name !== currentName.name);
        // Add to disliked if not already there
        if (!newPreferences.dislikedNames.find(n => n.name === currentName.name)) {
          newPreferences.dislikedNames.push({
            name: currentName.name,
            description: currentName.description,
            liked: false,
            timestamp: Date.now(),
            index: index
          });
        }
        // Analyze patterns and update preferences
        newPreferences.patterns = analyzePreferences([...newPreferences.likedNames, ...newPreferences.dislikedNames]);
        return newPreferences;
      });

      // Ensure no scroll happens for disliked items
      setTimeout(() => {
        window.scrollTo(0, currentScrollY);
      }, 0);
    }

    // Generate a new name to replace this slot for BOTH liked and disliked items
    if (input.trim()) {
      generateReplacementSuggestion(index);
    }

    // Track that user has provided feedback
    if (!hasProvidedFeedback) {
      setHasProvidedFeedback(true);
      // setShowOnboarding(false);
    }
  };

  // Generate replacement for a specific index (UX improvement)
  const generateReplacementSuggestion = async (replaceIndex: number) => {
    // Check usage limit before generating
    if (!checkUsageLimit()) {
      return;
    }

    console.log(`🔄 Starting replacement generation for index ${replaceIndex}`);

    try {
      // Generate single name prompt for replacement (not the full 4-name prompt)
      const singleNamePrompt = generateSingleNamePrompt(input, preferences, 1);

      console.log(`📝 Generated single name prompt for index ${replaceIndex}:`, singleNamePrompt.substring(0, 100) + '...');

      console.log(`🌐 Making API call for index ${replaceIndex}...`);

      const response = await fetch('https://api.yttranscribe.com/podcastNameGenerator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: singleNamePrompt
            }]
          }]
        })
      });

      console.log(`📡 API response status for index ${replaceIndex}:`, response.status);

      if (!response.ok) {
        throw new Error(`Failed to generate replacement suggestion: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`📦 API response data for index ${replaceIndex}:`, data);

      const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (!content) {
        throw new Error('No content in API response');
      }

      console.log(`📄 API content for index ${replaceIndex}:`, content.substring(0, 200) + '...');

      // Parse the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        console.error(`❌ No valid JSON found in response for index ${replaceIndex}:`, content);
        throw new Error('No valid JSON found in response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      console.log(`🔍 Parsed response for index ${replaceIndex}:`, parsedResponse);

      const newName = parsedResponse.podcast_names?.[0];

      if (newName) {
        console.log(`✅ New name generated for index ${replaceIndex}:`, newName);

        // Replace the item at the specific index
        setResults(prev => {
          const newResults = [...prev];
          newResults[replaceIndex] = {
            name: newName.name,
            description: newName.description,
            suggestedDomain: newName.suggestedDomain,
            domainStatus: 'checking'
          };
          console.log(`🔄 Updated results for index ${replaceIndex}:`, newResults[replaceIndex]);
          return newResults;
        });

        // Remove from pending set
        setPendingReplacements(prev => {
          const newSet = new Set(prev);
          newSet.delete(replaceIndex);
          console.log(`🗑️ Removed index ${replaceIndex} from pending replacements. Remaining:`, Array.from(newSet));
          return newSet;
        });

        // Clear feedback for this index so the new name appears as fresh/interactive
        setFeedback(prev => {
          const filtered = prev.filter(f => f.index !== replaceIndex);
          console.log(`🧹 Cleared feedback for index ${replaceIndex}. Remaining feedback:`, filtered);
          return filtered;
        });

        // Check domain availability for the new suggestion
        if (newName.suggestedDomain) {
          console.log(`🌐 Checking domain availability for ${newName.suggestedDomain}...`);
          // Set domain checking state
          setDomainChecking(prev => new Set([...prev, replaceIndex]));

          const domainStatus = await checkDomainAvailability(newName.suggestedDomain);
          console.log(`🏷️ Domain status for ${newName.suggestedDomain}:`, domainStatus);

          // Update the domain status for this specific result
          setResults(prev => {
            const newResults = [...prev];
            if (newResults[replaceIndex]) {
              newResults[replaceIndex].domainStatus = domainStatus;
            }
            return newResults;
          });

          // Remove from domain checking
          setDomainChecking(prev => {
            const newSet = new Set(prev);
            newSet.delete(replaceIndex);
            return newSet;
          });
        }

        console.log(`🎉 Successfully completed replacement for index ${replaceIndex}`);
      } else {
        console.error(`❌ No new name found in parsed response for index ${replaceIndex}:`, parsedResponse);
        throw new Error('No new name found in API response');
      }

    } catch (error) {
      console.error(`❌ Error generating replacement suggestion for index ${replaceIndex}:`, error);
      // Remove from pending on error
      setPendingReplacements(prev => {
        const newSet = new Set(prev);
        newSet.delete(replaceIndex);
        console.log(`🗑️ Removed index ${replaceIndex} from pending on error. Remaining:`, Array.from(newSet));
        return newSet;
      });
      // Clear feedback for this index on error too
      setFeedback(prev => {
        const filtered = prev.filter(f => f.index !== replaceIndex);
        console.log(`🧹 Cleared feedback for index ${replaceIndex} on error. Remaining:`, filtered);
        return filtered;
      });
    }
  };





  const handleRefinement = () => {
    if (input.trim()) {
      generateRefinedNames(input);
    }
  };

  const generateRefinedNames = async (userInput: string) => {
    setLoading(true);
    setError('');
    setIsRefining(true);

    try {
      // Get current liked and disliked names
      const likedNames = results.filter((_, index) => {
        const feedbackItem = feedback.find(f => f.index === index);
        return feedbackItem?.liked === true;
      });

      const dislikedCount = results.filter((_, index) => {
        const feedbackItem = feedback.find(f => f.index === index);
        return feedbackItem?.liked === false;
      }).length;

      // Calculate how many new names we need
      const newNamesNeeded = Math.max(1, dislikedCount); // At least 1 new name
      const totalNamesNeeded = Math.min(5, likedNames.length + newNamesNeeded);
      const actualNewNamesNeeded = totalNamesNeeded - likedNames.length;

      if (actualNewNamesNeeded <= 0) {
        // All names are liked, generate 1 new name to add variety
        // const adaptivePrompt = generateAdaptivePrompt(userInput, preferences);
        const response = await fetch('https://api.yttranscribe.com/podcastNameGenerator', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: generateSingleNamePrompt(userInput, preferences, 1)
              }]
            }]
          })
        });

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status}`);
        }

        const data = await response.json();
        const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

        if (!content) {
          throw new Error('No content received from API');
        }

        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error('No valid JSON found in response');
        }

        const parsedResponse = JSON.parse(jsonMatch[0]);
        if (!parsedResponse.podcast_names || !Array.isArray(parsedResponse.podcast_names)) {
          throw new Error('Invalid response format');
        }

        // Combine liked names with 1 new name, keeping total at 5
        const combinedResults = [...likedNames, ...parsedResponse.podcast_names].slice(0, 5);
        setResults(combinedResults);
        checkDomainsForResults(combinedResults);
      } else {
        // Generate only the needed number of new names
        // const adaptivePrompt = generateAdaptivePrompt(userInput, preferences);
        const response = await fetch('https://api.yttranscribe.com/podcastNameGenerator', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: generateSingleNamePrompt(userInput, preferences, actualNewNamesNeeded)
              }]
            }]
          })
        });

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status}`);
        }

        const data = await response.json();
        const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

        if (!content) {
          throw new Error('No content received from API');
        }

        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error('No valid JSON found in response');
        }

        const parsedResponse = JSON.parse(jsonMatch[0]);
        if (!parsedResponse.podcast_names || !Array.isArray(parsedResponse.podcast_names)) {
          throw new Error('Invalid response format');
        }

        // Combine liked names with new names
        const combinedResults = [...likedNames, ...parsedResponse.podcast_names];
        setResults(combinedResults);
        checkDomainsForResults(combinedResults);
      }

      // Update generation history
      // setGenerationHistory(prev => [...prev, results]);

      // Reset feedback for new names only (keep feedback for liked names)
      setFeedback(prevFeedback => {
        return prevFeedback.filter(f => {
          const originalResult = results[f.index];
          return likedNames.some(liked => liked.name === originalResult?.name);
        });
      });

      // Update feedback indices to match new results array
      setFeedback(prevFeedback => {
        return prevFeedback.map(f => {
          const originalResult = results[f.index];
          const newIndex = likedNames.findIndex(liked => liked.name === originalResult?.name);
          return newIndex >= 0 ? { ...f, index: newIndex } : f;
        }).filter(f => f.index >= 0);
      });

    } catch (err) {
      console.error('Error generating refined names:', err);
      setError(err instanceof Error ? err.message : 'Failed to generate refined names. Please try again.');
    } finally {
      setLoading(false);
      setIsRefining(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    generatePodcastNames();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      generatePodcastNames();
    }
  };

  return (
    <div className={`podcast-name-generator ${className}`} style={style}>
      <div className="generator-container">
        {/* Permanent Header Section */}
        <div className="header-section">
          <h1 className="main-title">Free Podcast Name Generator</h1>
          <h2 className="main-subtitle">Create the Perfect Name for Your Podcast in Seconds</h2>
        </div>

        {/* Benefits Section with Circular Checkmarks */}
        <div className="benefits-section">
          <div className="benefit-item">
            <div className="benefit-checkmark">✓</div>
            <span className="benefit-text">100% Free Forever</span>
          </div>
          <div className="benefit-item">
            <div className="benefit-checkmark">✓</div>
            <span className="benefit-text">No Sign-up Required</span>
          </div>
          <div className="benefit-item">
            <div className="benefit-checkmark">✓</div>
            <span className="benefit-text">Instant Results</span>
          </div>
        </div>



        {/* Usage Limit Reached Message */}
        {isLimitReached && (
          <div className="limit-reached-banner">
            <div className="limit-content">
              <span className="limit-icon">⚠️</span>
              <div className="limit-text">
                <p>You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below.</p>
              </div>
            </div>
          </div>
        )}
        


        {/* Initial Input Section - Always visible when no results */}
        {results.length === 0 && (
          <div className="initial-input-section">
            <form onSubmit={handleSubmit} className="input-form">
              <div className="input-container">
                <textarea
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Describe what your podcast is about"
                  className="input-field"
                  rows={3}
                  disabled={loading}
                />
                <div className="button-social-container">
                  <button
                    type="submit"
                    disabled={loading || !input.trim() || isLimitReached}
                    className={`generate-button ${isLimitReached ? 'disabled' : ''}`}
                  >
                    {loading ? 'Generating...' :
                     isLimitReached ? 'Daily Limit Reached' :
                     'Generate Names'}
                  </button>

                  {/* Social Proof Section - Inline with button */}
                  <div className="social-proof">
                    <div className="user-avatars">
                      <div className="avatar">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User avatar" />
                      </div>
                      <div className="avatar">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User avatar" />
                      </div>
                      <div className="avatar">
                        <img src="https://randomuser.me/api/portraits/men/86.jpg" alt="User avatar" />
                      </div>
                      <div className="avatar">
                        <img src="https://randomuser.me/api/portraits/women/63.jpg" alt="User avatar" />
                      </div>
                      <div className="avatar">
                        <img src="https://randomuser.me/api/portraits/men/54.jpg" alt="User avatar" />
                      </div>
                    </div>
                    <div className="rating-section">
                      <div className="stars">
                        <svg className="star" viewBox="0 0 24 24" fill="currentColor">
                          <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                        </svg>
                        <svg className="star" viewBox="0 0 24 24" fill="currentColor">
                          <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                        </svg>
                        <svg className="star" viewBox="0 0 24 24" fill="currentColor">
                          <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                        </svg>
                        <svg className="star" viewBox="0 0 24 24" fill="currentColor">
                          <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                        </svg>
                        <svg className="star star-partial" viewBox="0 0 24 24" fill="currentColor">
                          <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                        </svg>
                      </div>
                      <span className="trust-text">Trusted by 12k+ users</span>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        )}

        {error && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            {error}
          </div>
        )}

        {successMessage && (
          <div className="success-message">
            <span className="success-icon">✨</span>
            {successMessage}
          </div>
        )}

        {loading && (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>{isRefining ? 'Generating better names based on your preferences...' : 'Generating creative podcast names...'}</p>
          </div>
        )}

        {results.length > 0 && (
          <div className="results-container">
            {/* Favorites Section - Show at top when they exist */}
            {favorites.length > 0 && (
              <div className="favorites-section">
                <div className="favorites-header">
                  <h3>🏆 Your Winning Podcast Names ({favorites.length})</h3>
                  <p className="favorites-subtitle">Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!</p>
                </div>
                <div className="favorites-grid">
                  {favorites.map((favorite, index) => (
                    <div key={`fav-${index}`} className="favorite-card">
                      <div className="favorite-content">
                        <h4 className="favorite-name">{favorite.name}</h4>
                        <p className="favorite-description">{favorite.description}</p>
                        {favorite.suggestedDomain && (
                          <div className="domain-info inline">
                            <span className="domain-label">Domain:</span>
                            <span className="domain-name">{favorite.suggestedDomain}</span>
                            <span className={`domain-status ${favorite.domainStatus}`}>
                              {favorite.domainStatus === 'available' ? '✅ Available' :
                               favorite.domainStatus === 'taken' ? '❌ Taken' :
                               favorite.domainStatus === 'error' ? '⚠️ Check manually' : '🔍 Checking...'}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="favorite-actions">
                        <button
                          onClick={() => copyToClipboard(favorite.name, -1)}
                          className="copy-button small"
                          title="Copy to clipboard"
                        >
                          📋 Copy
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Input Section with Helpful Messaging */}
            <div className="input-section-simple">
              <div className="input-help-message-simple">
                <p className="input-sub-description">💡 Want different suggestions? Update your description below - <strong>your favorites will stay safe!</strong></p>
              </div>

              <form onSubmit={handleSubmit} className="input-form">
                <div className="input-container">
                  <textarea
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Describe what your podcast is about"
                    className="input-field"
                    rows={3}
                    disabled={loading}
                  />
                  <div className="button-social-container">
                    <button
                      type="submit"
                      disabled={loading || !input.trim() || isLimitReached}
                      className={`generate-button ${isLimitReached ? 'disabled' : ''}`}
                    >
                      {loading ? 'Generating...' :
                       isLimitReached ? 'Daily Limit Reached' :
                       'Generate Names'}
                    </button>

                    {/* Social Proof Section - Inline with button */}
                    <div className="social-proof">
                      <div className="user-avatars">
                        <div className="avatar">
                          <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User avatar" />
                        </div>
                        <div className="avatar">
                          <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User avatar" />
                        </div>
                        <div className="avatar">
                          <img src="https://randomuser.me/api/portraits/men/86.jpg" alt="User avatar" />
                        </div>
                        <div className="avatar">
                          <img src="https://randomuser.me/api/portraits/women/63.jpg" alt="User avatar" />
                        </div>
                        <div className="avatar">
                          <img src="https://randomuser.me/api/portraits/men/54.jpg" alt="User avatar" />
                        </div>
                      </div>
                      <div className="rating-section">
                        <div className="stars">
                          <svg className="star" viewBox="0 0 24 24" fill="currentColor">
                            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                          </svg>
                          <svg className="star" viewBox="0 0 24 24" fill="currentColor">
                            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                          </svg>
                          <svg className="star" viewBox="0 0 24 24" fill="currentColor">
                            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                          </svg>
                          <svg className="star" viewBox="0 0 24 24" fill="currentColor">
                            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                          </svg>
                          <svg className="star star-partial" viewBox="0 0 24 24" fill="currentColor">
                            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                          </svg>
                        </div>
                        <span className="trust-text">Trusted by 12k+ users</span>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </div>



            {/* Current Suggestions */}
            <div className="suggestions-section">
              <div className="suggestions-header">
                <h3>🎯 Current Suggestions</h3>

              </div>

              {/* AI Learning Info Box */}
              <div className="onboarding-banner">
                <div className="onboarding-content">
                  <span className="onboarding-icon">💡</span>
                  <div className="onboarding-text">
                    <strong>Smart AI Learning:</strong> The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste.
                  </div>
                </div>
              </div>
            <div className="results-grid">
              {results.map((result, index) => {
                const feedbackItem = feedback.find(f => f.index === index);
                const isLiked = feedbackItem?.liked === true;
                const isDisliked = feedbackItem?.liked === false;
                const isPendingReplacement = pendingReplacements.has(index);
                const isGeneratingReplacement = domainChecking.has(index);

                return (
                  <div
                    key={index}
                    className={`result-card ${isLiked ? 'liked' : ''} ${isDisliked ? 'disliked' : ''} ${isPendingReplacement ? 'pending' : ''} ${flyingToFavorites.has(index) ? 'flying-to-favorites' : ''}`}
                    style={{
                      opacity: isPendingReplacement ? 0.6 : 1,
                      pointerEvents: isPendingReplacement ? 'none' : 'auto'
                    }}
                  >
                    <div className="result-header">
                      <h4 className="result-name">
                        {isPendingReplacement ?
                          (isLiked ? 'Generating new suggestion...' : 'Generating better suggestion...') :
                          result.name}
                      </h4>
                      <div className="result-actions">
                        <div className="feedback-buttons">
                          <button
                            onClick={() => handleFeedback(index, true)}
                            className={`feedback-button like-button ${isLiked ? 'active' : ''}`}
                            title="I like this name"
                            disabled={isPendingReplacement}
                          >
                            👍
                          </button>
                          <button
                            onClick={() => handleFeedback(index, false)}
                            className={`feedback-button dislike-button ${isDisliked ? 'active' : ''} ${isPendingReplacement ? 'loading' : ''}`}
                            title={isPendingReplacement ? "Generating replacement..." : "I don't like this name"}
                            disabled={isPendingReplacement}
                          >
                            {isPendingReplacement ? '🔄' : '👎'}
                          </button>
                        </div>
                        <button
                          onClick={() => copyToClipboard(result.name, index)}
                          className="copy-button"
                          title="Copy podcast name"
                          disabled={isPendingReplacement}
                        >
                          {copiedIndex === index ? '✓ Copied!' : '📋 Copy'}
                        </button>
                      </div>
                    </div>
                    <p className="result-description">
                      {isPendingReplacement ?
                        (isLiked ? 'Added to favorites! Generating a new suggestion...' : 'Creating a better suggestion based on your preferences...') :
                        result.description}
                    </p>

                    {result.suggestedDomain && !isGeneratingReplacement && (
                      <div className="domain-info inline">
                        <span className="domain-label">Domain:</span>
                        <code className="domain-text">{result.suggestedDomain}.com</code>
                        <span className={`domain-status ${result.domainStatus}`}>
                          {(result.domainStatus === 'checking' || domainChecking.has(index)) && '⏳ Checking...'}
                          {result.domainStatus === 'available' && '✅ Available'}
                          {result.domainStatus === 'taken' && '❌ Taken'}
                          {result.domainStatus === 'error' && '⚠️ Check manually'}
                        </span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
            </div>

            {showRefinementButton && !loading && (
              <div className="refinement-section">
                <div className="refinement-info">
                  <p>💡 I'll keep your liked names and replace the disliked ones with better suggestions!</p>
                </div>
                <button
                  onClick={handleRefinement}
                  className="refinement-button"
                  disabled={loading}
                >
                  {isRefining ? '🔄 Refining...' : '🎯 Replace Disliked Names'}
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PodcastNameGenerator;

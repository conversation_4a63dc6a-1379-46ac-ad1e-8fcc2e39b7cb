import Ce, { useState as y } from "react";
var xe = { exports: {} }, ie = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Te;
function De() {
  if (Te) return ie;
  Te = 1;
  var $ = Symbol.for("react.transitional.element"), G = Symbol.for("react.fragment");
  function j(U, f, v) {
    var O = null;
    if (v !== void 0 && (O = "" + v), f.key !== void 0 && (O = "" + f.key), "key" in f) {
      v = {};
      for (var Y in f)
        Y !== "key" && (v[Y] = f[Y]);
    } else v = f;
    return f = v.ref, {
      $$typeof: $,
      type: U,
      key: O,
      ref: f !== void 0 ? f : null,
      props: v
    };
  }
  return ie.Fragment = G, ie.jsx = j, ie.jsxs = j, ie;
}
var le = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Ae;
function Le() {
  return Ae || (Ae = 1, process.env.NODE_ENV !== "production" && function() {
    function $(s) {
      if (s == null) return null;
      if (typeof s == "function")
        return s.$$typeof === Ne ? null : s.displayName || s.name || null;
      if (typeof s == "string") return s;
      switch (s) {
        case C:
          return "Fragment";
        case ce:
          return "Profiler";
        case J:
          return "StrictMode";
        case W:
          return "Suspense";
        case ve:
          return "SuspenseList";
        case K:
          return "Activity";
      }
      if (typeof s == "object")
        switch (typeof s.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), s.$$typeof) {
          case Z:
            return "Portal";
          case pe:
            return (s.displayName || "Context") + ".Provider";
          case ge:
            return (s._context.displayName || "Context") + ".Consumer";
          case de:
            var d = s.render;
            return s = s.displayName, s || (s = d.displayName || d.name || "", s = s !== "" ? "ForwardRef(" + s + ")" : "ForwardRef"), s;
          case me:
            return d = s.displayName || null, d !== null ? d : $(s.type) || "Memo";
          case ee:
            d = s._payload, s = s._init;
            try {
              return $(s(d));
            } catch {
            }
        }
      return null;
    }
    function G(s) {
      return "" + s;
    }
    function j(s) {
      try {
        G(s);
        var d = !1;
      } catch {
        d = !0;
      }
      if (d) {
        d = console;
        var h = d.error, N = typeof Symbol == "function" && Symbol.toStringTag && s[Symbol.toStringTag] || s.constructor.name || "Object";
        return h.call(
          d,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          N
        ), G(s);
      }
    }
    function U(s) {
      if (s === C) return "<>";
      if (typeof s == "object" && s !== null && s.$$typeof === ee)
        return "<...>";
      try {
        var d = $(s);
        return d ? "<" + d + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function f() {
      var s = q.A;
      return s === null ? null : s.getOwner();
    }
    function v() {
      return Error("react-stack-top-frame");
    }
    function O(s) {
      if (ue.call(s, "key")) {
        var d = Object.getOwnPropertyDescriptor(s, "key").get;
        if (d && d.isReactWarning) return !1;
      }
      return s.key !== void 0;
    }
    function Y(s, d) {
      function h() {
        te || (te = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          d
        ));
      }
      h.isReactWarning = !0, Object.defineProperty(s, "key", {
        get: h,
        configurable: !0
      });
    }
    function T() {
      var s = $(this.type);
      return se[s] || (se[s] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), s = this.props.ref, s !== void 0 ? s : null;
    }
    function z(s, d, h, N, _, E, L, V) {
      return h = E.ref, s = {
        $$typeof: Q,
        type: s,
        key: d,
        props: E,
        _owner: _
      }, (h !== void 0 ? h : null) !== null ? Object.defineProperty(s, "ref", {
        enumerable: !1,
        get: T
      }) : Object.defineProperty(s, "ref", { enumerable: !1, value: null }), s._store = {}, Object.defineProperty(s._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(s, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(s, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: L
      }), Object.defineProperty(s, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: V
      }), Object.freeze && (Object.freeze(s.props), Object.freeze(s)), s;
    }
    function X(s, d, h, N, _, E, L, V) {
      var b = d.children;
      if (b !== void 0)
        if (N)
          if (he(b)) {
            for (N = 0; N < b.length; N++)
              D(b[N]);
            Object.freeze && Object.freeze(b);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else D(b);
      if (ue.call(d, "key")) {
        b = $(s);
        var F = Object.keys(d).filter(function(fe) {
          return fe !== "key";
        });
        N = 0 < F.length ? "{key: someKey, " + F.join(": ..., ") + ": ...}" : "{key: someKey}", P[b + N] || (F = 0 < F.length ? "{" + F.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          N,
          b,
          F,
          b
        ), P[b + N] = !0);
      }
      if (b = null, h !== void 0 && (j(h), b = "" + h), O(d) && (j(d.key), b = "" + d.key), "key" in d) {
        h = {};
        for (var ae in d)
          ae !== "key" && (h[ae] = d[ae]);
      } else h = d;
      return b && Y(
        h,
        typeof s == "function" ? s.displayName || s.name || "Unknown" : s
      ), z(
        s,
        b,
        E,
        _,
        f(),
        h,
        L,
        V
      );
    }
    function D(s) {
      typeof s == "object" && s !== null && s.$$typeof === Q && s._store && (s._store.validated = 1);
    }
    var H = Ce, Q = Symbol.for("react.transitional.element"), Z = Symbol.for("react.portal"), C = Symbol.for("react.fragment"), J = Symbol.for("react.strict_mode"), ce = Symbol.for("react.profiler"), ge = Symbol.for("react.consumer"), pe = Symbol.for("react.context"), de = Symbol.for("react.forward_ref"), W = Symbol.for("react.suspense"), ve = Symbol.for("react.suspense_list"), me = Symbol.for("react.memo"), ee = Symbol.for("react.lazy"), K = Symbol.for("react.activity"), Ne = Symbol.for("react.client.reference"), q = H.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, ue = Object.prototype.hasOwnProperty, he = Array.isArray, B = console.createTask ? console.createTask : function() {
      return null;
    };
    H = {
      "react-stack-bottom-frame": function(s) {
        return s();
      }
    };
    var te, se = {}, be = H["react-stack-bottom-frame"].bind(
      H,
      v
    )(), ne = B(U(v)), P = {};
    le.Fragment = C, le.jsx = function(s, d, h, N, _) {
      var E = 1e4 > q.recentlyCreatedOwnerStacks++;
      return X(
        s,
        d,
        h,
        !1,
        N,
        _,
        E ? Error("react-stack-top-frame") : be,
        E ? B(U(s)) : ne
      );
    }, le.jsxs = function(s, d, h, N, _) {
      var E = 1e4 > q.recentlyCreatedOwnerStacks++;
      return X(
        s,
        d,
        h,
        !0,
        N,
        _,
        E ? Error("react-stack-top-frame") : be,
        E ? B(U(s)) : ne
      );
    };
  }()), le;
}
process.env.NODE_ENV === "production" ? xe.exports = De() : xe.exports = Le();
var e = xe.exports;
const Ie = ({
  className: $ = "",
  style: G = {}
}) => {
  const [j, U] = y(""), [f, v] = y([]), [O, Y] = y([]), [T, z] = y(!1), [X, D] = y(null), [H, Q] = y(null), [Z, C] = y([]), [J, ce] = y({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    }
  }), [ge, pe] = y(!1), [de, W] = y(!1), [ve, me] = y(!1), [ee, K] = y(/* @__PURE__ */ new Set()), [Ne, q] = y(/* @__PURE__ */ new Set()), [ue, he] = y(/* @__PURE__ */ new Set()), [B, te] = y(null), se = (t) => {
    const n = window.scrollY, a = document.documentElement.scrollHeight, i = window.innerHeight, l = a - n - i;
    t(), requestAnimationFrame(() => {
      const o = document.documentElement.scrollHeight, r = window.innerHeight, c = o - l - r;
      Math.abs(o - a) > 5 ? (window.scrollTo(0, Math.max(0, c)), console.log("📏 Scroll adjusted for height change:", {
        heightChange: o - a,
        oldScrollY: n,
        newScrollY: Math.max(0, c)
      })) : window.scrollTo(0, n);
    });
  }, [be, ne] = y(0), [P, s] = y(!1), d = 100, h = () => {
    const t = document.createElement("canvas"), n = t.getContext("2d");
    n.textBaseline = "top", n.font = "14px Arial", n.fillText("Usage tracking", 2, 2);
    const a = t.toDataURL(), i = navigator.userAgent, l = navigator.language, o = Intl.DateTimeFormat().resolvedOptions().timeZone, r = a + i + l + o;
    let c = 0;
    for (let m = 0; m < r.length; m++) {
      const u = r.charCodeAt(m);
      c = (c << 5) - c + u, c = c & c;
    }
    return `podcast_usage_${Math.abs(c)}`;
  }, N = () => {
    const t = h(), n = (/* @__PURE__ */ new Date()).toDateString(), a = `${t}_${n}`, i = localStorage.getItem(a), l = i ? parseInt(i, 10) : 0;
    return ne(l), l >= d ? (s(!0), !1) : !0;
  }, _ = (t = 1) => {
    const n = h(), a = (/* @__PURE__ */ new Date()).toDateString(), i = `${n}_${a}`, l = localStorage.getItem(i), r = (l ? parseInt(l, 10) : 0) + t;
    localStorage.setItem(i, r.toString()), ne(r), r >= d && s(!0);
  };
  Ce.useEffect(() => {
    N();
  }, []);
  const E = (t) => {
    let n = t.toLowerCase().replace(/[^a-z0-9\s]/g, "").replace(/\s+/g, " ").replace(/^the\s+/, "").trim();
    const a = ["the", "and", "for", "with", "from", "show", "podcast", "cast", "of", "in", "on", "at", "to", "a", "an"], i = n.split(" "), l = i.filter(
      (r) => r.length > 2 && !a.includes(r)
    );
    if (l.length === 1) {
      const r = l[0];
      return r.length >= 6 && r.length <= 15 ? r : r.length < 6 ? r + "pod" : L(r);
    }
    if (l.length >= 2) {
      const r = l[0], c = l[1], m = r + c;
      if (m.length >= 6 && m.length <= 15)
        return m;
      const u = L(r), k = L(c), x = u + k;
      if (x.length >= 6 && x.length <= 15)
        return x;
      if (x.length > 15)
        return u + "-" + k;
    }
    if (l.length > 0) {
      const r = L(l[0]), c = ["cast", "pod", "show", "talk"];
      for (const m of c) {
        const u = r + m;
        if (u.length >= 6 && u.length <= 15)
          return u;
      }
      if (r.length >= 6 && r.length <= 15)
        return r;
    }
    const o = i[0];
    return o && o.length >= 3 ? L(o) + "pod" : "podcast" + Math.random().toString(36).substring(2, 5);
  }, L = (t) => {
    if (t.length <= 8) return t;
    const n = {
      // Business & Professional
      business: "biz",
      entrepreneur: "entre",
      marketing: "market",
      finance: "fin",
      startup: "start",
      leadership: "lead",
      strategy: "strat",
      success: "win",
      growth: "grow",
      innovation: "innov",
      management: "manage",
      // Technology
      technology: "tech",
      development: "dev",
      digital: "digi",
      software: "soft",
      coding: "code",
      programming: "prog",
      // Content & Media
      stories: "story",
      journey: "path",
      adventure: "quest",
      creative: "create",
      entertainment: "fun",
      education: "learn",
      knowledge: "know",
      wisdom: "wise",
      // Lifestyle & Personal
      lifestyle: "life",
      wellness: "well",
      fitness: "fit",
      health: "heal",
      mindset: "mind",
      motivation: "motive",
      inspiration: "inspire",
      // Geographic & Cultural
      american: "usa",
      european: "euro",
      international: "global",
      community: "comm",
      culture: "cult",
      society: "social"
    };
    if (n[t])
      return n[t];
    const a = ["super", "mega", "ultra", "micro", "mini", "multi"];
    for (const l of a)
      if (t.startsWith(l) && t.length > l.length + 3) {
        const o = t.substring(l.length);
        if (o.length <= 8)
          return o;
      }
    const i = ["ing", "tion", "sion", "ness", "ment", "able", "ible"];
    for (const l of i)
      if (t.endsWith(l) && t.length > l.length + 4) {
        const o = t.substring(0, t.length - l.length);
        if (o.length >= 4 && o.length <= 8)
          return o;
      }
    return t.length > 8 ? t.substring(0, 8) : t;
  }, V = async (t) => {
    try {
      const n = await fetch(`https://dns.google/resolve?name=${t}&type=A`, {
        method: "GET",
        headers: {
          Accept: "application/json"
        }
      });
      if (!n.ok)
        return "error";
      const a = await n.json();
      return a.Answer && a.Answer.length > 0 ? "taken" : "available";
    } catch (n) {
      return console.warn("Domain check failed:", n), "error";
    }
  }, b = async (t) => {
    const n = [...t];
    for (let a = 0; a < n.length; a++) {
      const i = E(n[a].name);
      n[a].suggestedDomain = i, n[a].domainStatus = "checking", K((l) => /* @__PURE__ */ new Set([...l, a]));
    }
    v(n);
    for (let a = 0; a < n.length; a++) {
      const i = `${n[a].suggestedDomain}.com`;
      try {
        const l = await V(i);
        v((o) => {
          const r = [...o];
          return r[a] && (r[a].domainStatus = l), r;
        });
      } catch {
        v((o) => {
          const r = [...o];
          return r[a] && (r[a].domainStatus = "error"), r;
        });
      } finally {
        K((l) => {
          const o = new Set(l);
          return o.delete(a), o;
        });
      }
    }
  }, F = (t, n) => {
    if (t.length === 0) return null;
    const a = t.map((r) => r.name.split(" ").length), i = n.map((r) => r.name.split(" ").length), l = a.reduce((r, c) => r + c, 0) / a.length, o = i.length > 0 ? i.reduce((r, c) => r + c, 0) / i.length : 0;
    return l <= 2 && o > 2 ? "short" : l <= 4 && (o <= 2 || o > 4) ? "medium" : l > 4 && o <= 4 ? "long" : l <= 2 ? "short" : l <= 4 ? "medium" : "long";
  }, ae = (t, n) => {
    if (t.length === 0) return null;
    const a = t.map((l) => l.description.toLowerCase()).join(" "), i = n.map((l) => l.description.toLowerCase()).join(" ");
    return (a.includes("professional") || a.includes("business")) && !i.includes("professional") && !i.includes("business") ? "professional" : (a.includes("creative") || a.includes("unique")) && !i.includes("creative") && !i.includes("unique") ? "creative" : (a.includes("fun") || a.includes("playful")) && !i.includes("fun") && !i.includes("playful") ? "playful" : "descriptive";
  }, fe = (t) => {
    const n = [];
    t.forEach((i) => {
      const l = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], o = i.name.toLowerCase().split(/\s+/).filter(
        (r) => r.length > 2 && !l.includes(r)
      );
      n.push(...o);
    });
    const a = {};
    return n.forEach((i) => a[i] = (a[i] || 0) + 1), Object.entries(a).sort(([, i], [, l]) => l - i).slice(0, 5).map(([i]) => i);
  }, je = (t) => {
    const n = t.filter((i) => i.liked === !0), a = t.filter((i) => i.liked === !1);
    return {
      preferredLength: F(n, a),
      preferredStyle: ae(n, a),
      likedKeywords: fe(n),
      dislikedKeywords: fe(a),
      preferredStructure: null
      // Simplified for now
    };
  }, Pe = (t, n) => {
    const a = [
      ...n.likedNames.map((r) => r.name.toLowerCase()),
      ...n.dislikedNames.map((r) => r.name.toLowerCase()),
      ...f.map((r) => r.name.toLowerCase())
    ], i = `Create 4 unique, high-converting podcast names for: ${t}`;
    let l = `

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;
    a.length > 0 && (l += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${a.map((r) => `- ${r}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);
    let o = "";
    if (n.patterns.preferredLength && (o += `
Focus on ${{
      short: "1-2 words (punchy and memorable)",
      medium: "2-3 words (balanced and brandable)",
      long: "3-4 words (descriptive but still catchy)"
    }[n.patterns.preferredLength]}. `), n.patterns.preferredStyle && (o += `Use ${{
      descriptive: "clear, straightforward names that explain the content",
      creative: "imaginative, metaphorical, or playful names",
      professional: "authoritative, business-focused names",
      playful: "fun, energetic, engaging names"
    }[n.patterns.preferredStyle] || n.patterns.preferredStyle}. `), n.patterns.likedKeywords.length > 0 && (o += `
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (o += `
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `), n.likedNames.length > 0) {
      const r = n.likedNames.slice(-2).map((c) => c.name).join('", "');
      o += `
Generate names with similar appeal to: "${r}" (but completely different concepts). `;
    }
    return `${i}${l}${o}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;
  }, we = (t, n, a = 1) => {
    const i = [
      ...n.likedNames.map((c) => c.name.toLowerCase()),
      ...n.dislikedNames.map((c) => c.name.toLowerCase()),
      ...f.map((c) => c.name.toLowerCase())
    ], l = `Create ${a} unique, high-converting podcast name${a > 1 ? "s" : ""} for: ${t}`;
    let o = `

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;
    i.length > 0 && (o += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${i.map((c) => `- ${c}`).join(`
`)}
Do not create names similar to any of the above.`);
    let r = "";
    return n.patterns.likedKeywords.length > 0 && (r += `
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (r += `
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `), `${l}${o}${r}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${a > 1 ? ', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}' : ""}]}`;
  }, ye = async (t = !1) => {
    if (!j.trim()) {
      D("Please describe what your podcast is about");
      return;
    }
    if (!N()) {
      D(null);
      return;
    }
    z(!0), D(null), v([]), t ? W(!0) : (C([]), pe(!1), W(!1), me(!1));
    try {
      const n = t ? Pe(j, J) : `Create 4 unique, high-converting podcast names for: ${j}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`, a = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: n
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!a.ok)
        throw new Error(`API request failed: ${a.status} ${a.statusText}`);
      const i = await a.json();
      if (!i.candidates || !i.candidates[0] || !i.candidates[0].content)
        throw new Error("Invalid response format from API");
      const o = i.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!o)
        throw new Error("No valid JSON found in API response");
      const r = JSON.parse(o[0]);
      if (!r.podcast_names || !Array.isArray(r.podcast_names))
        throw new Error("Invalid response structure");
      v(r.podcast_names), _(4), b(r.podcast_names);
      const c = r.podcast_names.map((m, u) => ({
        name: m.name,
        description: m.description,
        liked: null,
        timestamp: Date.now(),
        index: u
      }));
      C(c);
    } catch (n) {
      console.error("Error generating podcast names:", n), D(n instanceof Error ? n.message : "An unexpected error occurred");
    } finally {
      z(!1), W(!1);
    }
  }, ke = async (t, n) => {
    try {
      await navigator.clipboard.writeText(t), Q(n), setTimeout(() => Q(null), 2e3);
    } catch (a) {
      console.error("Failed to copy text:", a);
    }
  }, Se = async (t, n) => {
    const a = f[t];
    if (a) {
      if (n) {
        const i = window.scrollY, l = document.documentElement.scrollHeight, o = window.innerHeight, r = l - i - o;
        console.log("📏 Before adding favorite:", {
          scrollY: i,
          documentHeight: l,
          scrollFromBottom: r
        }), he((c) => /* @__PURE__ */ new Set([...c, t])), se(() => {
          te(`"${a.name}" added to favorites!`);
        }), setTimeout(() => {
          se(() => {
            te(null);
          });
        }, 2e3), setTimeout(() => {
          Y((c) => c.find((m) => m.name === a.name) ? c : [...c, a]), setTimeout(() => {
            const c = document.documentElement.scrollHeight, m = window.innerHeight, u = c - l, k = c - r - m;
            console.log("📏 After adding favorite:", {
              afterDocumentHeight: c,
              heightDifference: u,
              newScrollY: k,
              scrollFromBottom: r
            }), window.scrollTo(0, Math.max(0, k));
          }, 0);
        }, 100), setTimeout(() => {
          he((c) => {
            const m = new Set(c);
            return m.delete(t), m;
          });
        }, 700), ce((c) => {
          const m = { ...c };
          return m.dislikedNames = m.dislikedNames.filter((u) => u.name !== a.name), m.likedNames.find((u) => u.name === a.name) || m.likedNames.push({
            name: a.name,
            description: a.description,
            liked: !0,
            timestamp: Date.now(),
            index: t
          }), m.patterns = je([...m.likedNames, ...m.dislikedNames]), m;
        }), q((c) => /* @__PURE__ */ new Set([...c, t]));
      } else {
        const i = window.scrollY;
        console.log("👎 Disliking item, maintaining scroll at:", i), q((l) => /* @__PURE__ */ new Set([...l, t])), ce((l) => {
          const o = { ...l };
          return o.likedNames = o.likedNames.filter((r) => r.name !== a.name), o.dislikedNames.find((r) => r.name === a.name) || o.dislikedNames.push({
            name: a.name,
            description: a.description,
            liked: !1,
            timestamp: Date.now(),
            index: t
          }), o.patterns = je([...o.likedNames, ...o.dislikedNames]), o;
        }), setTimeout(() => {
          window.scrollTo(0, i);
        }, 0);
      }
      j.trim() && _e(t), ve || me(!0);
    }
  }, _e = async (t) => {
    var n, a, i, l, o, r;
    if (N()) {
      console.log(`🔄 Starting replacement generation for index ${t}`);
      try {
        const c = we(j, J, 1);
        console.log(`📝 Generated single name prompt for index ${t}:`, c.substring(0, 100) + "..."), console.log(`🌐 Making API call for index ${t}...`);
        const m = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: c
              }]
            }]
          })
        });
        if (console.log(`📡 API response status for index ${t}:`, m.status), !m.ok)
          throw new Error(`Failed to generate replacement suggestion: ${m.status} ${m.statusText}`);
        const u = await m.json();
        console.log(`📦 API response data for index ${t}:`, u);
        const k = (o = (l = (i = (a = (n = u.candidates) == null ? void 0 : n[0]) == null ? void 0 : a.content) == null ? void 0 : i.parts) == null ? void 0 : l[0]) == null ? void 0 : o.text;
        if (!k)
          throw new Error("No content in API response");
        console.log(`📄 API content for index ${t}:`, k.substring(0, 200) + "...");
        const x = k.match(/\{[\s\S]*\}/);
        if (!x)
          throw console.error(`❌ No valid JSON found in response for index ${t}:`, k), new Error("No valid JSON found in response");
        const re = JSON.parse(x[0]);
        console.log(`🔍 Parsed response for index ${t}:`, re);
        const A = (r = re.podcast_names) == null ? void 0 : r[0];
        if (A) {
          if (console.log(`✅ New name generated for index ${t}:`, A), v((M) => {
            const w = [...M];
            return w[t] = {
              name: A.name,
              description: A.description,
              suggestedDomain: A.suggestedDomain,
              domainStatus: "checking"
            }, console.log(`🔄 Updated results for index ${t}:`, w[t]), w;
          }), q((M) => {
            const w = new Set(M);
            return w.delete(t), console.log(`🗑️ Removed index ${t} from pending replacements. Remaining:`, Array.from(w)), w;
          }), C((M) => {
            const w = M.filter((g) => g.index !== t);
            return console.log(`🧹 Cleared feedback for index ${t}. Remaining feedback:`, w), w;
          }), A.suggestedDomain) {
            console.log(`🌐 Checking domain availability for ${A.suggestedDomain}...`), K((w) => /* @__PURE__ */ new Set([...w, t]));
            const M = await V(A.suggestedDomain);
            console.log(`🏷️ Domain status for ${A.suggestedDomain}:`, M), v((w) => {
              const g = [...w];
              return g[t] && (g[t].domainStatus = M), g;
            }), K((w) => {
              const g = new Set(w);
              return g.delete(t), g;
            });
          }
          console.log(`🎉 Successfully completed replacement for index ${t}`);
        } else
          throw console.error(`❌ No new name found in parsed response for index ${t}:`, re), new Error("No new name found in API response");
      } catch (c) {
        console.error(`❌ Error generating replacement suggestion for index ${t}:`, c), q((m) => {
          const u = new Set(m);
          return u.delete(t), console.log(`🗑️ Removed index ${t} from pending on error. Remaining:`, Array.from(u)), u;
        }), C((m) => {
          const u = m.filter((k) => k.index !== t);
          return console.log(`🧹 Cleared feedback for index ${t} on error. Remaining:`, u), u;
        });
      }
    }
  }, $e = () => {
    j.trim() && Oe(j);
  }, Oe = async (t) => {
    var n, a, i, l, o, r, c, m, u, k;
    z(!0), D(""), W(!0);
    try {
      const x = f.filter((g, R) => {
        const p = Z.find((S) => S.index === R);
        return (p == null ? void 0 : p.liked) === !0;
      }), re = f.filter((g, R) => {
        const p = Z.find((S) => S.index === R);
        return (p == null ? void 0 : p.liked) === !1;
      }).length, A = Math.max(1, re), w = Math.min(5, x.length + A) - x.length;
      if (w <= 0) {
        const g = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: we(t, J, 1)
              }]
            }]
          })
        });
        if (!g.ok)
          throw new Error(`API request failed: ${g.status}`);
        const p = (o = (l = (i = (a = (n = (await g.json()).candidates) == null ? void 0 : n[0]) == null ? void 0 : a.content) == null ? void 0 : i.parts) == null ? void 0 : l[0]) == null ? void 0 : o.text;
        if (!p)
          throw new Error("No content received from API");
        const S = p.match(/\{[\s\S]*\}/);
        if (!S)
          throw new Error("No valid JSON found in response");
        const I = JSON.parse(S[0]);
        if (!I.podcast_names || !Array.isArray(I.podcast_names))
          throw new Error("Invalid response format");
        const oe = [...x, ...I.podcast_names].slice(0, 5);
        v(oe), b(oe);
      } else {
        const g = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: we(t, J, w)
              }]
            }]
          })
        });
        if (!g.ok)
          throw new Error(`API request failed: ${g.status}`);
        const p = (k = (u = (m = (c = (r = (await g.json()).candidates) == null ? void 0 : r[0]) == null ? void 0 : c.content) == null ? void 0 : m.parts) == null ? void 0 : u[0]) == null ? void 0 : k.text;
        if (!p)
          throw new Error("No content received from API");
        const S = p.match(/\{[\s\S]*\}/);
        if (!S)
          throw new Error("No valid JSON found in response");
        const I = JSON.parse(S[0]);
        if (!I.podcast_names || !Array.isArray(I.podcast_names))
          throw new Error("Invalid response format");
        const oe = [...x, ...I.podcast_names];
        v(oe), b(oe);
      }
      C((g) => g.filter((R) => {
        const p = f[R.index];
        return x.some((S) => S.name === (p == null ? void 0 : p.name));
      })), C((g) => g.map((R) => {
        const p = f[R.index], S = x.findIndex((I) => I.name === (p == null ? void 0 : p.name));
        return S >= 0 ? { ...R, index: S } : R;
      }).filter((R) => R.index >= 0));
    } catch (x) {
      console.error("Error generating refined names:", x), D(x instanceof Error ? x.message : "Failed to generate refined names. Please try again.");
    } finally {
      z(!1), W(!1);
    }
  }, Re = (t) => {
    t.preventDefault(), ye();
  }, Ee = (t) => {
    t.key === "Enter" && !t.shiftKey && (t.preventDefault(), ye());
  };
  return /* @__PURE__ */ e.jsx("div", { className: `podcast-name-generator ${$}`, style: G, children: /* @__PURE__ */ e.jsxs("div", { className: "generator-container", children: [
    /* @__PURE__ */ e.jsxs("div", { className: "header-section", children: [
      /* @__PURE__ */ e.jsx("h1", { className: "main-title", children: "Free Podcast Name Generator" }),
      /* @__PURE__ */ e.jsx("h2", { className: "main-subtitle", children: "Create the Perfect Name for Your Podcast in Seconds" })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: "benefits-section", children: [
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "100% Free Forever" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "No Sign-up Required" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "Instant Results" })
      ] })
    ] }),
    P && /* @__PURE__ */ e.jsx("div", { className: "limit-reached-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "limit-content", children: [
      /* @__PURE__ */ e.jsx("span", { className: "limit-icon", children: "⚠️" }),
      /* @__PURE__ */ e.jsx("div", { className: "limit-text", children: /* @__PURE__ */ e.jsx("p", { children: "You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below." }) })
    ] }) }),
    f.length === 0 && /* @__PURE__ */ e.jsx("div", { className: "initial-input-section", children: /* @__PURE__ */ e.jsx("form", { onSubmit: Re, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
      /* @__PURE__ */ e.jsx(
        "textarea",
        {
          value: j,
          onChange: (t) => U(t.target.value),
          onKeyPress: Ee,
          placeholder: "Describe what your podcast is about",
          className: "input-field",
          rows: 3,
          disabled: T
        }
      ),
      /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
        /* @__PURE__ */ e.jsx(
          "button",
          {
            type: "submit",
            disabled: T || !j.trim() || P,
            className: `generate-button ${P ? "disabled" : ""}`,
            children: T ? "Generating..." : P ? "Daily Limit Reached" : "Generate Names"
          }
        ),
        /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
          ] }),
          /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
            /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star star-partial", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
            ] }),
            /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
          ] })
        ] })
      ] })
    ] }) }) }),
    X && /* @__PURE__ */ e.jsxs("div", { className: "error-message", children: [
      /* @__PURE__ */ e.jsx("span", { className: "error-icon", children: "⚠️" }),
      X
    ] }),
    B && /* @__PURE__ */ e.jsxs("div", { className: "success-message", children: [
      /* @__PURE__ */ e.jsx("span", { className: "success-icon", children: "✨" }),
      B
    ] }),
    T && /* @__PURE__ */ e.jsxs("div", { className: "loading-container", children: [
      /* @__PURE__ */ e.jsx("div", { className: "loading-spinner" }),
      /* @__PURE__ */ e.jsx("p", { children: de ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
    ] }),
    f.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "results-container", children: [
      O.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "favorites-section", children: [
        /* @__PURE__ */ e.jsxs("div", { className: "favorites-header", children: [
          /* @__PURE__ */ e.jsxs("h3", { children: [
            "🏆 Your Winning Podcast Names (",
            O.length,
            ")"
          ] }),
          /* @__PURE__ */ e.jsx("p", { className: "favorites-subtitle", children: "Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!" })
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "favorites-grid", children: O.map((t, n) => /* @__PURE__ */ e.jsxs("div", { className: "favorite-card", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "favorite-content", children: [
            /* @__PURE__ */ e.jsx("h4", { className: "favorite-name", children: t.name }),
            /* @__PURE__ */ e.jsx("p", { className: "favorite-description", children: t.description }),
            t.suggestedDomain && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
              /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
              /* @__PURE__ */ e.jsx("span", { className: "domain-name", children: t.suggestedDomain }),
              /* @__PURE__ */ e.jsx("span", { className: `domain-status ${t.domainStatus}`, children: t.domainStatus === "available" ? "✅ Available" : t.domainStatus === "taken" ? "❌ Taken" : t.domainStatus === "error" ? "⚠️ Check manually" : "🔍 Checking..." })
            ] })
          ] }),
          /* @__PURE__ */ e.jsx("div", { className: "favorite-actions", children: /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: () => ke(t.name, -1),
              className: "copy-button small",
              title: "Copy to clipboard",
              children: "📋 Copy"
            }
          ) })
        ] }, `fav-${n}`)) })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "input-section-simple", children: [
        /* @__PURE__ */ e.jsx("div", { className: "input-help-message-simple", children: /* @__PURE__ */ e.jsxs("p", { className: "input-sub-description", children: [
          "💡 Want different suggestions? Update your description below - ",
          /* @__PURE__ */ e.jsx("strong", { children: "your favorites will stay safe!" })
        ] }) }),
        /* @__PURE__ */ e.jsx("form", { onSubmit: Re, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
          /* @__PURE__ */ e.jsx(
            "textarea",
            {
              value: j,
              onChange: (t) => U(t.target.value),
              onKeyPress: Ee,
              placeholder: "Describe what your podcast is about",
              className: "input-field",
              rows: 3,
              disabled: T
            }
          ),
          /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
            /* @__PURE__ */ e.jsx(
              "button",
              {
                type: "submit",
                disabled: T || !j.trim() || P,
                className: `generate-button ${P ? "disabled" : ""}`,
                children: T ? "Generating..." : P ? "Daily Limit Reached" : "Generate Names"
              }
            ),
            /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
              /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
              ] }),
              /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
                /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star star-partial", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
                ] }),
                /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
              ] })
            ] })
          ] })
        ] }) })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "suggestions-section", children: [
        /* @__PURE__ */ e.jsx("div", { className: "suggestions-header", children: /* @__PURE__ */ e.jsx("h3", { children: "🎯 Current Suggestions" }) }),
        /* @__PURE__ */ e.jsx("div", { className: "onboarding-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "onboarding-content", children: [
          /* @__PURE__ */ e.jsx("span", { className: "onboarding-icon", children: "💡" }),
          /* @__PURE__ */ e.jsxs("div", { className: "onboarding-text", children: [
            /* @__PURE__ */ e.jsx("strong", { children: "Smart AI Learning:" }),
            " The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."
          ] })
        ] }) }),
        /* @__PURE__ */ e.jsx("div", { className: "results-grid", children: f.map((t, n) => {
          const a = Z.find((c) => c.index === n), i = (a == null ? void 0 : a.liked) === !0, l = (a == null ? void 0 : a.liked) === !1, o = Ne.has(n), r = ee.has(n);
          return /* @__PURE__ */ e.jsxs(
            "div",
            {
              className: `result-card ${i ? "liked" : ""} ${l ? "disliked" : ""} ${o ? "pending" : ""} ${ue.has(n) ? "flying-to-favorites" : ""}`,
              style: {
                opacity: o ? 0.6 : 1,
                pointerEvents: o ? "none" : "auto"
              },
              children: [
                /* @__PURE__ */ e.jsxs("div", { className: "result-header", children: [
                  /* @__PURE__ */ e.jsx("h4", { className: "result-name", children: o ? i ? "Generating new suggestion..." : "Generating better suggestion..." : t.name }),
                  /* @__PURE__ */ e.jsxs("div", { className: "result-actions", children: [
                    /* @__PURE__ */ e.jsxs("div", { className: "feedback-buttons", children: [
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => Se(n, !0),
                          className: `feedback-button like-button ${i ? "active" : ""}`,
                          title: "I like this name",
                          disabled: o,
                          children: "👍"
                        }
                      ),
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => Se(n, !1),
                          className: `feedback-button dislike-button ${l ? "active" : ""} ${o ? "loading" : ""}`,
                          title: o ? "Generating replacement..." : "I don't like this name",
                          disabled: o,
                          children: o ? "🔄" : "👎"
                        }
                      )
                    ] }),
                    /* @__PURE__ */ e.jsx(
                      "button",
                      {
                        onClick: () => ke(t.name, n),
                        className: "copy-button",
                        title: "Copy podcast name",
                        disabled: o,
                        children: H === n ? "✓ Copied!" : "📋 Copy"
                      }
                    )
                  ] })
                ] }),
                /* @__PURE__ */ e.jsx("p", { className: "result-description", children: o ? i ? "Added to favorites! Generating a new suggestion..." : "Creating a better suggestion based on your preferences..." : t.description }),
                t.suggestedDomain && !r && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
                  /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
                  /* @__PURE__ */ e.jsxs("code", { className: "domain-text", children: [
                    t.suggestedDomain,
                    ".com"
                  ] }),
                  /* @__PURE__ */ e.jsxs("span", { className: `domain-status ${t.domainStatus}`, children: [
                    (t.domainStatus === "checking" || ee.has(n)) && "⏳ Checking...",
                    t.domainStatus === "available" && "✅ Available",
                    t.domainStatus === "taken" && "❌ Taken",
                    t.domainStatus === "error" && "⚠️ Check manually"
                  ] })
                ] })
              ]
            },
            n
          );
        }) })
      ] }),
      ge && !T && /* @__PURE__ */ e.jsxs("div", { className: "refinement-section", children: [
        /* @__PURE__ */ e.jsx("div", { className: "refinement-info", children: /* @__PURE__ */ e.jsx("p", { children: "💡 I'll keep your liked names and replace the disliked ones with better suggestions!" }) }),
        /* @__PURE__ */ e.jsx(
          "button",
          {
            onClick: $e,
            className: "refinement-button",
            disabled: T,
            children: de ? "🔄 Refining..." : "🎯 Replace Disliked Names"
          }
        )
      ] })
    ] })
  ] }) });
};
export {
  Ie as default
};

import Ce, { useState as k } from "react";
var xe = { exports: {} }, ie = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Te;
function De() {
  if (Te) return ie;
  Te = 1;
  var O = Symbol.for("react.transitional.element"), H = Symbol.for("react.fragment");
  function y(U, f, v) {
    var D = null;
    if (v !== void 0 && (D = "" + v), f.key !== void 0 && (D = "" + f.key), "key" in f) {
      v = {};
      for (var W in f)
        W !== "key" && (v[W] = f[W]);
    } else v = f;
    return f = v.ref, {
      $$typeof: O,
      type: U,
      key: D,
      ref: f !== void 0 ? f : null,
      props: v
    };
  }
  return ie.Fragment = H, ie.jsx = y, ie.jsxs = y, ie;
}
var le = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Ae;
function Le() {
  return Ae || (Ae = 1, process.env.NODE_ENV !== "production" && function() {
    function O(s) {
      if (s == null) return null;
      if (typeof s == "function")
        return s.$$typeof === Ne ? null : s.displayName || s.name || null;
      if (typeof s == "string") return s;
      switch (s) {
        case P:
          return "Fragment";
        case ce:
          return "Profiler";
        case K:
          return "StrictMode";
        case G:
          return "Suspense";
        case ve:
          return "SuspenseList";
        case B:
          return "Activity";
      }
      if (typeof s == "object")
        switch (typeof s.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), s.$$typeof) {
          case ee:
            return "Portal";
          case pe:
            return (s.displayName || "Context") + ".Provider";
          case ge:
            return (s._context.displayName || "Context") + ".Consumer";
          case de:
            var d = s.render;
            return s = s.displayName, s || (s = d.displayName || d.name || "", s = s !== "" ? "ForwardRef(" + s + ")" : "ForwardRef"), s;
          case me:
            return d = s.displayName || null, d !== null ? d : O(s.type) || "Memo";
          case te:
            d = s._payload, s = s._init;
            try {
              return O(s(d));
            } catch {
            }
        }
      return null;
    }
    function H(s) {
      return "" + s;
    }
    function y(s) {
      try {
        H(s);
        var d = !1;
      } catch {
        d = !0;
      }
      if (d) {
        d = console;
        var h = d.error, N = typeof Symbol == "function" && Symbol.toStringTag && s[Symbol.toStringTag] || s.constructor.name || "Object";
        return h.call(
          d,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          N
        ), H(s);
      }
    }
    function U(s) {
      if (s === P) return "<>";
      if (typeof s == "object" && s !== null && s.$$typeof === te)
        return "<...>";
      try {
        var d = O(s);
        return d ? "<" + d + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function f() {
      var s = q.A;
      return s === null ? null : s.getOwner();
    }
    function v() {
      return Error("react-stack-top-frame");
    }
    function D(s) {
      if (ue.call(s, "key")) {
        var d = Object.getOwnPropertyDescriptor(s, "key").get;
        if (d && d.isReactWarning) return !1;
      }
      return s.key !== void 0;
    }
    function W(s, d) {
      function h() {
        se || (se = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          d
        ));
      }
      h.isReactWarning = !0, Object.defineProperty(s, "key", {
        get: h,
        configurable: !0
      });
    }
    function C() {
      var s = O(this.type);
      return ne[s] || (ne[s] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), s = this.props.ref, s !== void 0 ? s : null;
    }
    function z(s, d, h, N, $, T, I, X) {
      return h = T.ref, s = {
        $$typeof: Z,
        type: s,
        key: d,
        props: T,
        _owner: $
      }, (h !== void 0 ? h : null) !== null ? Object.defineProperty(s, "ref", {
        enumerable: !1,
        get: C
      }) : Object.defineProperty(s, "ref", { enumerable: !1, value: null }), s._store = {}, Object.defineProperty(s._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(s, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(s, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: I
      }), Object.defineProperty(s, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: X
      }), Object.freeze && (Object.freeze(s.props), Object.freeze(s)), s;
    }
    function Q(s, d, h, N, $, T, I, X) {
      var b = d.children;
      if (b !== void 0)
        if (N)
          if (he(b)) {
            for (N = 0; N < b.length; N++)
              L(b[N]);
            Object.freeze && Object.freeze(b);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else L(b);
      if (ue.call(d, "key")) {
        b = O(s);
        var F = Object.keys(d).filter(function(fe) {
          return fe !== "key";
        });
        N = 0 < F.length ? "{key: someKey, " + F.join(": ..., ") + ": ...}" : "{key: someKey}", _[b + N] || (F = 0 < F.length ? "{" + F.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          N,
          b,
          F,
          b
        ), _[b + N] = !0);
      }
      if (b = null, h !== void 0 && (y(h), b = "" + h), D(d) && (y(d.key), b = "" + d.key), "key" in d) {
        h = {};
        for (var re in d)
          re !== "key" && (h[re] = d[re]);
      } else h = d;
      return b && W(
        h,
        typeof s == "function" ? s.displayName || s.name || "Unknown" : s
      ), z(
        s,
        b,
        T,
        $,
        f(),
        h,
        I,
        X
      );
    }
    function L(s) {
      typeof s == "object" && s !== null && s.$$typeof === Z && s._store && (s._store.validated = 1);
    }
    var J = Ce, Z = Symbol.for("react.transitional.element"), ee = Symbol.for("react.portal"), P = Symbol.for("react.fragment"), K = Symbol.for("react.strict_mode"), ce = Symbol.for("react.profiler"), ge = Symbol.for("react.consumer"), pe = Symbol.for("react.context"), de = Symbol.for("react.forward_ref"), G = Symbol.for("react.suspense"), ve = Symbol.for("react.suspense_list"), me = Symbol.for("react.memo"), te = Symbol.for("react.lazy"), B = Symbol.for("react.activity"), Ne = Symbol.for("react.client.reference"), q = J.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, ue = Object.prototype.hasOwnProperty, he = Array.isArray, V = console.createTask ? console.createTask : function() {
      return null;
    };
    J = {
      "react-stack-bottom-frame": function(s) {
        return s();
      }
    };
    var se, ne = {}, be = J["react-stack-bottom-frame"].bind(
      J,
      v
    )(), ae = V(U(v)), _ = {};
    le.Fragment = P, le.jsx = function(s, d, h, N, $) {
      var T = 1e4 > q.recentlyCreatedOwnerStacks++;
      return Q(
        s,
        d,
        h,
        !1,
        N,
        $,
        T ? Error("react-stack-top-frame") : be,
        T ? V(U(s)) : ae
      );
    }, le.jsxs = function(s, d, h, N, $) {
      var T = 1e4 > q.recentlyCreatedOwnerStacks++;
      return Q(
        s,
        d,
        h,
        !0,
        N,
        $,
        T ? Error("react-stack-top-frame") : be,
        T ? V(U(s)) : ae
      );
    };
  }()), le;
}
process.env.NODE_ENV === "production" ? xe.exports = De() : xe.exports = Le();
var e = xe.exports;
const Me = ({
  className: O = "",
  style: H = {}
}) => {
  const [y, U] = k(""), [f, v] = k([]), [D, W] = k([]), [C, z] = k(!1), [Q, L] = k(null), [J, Z] = k(null), [ee, P] = k([]), [K, ce] = k({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    }
  }), [ge, pe] = k(!1), [de, G] = k(!1), [ve, me] = k(!1), [te, B] = k(/* @__PURE__ */ new Set()), [Ne, q] = k(/* @__PURE__ */ new Set()), [ue, he] = k(/* @__PURE__ */ new Set()), [V, se] = k(null), ne = (t) => {
    const a = window.scrollY, n = document.documentElement.scrollHeight, i = window.innerHeight, l = n - a - i;
    t(), requestAnimationFrame(() => {
      const o = document.documentElement.scrollHeight, r = window.innerHeight, c = o - l - r;
      Math.abs(o - n) > 5 ? (window.scrollTo(0, Math.max(0, c)), console.log("📏 Scroll adjusted for height change:", {
        heightChange: o - n,
        oldScrollY: a,
        newScrollY: Math.max(0, c)
      })) : window.scrollTo(0, a);
    });
  }, [be, ae] = k(0), [_, s] = k(!1), d = 100, h = () => {
    const t = document.createElement("canvas"), a = t.getContext("2d");
    a.textBaseline = "top", a.font = "14px Arial", a.fillText("Usage tracking", 2, 2);
    const n = t.toDataURL(), i = navigator.userAgent, l = navigator.language, o = Intl.DateTimeFormat().resolvedOptions().timeZone, r = n + i + l + o;
    let c = 0;
    for (let m = 0; m < r.length; m++) {
      const u = r.charCodeAt(m);
      c = (c << 5) - c + u, c = c & c;
    }
    return `podcast_usage_${Math.abs(c)}`;
  }, N = () => {
    const t = h(), a = (/* @__PURE__ */ new Date()).toDateString(), n = `${t}_${a}`, i = localStorage.getItem(n), l = i ? parseInt(i, 10) : 0;
    return ae(l), l >= d ? (s(!0), !1) : !0;
  }, $ = (t = 1) => {
    const a = h(), n = (/* @__PURE__ */ new Date()).toDateString(), i = `${a}_${n}`, l = localStorage.getItem(i), r = (l ? parseInt(l, 10) : 0) + t;
    localStorage.setItem(i, r.toString()), ae(r), r >= d && s(!0);
  };
  Ce.useEffect(() => {
    N();
  }, []);
  const T = (t) => {
    let a = t.toLowerCase().replace(/[^a-z0-9\s]/g, "").replace(/\s+/g, " ").replace(/^the\s+/, "").trim();
    const n = ["the", "and", "for", "with", "from", "show", "podcast", "cast", "of", "in", "on", "at", "to", "a", "an"], i = a.split(" "), l = i.filter(
      (r) => r.length > 2 && !n.includes(r)
    );
    if (l.length === 1) {
      const r = l[0];
      return r.length >= 6 && r.length <= 15 ? r : r.length < 6 ? r + "pod" : I(r);
    }
    if (l.length >= 2) {
      const r = l[0], c = l[1], m = r + c;
      if (m.length >= 6 && m.length <= 15)
        return m;
      const u = I(r), j = I(c), w = u + j;
      if (w.length >= 6 && w.length <= 15)
        return w;
      if (w.length > 15)
        return u + "-" + j;
    }
    if (l.length > 0) {
      const r = I(l[0]), c = ["cast", "pod", "show", "talk"];
      for (const m of c) {
        const u = r + m;
        if (u.length >= 6 && u.length <= 15)
          return u;
      }
      if (r.length >= 6 && r.length <= 15)
        return r;
    }
    const o = i[0];
    return o && o.length >= 3 ? I(o) + "pod" : "podcast" + Math.random().toString(36).substring(2, 5);
  }, I = (t) => {
    if (t.length <= 8) return t;
    const a = {
      // Business & Professional
      business: "biz",
      entrepreneur: "entre",
      marketing: "market",
      finance: "fin",
      startup: "start",
      leadership: "lead",
      strategy: "strat",
      success: "win",
      growth: "grow",
      innovation: "innov",
      management: "manage",
      // Technology
      technology: "tech",
      development: "dev",
      digital: "digi",
      software: "soft",
      coding: "code",
      programming: "prog",
      // Content & Media
      stories: "story",
      journey: "path",
      adventure: "quest",
      creative: "create",
      entertainment: "fun",
      education: "learn",
      knowledge: "know",
      wisdom: "wise",
      // Lifestyle & Personal
      lifestyle: "life",
      wellness: "well",
      fitness: "fit",
      health: "heal",
      mindset: "mind",
      motivation: "motive",
      inspiration: "inspire",
      // Geographic & Cultural
      american: "usa",
      european: "euro",
      international: "global",
      community: "comm",
      culture: "cult",
      society: "social"
    };
    if (a[t])
      return a[t];
    const n = ["super", "mega", "ultra", "micro", "mini", "multi"];
    for (const l of n)
      if (t.startsWith(l) && t.length > l.length + 3) {
        const o = t.substring(l.length);
        if (o.length <= 8)
          return o;
      }
    const i = ["ing", "tion", "sion", "ness", "ment", "able", "ible"];
    for (const l of i)
      if (t.endsWith(l) && t.length > l.length + 4) {
        const o = t.substring(0, t.length - l.length);
        if (o.length >= 4 && o.length <= 8)
          return o;
      }
    return t.length > 8 ? t.substring(0, 8) : t;
  }, X = async (t) => {
    try {
      const a = await fetch(`https://dns.google/resolve?name=${t}&type=A`, {
        method: "GET",
        headers: {
          Accept: "application/json"
        }
      });
      if (!a.ok)
        return "error";
      const n = await a.json();
      return n.Answer && n.Answer.length > 0 ? "taken" : "available";
    } catch (a) {
      return console.warn("Domain check failed:", a), "error";
    }
  }, b = async (t) => {
    const a = [...t];
    for (let n = 0; n < a.length; n++) {
      const i = T(a[n].name);
      a[n].suggestedDomain = i, a[n].domainStatus = "checking", B((l) => /* @__PURE__ */ new Set([...l, n]));
    }
    v(a);
    for (let n = 0; n < a.length; n++) {
      const i = `${a[n].suggestedDomain}.com`;
      try {
        const l = await X(i);
        v((o) => {
          const r = [...o];
          return r[n] && (r[n].domainStatus = l), r;
        });
      } catch {
        v((o) => {
          const r = [...o];
          return r[n] && (r[n].domainStatus = "error"), r;
        });
      } finally {
        B((l) => {
          const o = new Set(l);
          return o.delete(n), o;
        });
      }
    }
  }, F = (t, a) => {
    if (t.length === 0) return null;
    const n = t.map((r) => r.name.split(" ").length), i = a.map((r) => r.name.split(" ").length), l = n.reduce((r, c) => r + c, 0) / n.length, o = i.length > 0 ? i.reduce((r, c) => r + c, 0) / i.length : 0;
    return l <= 2 && o > 2 ? "short" : l <= 4 && (o <= 2 || o > 4) ? "medium" : l > 4 && o <= 4 ? "long" : l <= 2 ? "short" : l <= 4 ? "medium" : "long";
  }, re = (t, a) => {
    if (t.length === 0) return null;
    const n = t.map((l) => l.description.toLowerCase()).join(" "), i = a.map((l) => l.description.toLowerCase()).join(" ");
    return (n.includes("professional") || n.includes("business")) && !i.includes("professional") && !i.includes("business") ? "professional" : (n.includes("creative") || n.includes("unique")) && !i.includes("creative") && !i.includes("unique") ? "creative" : (n.includes("fun") || n.includes("playful")) && !i.includes("fun") && !i.includes("playful") ? "playful" : "descriptive";
  }, fe = (t) => {
    const a = [];
    t.forEach((i) => {
      const l = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], o = i.name.toLowerCase().split(/\s+/).filter(
        (r) => r.length > 2 && !l.includes(r)
      );
      a.push(...o);
    });
    const n = {};
    return a.forEach((i) => n[i] = (n[i] || 0) + 1), Object.entries(n).sort(([, i], [, l]) => l - i).slice(0, 5).map(([i]) => i);
  }, je = (t) => {
    const a = t.filter((i) => i.liked === !0), n = t.filter((i) => i.liked === !1);
    return {
      preferredLength: F(a, n),
      preferredStyle: re(a, n),
      likedKeywords: fe(a),
      dislikedKeywords: fe(n),
      preferredStructure: null
      // Simplified for now
    };
  }, Pe = (t, a) => {
    const n = [
      ...a.likedNames.map((r) => r.name.toLowerCase()),
      ...a.dislikedNames.map((r) => r.name.toLowerCase()),
      ...f.map((r) => r.name.toLowerCase())
    ], i = `Create 4 unique, high-converting podcast names for: ${t}`;
    let l = `

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;
    n.length > 0 && (l += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${n.map((r) => `- ${r}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);
    let o = "";
    if (a.patterns.preferredLength && (o += `
Focus on ${{
      short: "1-2 words (punchy and memorable)",
      medium: "2-3 words (balanced and brandable)",
      long: "3-4 words (descriptive but still catchy)"
    }[a.patterns.preferredLength]}. `), a.patterns.preferredStyle && (o += `Use ${{
      descriptive: "clear, straightforward names that explain the content",
      creative: "imaginative, metaphorical, or playful names",
      professional: "authoritative, business-focused names",
      playful: "fun, energetic, engaging names"
    }[a.patterns.preferredStyle] || a.patterns.preferredStyle}. `), a.patterns.likedKeywords.length > 0 && (o += `
Incorporate themes similar to: ${a.patterns.likedKeywords.join(", ")}. `), a.patterns.dislikedKeywords.length > 0 && (o += `
Avoid themes like: ${a.patterns.dislikedKeywords.join(", ")}. `), a.likedNames.length > 0) {
      const r = a.likedNames.slice(-2).map((c) => c.name).join('", "');
      o += `
Generate names with similar appeal to: "${r}" (but completely different concepts). `;
    }
    return `${i}${l}${o}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;
  }, we = (t, a, n = 1) => {
    const i = [
      ...a.likedNames.map((c) => c.name.toLowerCase()),
      ...a.dislikedNames.map((c) => c.name.toLowerCase()),
      ...f.map((c) => c.name.toLowerCase())
    ], l = `Create ${n} unique, high-converting podcast name${n > 1 ? "s" : ""} for: ${t}`;
    let o = `

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;
    i.length > 0 && (o += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${i.map((c) => `- ${c}`).join(`
`)}
Do not create names similar to any of the above.`);
    let r = "";
    return a.patterns.likedKeywords.length > 0 && (r += `
Incorporate themes similar to: ${a.patterns.likedKeywords.join(", ")}. `), a.patterns.dislikedKeywords.length > 0 && (r += `
Avoid themes like: ${a.patterns.dislikedKeywords.join(", ")}. `), `${l}${o}${r}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${n > 1 ? ', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}' : ""}]}`;
  }, ye = async (t = !1) => {
    if (!y.trim()) {
      L("Please describe what your podcast is about");
      return;
    }
    if (!N()) {
      L(null);
      return;
    }
    z(!0), L(null), v([]), t ? G(!0) : (P([]), pe(!1), G(!1), me(!1));
    try {
      const a = t ? Pe(y, K) : `Create 4 unique, high-converting podcast names for: ${y}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`, n = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: a
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!n.ok)
        throw new Error(`API request failed: ${n.status} ${n.statusText}`);
      const i = await n.json();
      if (!i.candidates || !i.candidates[0] || !i.candidates[0].content)
        throw new Error("Invalid response format from API");
      const o = i.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!o)
        throw new Error("No valid JSON found in API response");
      const r = JSON.parse(o[0]);
      if (!r.podcast_names || !Array.isArray(r.podcast_names))
        throw new Error("Invalid response structure");
      v(r.podcast_names), $(4), b(r.podcast_names);
      const c = r.podcast_names.map((m, u) => ({
        name: m.name,
        description: m.description,
        liked: null,
        timestamp: Date.now(),
        index: u
      }));
      P(c);
    } catch (a) {
      console.error("Error generating podcast names:", a), L(a instanceof Error ? a.message : "An unexpected error occurred");
    } finally {
      z(!1), G(!1);
    }
  }, ke = async (t, a) => {
    try {
      await navigator.clipboard.writeText(t), Z(a), setTimeout(() => Z(null), 2e3);
    } catch (n) {
      console.error("Failed to copy text:", n);
    }
  }, Se = async (t, a) => {
    const n = f[t];
    if (n) {
      if (a) {
        const i = window.scrollY, l = document.documentElement.scrollHeight, o = window.innerHeight, r = l - i - o;
        console.log("📏 Before adding favorite:", {
          scrollY: i,
          documentHeight: l,
          scrollFromBottom: r
        }), he((c) => /* @__PURE__ */ new Set([...c, t])), ne(() => {
          se(`"${n.name}" added to favorites!`);
        }), setTimeout(() => {
          ne(() => {
            se(null);
          });
        }, 2e3), setTimeout(() => {
          const c = document.querySelector(".favorites-section"), m = c ? c.scrollHeight : 0;
          W((u) => u.find((j) => j.name === n.name) ? u : [...u, n]), requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              const u = c ? c.scrollHeight : 0, j = u - m, Y = document.documentElement.scrollHeight - l, S = j > 0 ? j : Y, A = i + S;
              console.log("📏 After adding favorite:", {
                beforeFavoritesHeight: m,
                afterFavoritesHeight: u,
                actualHeightIncrease: j,
                documentHeightIncrease: Y,
                heightIncrease: S,
                beforeScrollY: i,
                newScrollY: A,
                cardName: n.name
              }), window.scrollTo(0, Math.max(0, A));
            });
          });
        }, 100), setTimeout(() => {
          he((c) => {
            const m = new Set(c);
            return m.delete(t), m;
          });
        }, 700), ce((c) => {
          const m = { ...c };
          return m.dislikedNames = m.dislikedNames.filter((u) => u.name !== n.name), m.likedNames.find((u) => u.name === n.name) || m.likedNames.push({
            name: n.name,
            description: n.description,
            liked: !0,
            timestamp: Date.now(),
            index: t
          }), m.patterns = je([...m.likedNames, ...m.dislikedNames]), m;
        }), q((c) => /* @__PURE__ */ new Set([...c, t]));
      } else {
        const i = window.scrollY;
        console.log("👎 Disliking item, maintaining scroll at:", i), q((l) => /* @__PURE__ */ new Set([...l, t])), ce((l) => {
          const o = { ...l };
          return o.likedNames = o.likedNames.filter((r) => r.name !== n.name), o.dislikedNames.find((r) => r.name === n.name) || o.dislikedNames.push({
            name: n.name,
            description: n.description,
            liked: !1,
            timestamp: Date.now(),
            index: t
          }), o.patterns = je([...o.likedNames, ...o.dislikedNames]), o;
        }), setTimeout(() => {
          window.scrollTo(0, i);
        }, 0);
      }
      y.trim() && _e(t), ve || me(!0);
    }
  }, _e = async (t) => {
    var a, n, i, l, o, r;
    if (N()) {
      console.log(`🔄 Starting replacement generation for index ${t}`);
      try {
        const c = we(y, K, 1);
        console.log(`📝 Generated single name prompt for index ${t}:`, c.substring(0, 100) + "..."), console.log(`🌐 Making API call for index ${t}...`);
        const m = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: c
              }]
            }]
          })
        });
        if (console.log(`📡 API response status for index ${t}:`, m.status), !m.ok)
          throw new Error(`Failed to generate replacement suggestion: ${m.status} ${m.statusText}`);
        const u = await m.json();
        console.log(`📦 API response data for index ${t}:`, u);
        const j = (o = (l = (i = (n = (a = u.candidates) == null ? void 0 : a[0]) == null ? void 0 : n.content) == null ? void 0 : i.parts) == null ? void 0 : l[0]) == null ? void 0 : o.text;
        if (!j)
          throw new Error("No content in API response");
        console.log(`📄 API content for index ${t}:`, j.substring(0, 200) + "...");
        const w = j.match(/\{[\s\S]*\}/);
        if (!w)
          throw console.error(`❌ No valid JSON found in response for index ${t}:`, j), new Error("No valid JSON found in response");
        const Y = JSON.parse(w[0]);
        console.log(`🔍 Parsed response for index ${t}:`, Y);
        const S = (r = Y.podcast_names) == null ? void 0 : r[0];
        if (S) {
          if (console.log(`✅ New name generated for index ${t}:`, S), v((A) => {
            const x = [...A];
            return x[t] = {
              name: S.name,
              description: S.description,
              suggestedDomain: S.suggestedDomain,
              domainStatus: "checking"
            }, console.log(`🔄 Updated results for index ${t}:`, x[t]), x;
          }), q((A) => {
            const x = new Set(A);
            return x.delete(t), console.log(`🗑️ Removed index ${t} from pending replacements. Remaining:`, Array.from(x)), x;
          }), P((A) => {
            const x = A.filter((g) => g.index !== t);
            return console.log(`🧹 Cleared feedback for index ${t}. Remaining feedback:`, x), x;
          }), S.suggestedDomain) {
            console.log(`🌐 Checking domain availability for ${S.suggestedDomain}...`), B((x) => /* @__PURE__ */ new Set([...x, t]));
            const A = await X(S.suggestedDomain);
            console.log(`🏷️ Domain status for ${S.suggestedDomain}:`, A), v((x) => {
              const g = [...x];
              return g[t] && (g[t].domainStatus = A), g;
            }), B((x) => {
              const g = new Set(x);
              return g.delete(t), g;
            });
          }
          console.log(`🎉 Successfully completed replacement for index ${t}`);
        } else
          throw console.error(`❌ No new name found in parsed response for index ${t}:`, Y), new Error("No new name found in API response");
      } catch (c) {
        console.error(`❌ Error generating replacement suggestion for index ${t}:`, c), q((m) => {
          const u = new Set(m);
          return u.delete(t), console.log(`🗑️ Removed index ${t} from pending on error. Remaining:`, Array.from(u)), u;
        }), P((m) => {
          const u = m.filter((j) => j.index !== t);
          return console.log(`🧹 Cleared feedback for index ${t} on error. Remaining:`, u), u;
        });
      }
    }
  }, $e = () => {
    y.trim() && Oe(y);
  }, Oe = async (t) => {
    var a, n, i, l, o, r, c, m, u, j;
    z(!0), L(""), G(!0);
    try {
      const w = f.filter((g, E) => {
        const p = ee.find((R) => R.index === E);
        return (p == null ? void 0 : p.liked) === !0;
      }), Y = f.filter((g, E) => {
        const p = ee.find((R) => R.index === E);
        return (p == null ? void 0 : p.liked) === !1;
      }).length, S = Math.max(1, Y), x = Math.min(5, w.length + S) - w.length;
      if (x <= 0) {
        const g = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: we(t, K, 1)
              }]
            }]
          })
        });
        if (!g.ok)
          throw new Error(`API request failed: ${g.status}`);
        const p = (o = (l = (i = (n = (a = (await g.json()).candidates) == null ? void 0 : a[0]) == null ? void 0 : n.content) == null ? void 0 : i.parts) == null ? void 0 : l[0]) == null ? void 0 : o.text;
        if (!p)
          throw new Error("No content received from API");
        const R = p.match(/\{[\s\S]*\}/);
        if (!R)
          throw new Error("No valid JSON found in response");
        const M = JSON.parse(R[0]);
        if (!M.podcast_names || !Array.isArray(M.podcast_names))
          throw new Error("Invalid response format");
        const oe = [...w, ...M.podcast_names].slice(0, 5);
        v(oe), b(oe);
      } else {
        const g = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: we(t, K, x)
              }]
            }]
          })
        });
        if (!g.ok)
          throw new Error(`API request failed: ${g.status}`);
        const p = (j = (u = (m = (c = (r = (await g.json()).candidates) == null ? void 0 : r[0]) == null ? void 0 : c.content) == null ? void 0 : m.parts) == null ? void 0 : u[0]) == null ? void 0 : j.text;
        if (!p)
          throw new Error("No content received from API");
        const R = p.match(/\{[\s\S]*\}/);
        if (!R)
          throw new Error("No valid JSON found in response");
        const M = JSON.parse(R[0]);
        if (!M.podcast_names || !Array.isArray(M.podcast_names))
          throw new Error("Invalid response format");
        const oe = [...w, ...M.podcast_names];
        v(oe), b(oe);
      }
      P((g) => g.filter((E) => {
        const p = f[E.index];
        return w.some((R) => R.name === (p == null ? void 0 : p.name));
      })), P((g) => g.map((E) => {
        const p = f[E.index], R = w.findIndex((M) => M.name === (p == null ? void 0 : p.name));
        return R >= 0 ? { ...E, index: R } : E;
      }).filter((E) => E.index >= 0));
    } catch (w) {
      console.error("Error generating refined names:", w), L(w instanceof Error ? w.message : "Failed to generate refined names. Please try again.");
    } finally {
      z(!1), G(!1);
    }
  }, Re = (t) => {
    t.preventDefault(), ye();
  }, Ee = (t) => {
    t.key === "Enter" && !t.shiftKey && (t.preventDefault(), ye());
  };
  return /* @__PURE__ */ e.jsx("div", { className: `podcast-name-generator ${O}`, style: H, children: /* @__PURE__ */ e.jsxs("div", { className: "generator-container", children: [
    /* @__PURE__ */ e.jsxs("div", { className: "header-section", children: [
      /* @__PURE__ */ e.jsx("h1", { className: "main-title", children: "Free Podcast Name Generator" }),
      /* @__PURE__ */ e.jsx("h2", { className: "main-subtitle", children: "Create the Perfect Name for Your Podcast in Seconds" })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: "benefits-section", children: [
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "100% Free Forever" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "No Sign-up Required" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "Instant Results" })
      ] })
    ] }),
    _ && /* @__PURE__ */ e.jsx("div", { className: "limit-reached-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "limit-content", children: [
      /* @__PURE__ */ e.jsx("span", { className: "limit-icon", children: "⚠️" }),
      /* @__PURE__ */ e.jsx("div", { className: "limit-text", children: /* @__PURE__ */ e.jsx("p", { children: "You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below." }) })
    ] }) }),
    f.length === 0 && /* @__PURE__ */ e.jsx("div", { className: "initial-input-section", children: /* @__PURE__ */ e.jsx("form", { onSubmit: Re, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
      /* @__PURE__ */ e.jsx(
        "textarea",
        {
          value: y,
          onChange: (t) => U(t.target.value),
          onKeyPress: Ee,
          placeholder: "Describe what your podcast is about",
          className: "input-field",
          rows: 3,
          disabled: C
        }
      ),
      /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
        /* @__PURE__ */ e.jsx(
          "button",
          {
            type: "submit",
            disabled: C || !y.trim() || _,
            className: `generate-button ${_ ? "disabled" : ""}`,
            children: C ? "Generating..." : _ ? "Daily Limit Reached" : "Generate Names"
          }
        ),
        /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
          ] }),
          /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
            /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star star-partial", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
            ] }),
            /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
          ] })
        ] })
      ] })
    ] }) }) }),
    Q && /* @__PURE__ */ e.jsxs("div", { className: "error-message", children: [
      /* @__PURE__ */ e.jsx("span", { className: "error-icon", children: "⚠️" }),
      Q
    ] }),
    V && /* @__PURE__ */ e.jsxs("div", { className: "success-message", children: [
      /* @__PURE__ */ e.jsx("span", { className: "success-icon", children: "✨" }),
      V
    ] }),
    C && /* @__PURE__ */ e.jsxs("div", { className: "loading-container", children: [
      /* @__PURE__ */ e.jsx("div", { className: "loading-spinner" }),
      /* @__PURE__ */ e.jsx("p", { children: de ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
    ] }),
    f.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "results-container", children: [
      D.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "favorites-section", children: [
        /* @__PURE__ */ e.jsxs("div", { className: "favorites-header", children: [
          /* @__PURE__ */ e.jsxs("h3", { children: [
            "🏆 Your Winning Podcast Names (",
            D.length,
            ")"
          ] }),
          /* @__PURE__ */ e.jsx("p", { className: "favorites-subtitle", children: "Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!" })
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "favorites-grid", children: D.map((t, a) => /* @__PURE__ */ e.jsxs("div", { className: "favorite-card", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "favorite-content", children: [
            /* @__PURE__ */ e.jsx("h4", { className: "favorite-name", children: t.name }),
            /* @__PURE__ */ e.jsx("p", { className: "favorite-description", children: t.description }),
            t.suggestedDomain && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
              /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
              /* @__PURE__ */ e.jsx("span", { className: "domain-name", children: t.suggestedDomain }),
              /* @__PURE__ */ e.jsx("span", { className: `domain-status ${t.domainStatus}`, children: t.domainStatus === "available" ? "✅ Available" : t.domainStatus === "taken" ? "❌ Taken" : t.domainStatus === "error" ? "⚠️ Check manually" : "🔍 Checking..." })
            ] })
          ] }),
          /* @__PURE__ */ e.jsx("div", { className: "favorite-actions", children: /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: () => ke(t.name, -1),
              className: "copy-button small",
              title: "Copy to clipboard",
              children: "📋 Copy"
            }
          ) })
        ] }, `fav-${a}`)) })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "input-section-simple", children: [
        /* @__PURE__ */ e.jsx("div", { className: "input-help-message-simple", children: /* @__PURE__ */ e.jsxs("p", { className: "input-sub-description", children: [
          "💡 Want different suggestions? Update your description below - ",
          /* @__PURE__ */ e.jsx("strong", { children: "your favorites will stay safe!" })
        ] }) }),
        /* @__PURE__ */ e.jsx("form", { onSubmit: Re, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
          /* @__PURE__ */ e.jsx(
            "textarea",
            {
              value: y,
              onChange: (t) => U(t.target.value),
              onKeyPress: Ee,
              placeholder: "Describe what your podcast is about",
              className: "input-field",
              rows: 3,
              disabled: C
            }
          ),
          /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
            /* @__PURE__ */ e.jsx(
              "button",
              {
                type: "submit",
                disabled: C || !y.trim() || _,
                className: `generate-button ${_ ? "disabled" : ""}`,
                children: C ? "Generating..." : _ ? "Daily Limit Reached" : "Generate Names"
              }
            ),
            /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
              /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
              ] }),
              /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
                /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star star-partial", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
                ] }),
                /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
              ] })
            ] })
          ] })
        ] }) })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "suggestions-section", children: [
        /* @__PURE__ */ e.jsx("div", { className: "suggestions-header", children: /* @__PURE__ */ e.jsx("h3", { children: "🎯 Current Suggestions" }) }),
        /* @__PURE__ */ e.jsx("div", { className: "onboarding-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "onboarding-content", children: [
          /* @__PURE__ */ e.jsx("span", { className: "onboarding-icon", children: "💡" }),
          /* @__PURE__ */ e.jsxs("div", { className: "onboarding-text", children: [
            /* @__PURE__ */ e.jsx("strong", { children: "Smart AI Learning:" }),
            " The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."
          ] })
        ] }) }),
        /* @__PURE__ */ e.jsx("div", { className: "results-grid", children: f.map((t, a) => {
          const n = ee.find((c) => c.index === a), i = (n == null ? void 0 : n.liked) === !0, l = (n == null ? void 0 : n.liked) === !1, o = Ne.has(a), r = te.has(a);
          return /* @__PURE__ */ e.jsxs(
            "div",
            {
              className: `result-card ${i ? "liked" : ""} ${l ? "disliked" : ""} ${o ? "pending" : ""} ${ue.has(a) ? "flying-to-favorites" : ""}`,
              style: {
                opacity: o ? 0.6 : 1,
                pointerEvents: o ? "none" : "auto"
              },
              children: [
                /* @__PURE__ */ e.jsxs("div", { className: "result-header", children: [
                  /* @__PURE__ */ e.jsx("h4", { className: "result-name", children: o ? i ? "Generating new suggestion..." : "Generating better suggestion..." : t.name }),
                  /* @__PURE__ */ e.jsxs("div", { className: "result-actions", children: [
                    /* @__PURE__ */ e.jsxs("div", { className: "feedback-buttons", children: [
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => Se(a, !0),
                          className: `feedback-button like-button ${i ? "active" : ""}`,
                          title: "I like this name",
                          disabled: o,
                          children: "👍"
                        }
                      ),
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => Se(a, !1),
                          className: `feedback-button dislike-button ${l ? "active" : ""} ${o ? "loading" : ""}`,
                          title: o ? "Generating replacement..." : "I don't like this name",
                          disabled: o,
                          children: o ? "🔄" : "👎"
                        }
                      )
                    ] }),
                    /* @__PURE__ */ e.jsx(
                      "button",
                      {
                        onClick: () => ke(t.name, a),
                        className: "copy-button",
                        title: "Copy podcast name",
                        disabled: o,
                        children: J === a ? "✓ Copied!" : "📋 Copy"
                      }
                    )
                  ] })
                ] }),
                /* @__PURE__ */ e.jsx("p", { className: "result-description", children: o ? i ? "Added to favorites! Generating a new suggestion..." : "Creating a better suggestion based on your preferences..." : t.description }),
                t.suggestedDomain && !r && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
                  /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
                  /* @__PURE__ */ e.jsxs("code", { className: "domain-text", children: [
                    t.suggestedDomain,
                    ".com"
                  ] }),
                  /* @__PURE__ */ e.jsxs("span", { className: `domain-status ${t.domainStatus}`, children: [
                    (t.domainStatus === "checking" || te.has(a)) && "⏳ Checking...",
                    t.domainStatus === "available" && "✅ Available",
                    t.domainStatus === "taken" && "❌ Taken",
                    t.domainStatus === "error" && "⚠️ Check manually"
                  ] })
                ] })
              ]
            },
            a
          );
        }) })
      ] }),
      ge && !C && /* @__PURE__ */ e.jsxs("div", { className: "refinement-section", children: [
        /* @__PURE__ */ e.jsx("div", { className: "refinement-info", children: /* @__PURE__ */ e.jsx("p", { children: "💡 I'll keep your liked names and replace the disliked ones with better suggestions!" }) }),
        /* @__PURE__ */ e.jsx(
          "button",
          {
            onClick: $e,
            className: "refinement-button",
            disabled: C,
            children: de ? "🔄 Refining..." : "🎯 Replace Disliked Names"
          }
        )
      ] })
    ] })
  ] }) });
};
export {
  Me as default
};

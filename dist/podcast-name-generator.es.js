import ve, { useState as R } from "react";
var we = { exports: {} }, te = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ke;
function ye() {
  if (ke) return te;
  ke = 1;
  var S = Symbol.for("react.transitional.element"), Y = Symbol.for("react.fragment");
  function C(w, j, p) {
    var k = null;
    if (p !== void 0 && (k = "" + p), j.key !== void 0 && (k = "" + j.key), "key" in j) {
      p = {};
      for (var N in j)
        N !== "key" && (p[N] = j[N]);
    } else p = j;
    return j = p.ref, {
      $$typeof: S,
      type: w,
      key: k,
      ref: j !== void 0 ? j : null,
      props: p
    };
  }
  return te.Fragment = Y, te.jsx = C, te.jsxs = C, te;
}
var ne = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Ne;
function je() {
  return Ne || (Ne = 1, process.env.NODE_ENV !== "production" && function() {
    function S(e) {
      if (e == null) return null;
      if (typeof e == "function")
        return e.$$typeof === le ? null : e.displayName || e.name || null;
      if (typeof e == "string") return e;
      switch (e) {
        case x:
          return "Fragment";
        case he:
          return "Profiler";
        case z:
          return "StrictMode";
        case B:
          return "Suspense";
        case ie:
          return "SuspenseList";
        case ce:
          return "Activity";
      }
      if (typeof e == "object")
        switch (typeof e.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), e.$$typeof) {
          case O:
            return "Portal";
          case oe:
            return (e.displayName || "Context") + ".Provider";
          case se:
            return (e._context.displayName || "Context") + ".Consumer";
          case I:
            var o = e.render;
            return e = e.displayName, e || (e = o.displayName || o.name || "", e = e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef"), e;
          case ge:
            return o = e.displayName || null, o !== null ? o : S(e.type) || "Memo";
          case K:
            o = e._payload, e = e._init;
            try {
              return S(e(o));
            } catch {
            }
        }
      return null;
    }
    function Y(e) {
      return "" + e;
    }
    function C(e) {
      try {
        Y(e);
        var o = !1;
      } catch {
        o = !0;
      }
      if (o) {
        o = console;
        var u = o.error, f = typeof Symbol == "function" && Symbol.toStringTag && e[Symbol.toStringTag] || e.constructor.name || "Object";
        return u.call(
          o,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          f
        ), Y(e);
      }
    }
    function w(e) {
      if (e === x) return "<>";
      if (typeof e == "object" && e !== null && e.$$typeof === K)
        return "<...>";
      try {
        var o = S(e);
        return o ? "<" + o + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function j() {
      var e = P.A;
      return e === null ? null : e.getOwner();
    }
    function p() {
      return Error("react-stack-top-frame");
    }
    function k(e) {
      if (F.call(e, "key")) {
        var o = Object.getOwnPropertyDescriptor(e, "key").get;
        if (o && o.isReactWarning) return !1;
      }
      return e.key !== void 0;
    }
    function N(e, o) {
      function u() {
        U || (U = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          o
        ));
      }
      u.isReactWarning = !0, Object.defineProperty(e, "key", {
        get: u,
        configurable: !0
      });
    }
    function M() {
      var e = S(this.type);
      return ue[e] || (ue[e] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), e = this.props.ref, e !== void 0 ? e : null;
    }
    function re(e, o, u, f, T, _, H, X) {
      return u = _.ref, e = {
        $$typeof: D,
        type: e,
        key: o,
        props: _,
        _owner: T
      }, (u !== void 0 ? u : null) !== null ? Object.defineProperty(e, "ref", {
        enumerable: !1,
        get: M
      }) : Object.defineProperty(e, "ref", { enumerable: !1, value: null }), e._store = {}, Object.defineProperty(e._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(e, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(e, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: H
      }), Object.defineProperty(e, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: X
      }), Object.freeze && (Object.freeze(e.props), Object.freeze(e)), e;
    }
    function A(e, o, u, f, T, _, H, X) {
      var h = o.children;
      if (h !== void 0)
        if (f)
          if (de(h)) {
            for (f = 0; f < h.length; f++)
              ae(h[f]);
            Object.freeze && Object.freeze(h);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else ae(h);
      if (F.call(o, "key")) {
        h = S(e);
        var n = Object.keys(o).filter(function(r) {
          return r !== "key";
        });
        f = 0 < n.length ? "{key: someKey, " + n.join(": ..., ") + ": ...}" : "{key: someKey}", fe[h + f] || (n = 0 < n.length ? "{" + n.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          f,
          h,
          n,
          h
        ), fe[h + f] = !0);
      }
      if (h = null, u !== void 0 && (C(u), h = "" + u), k(o) && (C(o.key), h = "" + o.key), "key" in o) {
        u = {};
        for (var t in o)
          t !== "key" && (u[t] = o[t]);
      } else u = o;
      return h && N(
        u,
        typeof e == "function" ? e.displayName || e.name || "Unknown" : e
      ), re(
        e,
        h,
        _,
        T,
        j(),
        u,
        H,
        X
      );
    }
    function ae(e) {
      typeof e == "object" && e !== null && e.$$typeof === D && e._store && (e._store.validated = 1);
    }
    var $ = ve, D = Symbol.for("react.transitional.element"), O = Symbol.for("react.portal"), x = Symbol.for("react.fragment"), z = Symbol.for("react.strict_mode"), he = Symbol.for("react.profiler"), se = Symbol.for("react.consumer"), oe = Symbol.for("react.context"), I = Symbol.for("react.forward_ref"), B = Symbol.for("react.suspense"), ie = Symbol.for("react.suspense_list"), ge = Symbol.for("react.memo"), K = Symbol.for("react.lazy"), ce = Symbol.for("react.activity"), le = Symbol.for("react.client.reference"), P = $.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, F = Object.prototype.hasOwnProperty, de = Array.isArray, G = console.createTask ? console.createTask : function() {
      return null;
    };
    $ = {
      "react-stack-bottom-frame": function(e) {
        return e();
      }
    };
    var U, ue = {}, me = $["react-stack-bottom-frame"].bind(
      $,
      p
    )(), V = G(w(p)), fe = {};
    ne.Fragment = x, ne.jsx = function(e, o, u, f, T) {
      var _ = 1e4 > P.recentlyCreatedOwnerStacks++;
      return A(
        e,
        o,
        u,
        !1,
        f,
        T,
        _ ? Error("react-stack-top-frame") : me,
        _ ? G(w(e)) : V
      );
    }, ne.jsxs = function(e, o, u, f, T) {
      var _ = 1e4 > P.recentlyCreatedOwnerStacks++;
      return A(
        e,
        o,
        u,
        !0,
        f,
        T,
        _ ? Error("react-stack-top-frame") : me,
        _ ? G(w(e)) : V
      );
    };
  }()), ne;
}
process.env.NODE_ENV === "production" ? we.exports = ye() : we.exports = je();
var s = we.exports;
const Ee = ({
  apiKey: S = "AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",
  className: Y = "",
  style: C = {}
}) => {
  const [w, j] = R(""), [p, k] = R([]), [N, M] = R(!1), [re, A] = R(null), [ae, $] = R(null), [D, O] = R([]), [x, z] = R({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    },
    generationRound: 0
  }), [he, se] = R(!1), [oe, I] = R(!1), [B, ie] = R([]), [ge, K] = R(!0), [ce, le] = R(!1), [P, F] = R(/* @__PURE__ */ new Set()), de = (n) => n.toLowerCase().replace(/[^a-z0-9\s]/g, "").replace(/\s+/g, "").replace(/^the/, "").substring(0, 50), G = async (n) => {
    try {
      const t = await fetch(`https://dns.google/resolve?name=${n}&type=A`, {
        method: "GET",
        headers: {
          Accept: "application/json"
        }
      });
      if (!t.ok)
        return "error";
      const r = await t.json();
      return r.Answer && r.Answer.length > 0 ? "taken" : "available";
    } catch (t) {
      return console.warn("Domain check failed:", t), "error";
    }
  }, U = async (n) => {
    const t = [...n];
    for (let r = 0; r < t.length; r++) {
      const a = de(t[r].name);
      t[r].suggestedDomain = a, t[r].domainStatus = "checking", F((i) => /* @__PURE__ */ new Set([...i, r]));
    }
    k(t);
    for (let r = 0; r < t.length; r++) {
      const a = `${t[r].suggestedDomain}.com`;
      try {
        const i = await G(a);
        k((d) => {
          const c = [...d];
          return c[r] && (c[r].domainStatus = i), c;
        });
      } catch {
        k((d) => {
          const c = [...d];
          return c[r] && (c[r].domainStatus = "error"), c;
        });
      } finally {
        F((i) => {
          const d = new Set(i);
          return d.delete(r), d;
        });
      }
    }
  }, ue = (n, t) => {
    if (n.length === 0) return null;
    const r = n.map((c) => c.name.split(" ").length), a = t.map((c) => c.name.split(" ").length), i = r.reduce((c, v) => c + v, 0) / r.length, d = a.length > 0 ? a.reduce((c, v) => c + v, 0) / a.length : 0;
    return i <= 2 && d > 2 ? "short" : i <= 4 && (d <= 2 || d > 4) ? "medium" : i > 4 && d <= 4 ? "long" : i <= 2 ? "short" : i <= 4 ? "medium" : "long";
  }, me = (n, t) => {
    if (n.length === 0) return null;
    const r = n.map((i) => i.description.toLowerCase()).join(" "), a = t.map((i) => i.description.toLowerCase()).join(" ");
    return (r.includes("professional") || r.includes("business")) && !a.includes("professional") && !a.includes("business") ? "professional" : (r.includes("creative") || r.includes("unique")) && !a.includes("creative") && !a.includes("unique") ? "creative" : (r.includes("fun") || r.includes("playful")) && !a.includes("fun") && !a.includes("playful") ? "playful" : "descriptive";
  }, V = (n) => {
    const t = [];
    n.forEach((a) => {
      const i = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], d = a.name.toLowerCase().split(/\s+/).filter(
        (c) => c.length > 2 && !i.includes(c)
      );
      t.push(...d);
    });
    const r = {};
    return t.forEach((a) => r[a] = (r[a] || 0) + 1), Object.entries(r).sort(([, a], [, i]) => i - a).slice(0, 5).map(([a]) => a);
  }, fe = (n) => {
    const t = n.filter((a) => a.liked === !0), r = n.filter((a) => a.liked === !1);
    return {
      preferredLength: ue(t, r),
      preferredStyle: me(t, r),
      likedKeywords: V(t),
      dislikedKeywords: V(r),
      preferredStructure: null
      // Simplified for now
    };
  }, e = (n, t) => {
    const r = `Create 5 high-converting and catchy podcast names based on the following user input: ${n}.`;
    let a = "";
    if (t.patterns.preferredLength) {
      const i = {
        short: "1-2 words",
        medium: "3-4 words",
        long: "5+ words"
      };
      a += `Focus on ${t.patterns.preferredLength} names (${i[t.patterns.preferredLength]}). `;
    }
    if (t.patterns.preferredStyle && (a += `Use a ${t.patterns.preferredStyle} style. `), t.patterns.likedKeywords.length > 0 && (a += `Incorporate concepts similar to: ${t.patterns.likedKeywords.join(", ")}. `), t.patterns.dislikedKeywords.length > 0 && (a += `Avoid concepts like: ${t.patterns.dislikedKeywords.join(", ")}. `), t.likedNames.length > 0) {
      const i = t.likedNames.slice(-2).map((d) => d.name).join('", "');
      a += `Generate names with similar appeal to: "${i}". `;
    }
    return `${r} ${a} Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`;
  }, o = async (n = !1) => {
    if (!w.trim()) {
      A("Please describe what your podcast is about");
      return;
    }
    M(!0), A(null), k([]), n ? I(!0) : (O([]), se(!1), I(!1), K(!0), le(!1));
    try {
      const t = n ? e(w, x) : `Create 5 high-converting and catchy podcast names based on the following user input: ${w}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`, r = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${S}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: t
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!r.ok)
        throw new Error(`API request failed: ${r.status} ${r.statusText}`);
      const a = await r.json();
      if (!a.candidates || !a.candidates[0] || !a.candidates[0].content)
        throw new Error("Invalid response format from API");
      const d = a.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!d)
        throw new Error("No valid JSON found in API response");
      const c = JSON.parse(d[0]);
      if (!c.podcast_names || !Array.isArray(c.podcast_names))
        throw new Error("Invalid response structure");
      k(c.podcast_names), U(c.podcast_names), ie((g) => [...g, c.podcast_names]), z((g) => ({
        ...g,
        generationRound: g.generationRound + 1
      }));
      const v = c.podcast_names.map((g, Z) => ({
        name: g.name,
        description: g.description,
        liked: null,
        timestamp: Date.now(),
        index: Z
      }));
      O(v);
    } catch (t) {
      console.error("Error generating podcast names:", t), A(t instanceof Error ? t.message : "An unexpected error occurred");
    } finally {
      M(!1), I(!1);
    }
  }, u = async (n, t) => {
    try {
      await navigator.clipboard.writeText(n), $(t), setTimeout(() => $(null), 2e3);
    } catch (r) {
      console.error("Failed to copy text:", r);
    }
  }, f = async (n, t) => {
    const r = [...D];
    r[n] = {
      ...r[n],
      liked: t,
      timestamp: Date.now()
    }, O(r);
    const a = { ...x }, i = r[n];
    t ? (a.dislikedNames = a.dislikedNames.filter((c) => c.name !== i.name), a.likedNames.find((c) => c.name === i.name) || a.likedNames.push(i)) : (a.likedNames = a.likedNames.filter((c) => c.name !== i.name), a.dislikedNames.find((c) => c.name === i.name) || a.dislikedNames.push(i), w.trim() && T(n, a)), a.patterns = fe([...a.likedNames, ...a.dislikedNames]), z(a), a.likedNames.length + a.dislikedNames.length >= 2 && se(!1), ce || (le(!0), K(!1));
  }, T = async (n, t) => {
    var r, a, i, d, c;
    try {
      F((m) => /* @__PURE__ */ new Set([...m, n]));
      const v = e(w, t), g = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=" + S, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: v.replace("Create 5", "Create 1")
              // Only need 1 replacement
            }]
          }]
        })
      });
      if (!g.ok)
        throw new Error(`API request failed: ${g.status}`);
      const Q = (c = (d = (i = (a = (r = (await g.json()).candidates) == null ? void 0 : r[0]) == null ? void 0 : a.content) == null ? void 0 : i.parts) == null ? void 0 : d[0]) == null ? void 0 : c.text;
      if (!Q)
        throw new Error("No content received from API");
      const E = Q.match(/\{[\s\S]*\}/);
      if (!E)
        throw new Error("No valid JSON found in response");
      const q = JSON.parse(E[0]);
      if (!q.podcast_names || !Array.isArray(q.podcast_names) || q.podcast_names.length === 0)
        throw new Error("Invalid response format");
      const W = q.podcast_names[0];
      k((m) => {
        const l = [...m];
        return l[n] = W, l;
      });
      const be = de(W.name), pe = await G(be);
      k((m) => {
        const l = [...m];
        return l[n] = {
          ...l[n],
          suggestedDomain: be,
          domainStatus: pe
        }, l;
      }), O((m) => {
        const l = [...m];
        return l[n] = {
          name: W.name,
          description: W.description,
          liked: null,
          timestamp: Date.now(),
          index: n
        }, l;
      });
    } catch (v) {
      console.error("Error generating replacement name:", v);
    } finally {
      F((v) => {
        const g = new Set(v);
        return g.delete(n), g;
      });
    }
  }, _ = () => {
    w.trim() && H(w);
  }, H = async (n) => {
    var t, r, a, i, d, c, v, g, Z, Q;
    M(!0), A(""), I(!0);
    try {
      const E = p.filter((m, l) => {
        const b = D.find((y) => y.index === l);
        return (b == null ? void 0 : b.liked) === !0;
      }), q = p.filter((m, l) => {
        const b = D.find((y) => y.index === l);
        return (b == null ? void 0 : b.liked) === !1;
      }).length, W = Math.max(1, q), pe = Math.min(5, E.length + W) - E.length;
      if (pe <= 0) {
        const m = e(n, x), l = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=" + S, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: m.replace("Create 5", "Create 1")
              }]
            }]
          })
        });
        if (!l.ok)
          throw new Error(`API request failed: ${l.status}`);
        const y = (d = (i = (a = (r = (t = (await l.json()).candidates) == null ? void 0 : t[0]) == null ? void 0 : r.content) == null ? void 0 : a.parts) == null ? void 0 : i[0]) == null ? void 0 : d.text;
        if (!y)
          throw new Error("No content received from API");
        const L = y.match(/\{[\s\S]*\}/);
        if (!L)
          throw new Error("No valid JSON found in response");
        const J = JSON.parse(L[0]);
        if (!J.podcast_names || !Array.isArray(J.podcast_names))
          throw new Error("Invalid response format");
        const ee = [...E, ...J.podcast_names].slice(0, 5);
        k(ee), U(ee);
      } else {
        const m = e(n, x), l = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=" + S, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: m.replace("Create 5", `Create ${pe}`)
              }]
            }]
          })
        });
        if (!l.ok)
          throw new Error(`API request failed: ${l.status}`);
        const y = (Q = (Z = (g = (v = (c = (await l.json()).candidates) == null ? void 0 : c[0]) == null ? void 0 : v.content) == null ? void 0 : g.parts) == null ? void 0 : Z[0]) == null ? void 0 : Q.text;
        if (!y)
          throw new Error("No content received from API");
        const L = y.match(/\{[\s\S]*\}/);
        if (!L)
          throw new Error("No valid JSON found in response");
        const J = JSON.parse(L[0]);
        if (!J.podcast_names || !Array.isArray(J.podcast_names))
          throw new Error("Invalid response format");
        const ee = [...E, ...J.podcast_names];
        k(ee), U(ee);
      }
      ie((m) => [...m, p]), z((m) => ({
        ...m,
        generationRound: m.generationRound + 1
      })), O((m) => m.filter((l) => {
        const b = p[l.index];
        return E.some((y) => y.name === (b == null ? void 0 : b.name));
      })), O((m) => m.map((l) => {
        const b = p[l.index], y = E.findIndex((L) => L.name === (b == null ? void 0 : b.name));
        return y >= 0 ? { ...l, index: y } : l;
      }).filter((l) => l.index >= 0));
    } catch (E) {
      console.error("Error generating refined names:", E), A(E instanceof Error ? E.message : "Failed to generate refined names. Please try again.");
    } finally {
      M(!1), I(!1);
    }
  }, X = (n) => {
    n.preventDefault(), o();
  }, h = (n) => {
    n.key === "Enter" && !n.shiftKey && (n.preventDefault(), o());
  };
  return /* @__PURE__ */ s.jsx("div", { className: `podcast-name-generator ${Y}`, style: C, children: /* @__PURE__ */ s.jsxs("div", { className: "generator-container", children: [
    /* @__PURE__ */ s.jsx("h2", { className: "generator-title", children: "Podcast Name Generator" }),
    /* @__PURE__ */ s.jsx("p", { className: "generator-subtitle", children: "Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI" }),
    ge && /* @__PURE__ */ s.jsx("div", { className: "onboarding-banner", children: /* @__PURE__ */ s.jsxs("div", { className: "onboarding-content", children: [
      /* @__PURE__ */ s.jsx("span", { className: "onboarding-icon", children: "💡" }),
      /* @__PURE__ */ s.jsxs("div", { className: "onboarding-text", children: [
        /* @__PURE__ */ s.jsx("strong", { children: "Pro Tip:" }),
        " Use the 👍👎 buttons to rate names. Liked names stay, disliked names get instantly replaced with better suggestions!"
      ] })
    ] }) }),
    /* @__PURE__ */ s.jsx("form", { onSubmit: X, className: "input-form", children: /* @__PURE__ */ s.jsxs("div", { className: "input-container", children: [
      /* @__PURE__ */ s.jsx(
        "textarea",
        {
          value: w,
          onChange: (n) => j(n.target.value),
          onKeyPress: h,
          placeholder: "Describe what your podcast is about",
          className: "input-field",
          rows: 3,
          disabled: N
        }
      ),
      /* @__PURE__ */ s.jsx(
        "button",
        {
          type: "submit",
          disabled: N || !w.trim(),
          className: "generate-button",
          children: N ? "Generating..." : "Generate Names"
        }
      )
    ] }) }),
    re && /* @__PURE__ */ s.jsxs("div", { className: "error-message", children: [
      /* @__PURE__ */ s.jsx("span", { className: "error-icon", children: "⚠️" }),
      re
    ] }),
    N && /* @__PURE__ */ s.jsxs("div", { className: "loading-container", children: [
      /* @__PURE__ */ s.jsx("div", { className: "loading-spinner" }),
      /* @__PURE__ */ s.jsx("p", { children: oe ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
    ] }),
    p.length > 0 && /* @__PURE__ */ s.jsxs("div", { className: "results-container", children: [
      /* @__PURE__ */ s.jsxs("h3", { className: "results-title", children: [
        x.generationRound > 1 ? "Refined Podcast Name Suggestions" : "Your Podcast Name Suggestions",
        B.length > 1 && /* @__PURE__ */ s.jsxs("span", { className: "generation-counter", children: [
          " (Round ",
          x.generationRound,
          ")"
        ] })
      ] }),
      x.generationRound > 1 && /* @__PURE__ */ s.jsxs("p", { className: "refinement-info", children: [
        "✨ These names are tailored based on your preferences from ",
        B.length - 1,
        " previous round",
        B.length > 2 ? "s" : ""
      ] }),
      x.generationRound === 1 && !ce && /* @__PURE__ */ s.jsx("div", { className: "feedback-hint", children: /* @__PURE__ */ s.jsxs("div", { className: "feedback-hint-content", children: [
        /* @__PURE__ */ s.jsx("span", { className: "feedback-hint-icon", children: "👆" }),
        /* @__PURE__ */ s.jsxs("p", { children: [
          /* @__PURE__ */ s.jsx("strong", { children: "Like what you see?" }),
          " Use the 👍👎 buttons below! Liked names will stay, disliked names get instantly replaced with new ones!"
        ] })
      ] }) }),
      /* @__PURE__ */ s.jsx("div", { className: "results-grid", children: p.map((n, t) => {
        const r = D.find((d) => d.index === t), a = (r == null ? void 0 : r.liked) === !0, i = (r == null ? void 0 : r.liked) === !1;
        return /* @__PURE__ */ s.jsxs(
          "div",
          {
            className: `result-card ${a ? "liked" : ""} ${i ? "disliked" : ""}`,
            children: [
              /* @__PURE__ */ s.jsxs("div", { className: "result-header", children: [
                /* @__PURE__ */ s.jsxs("h4", { className: "result-name", children: [
                  n.name,
                  a && x.generationRound > 1 && /* @__PURE__ */ s.jsx("span", { className: "kept-indicator", title: "This name was kept from your previous selection", children: "⭐" })
                ] }),
                /* @__PURE__ */ s.jsxs("div", { className: "result-actions", children: [
                  /* @__PURE__ */ s.jsxs("div", { className: "feedback-buttons", children: [
                    /* @__PURE__ */ s.jsx(
                      "button",
                      {
                        onClick: () => f(t, !0),
                        className: `feedback-button like-button ${a ? "active" : ""}`,
                        title: "I like this name",
                        disabled: N,
                        children: "👍"
                      }
                    ),
                    /* @__PURE__ */ s.jsx(
                      "button",
                      {
                        onClick: () => f(t, !1),
                        className: `feedback-button dislike-button ${i ? "active" : ""} ${P.has(t) ? "loading" : ""}`,
                        title: P.has(t) ? "Generating replacement..." : "I don't like this name",
                        disabled: N || P.has(t),
                        children: P.has(t) ? "🔄" : "👎"
                      }
                    )
                  ] }),
                  /* @__PURE__ */ s.jsx(
                    "button",
                    {
                      onClick: () => u(n.name, t),
                      className: "copy-button",
                      title: "Copy podcast name",
                      children: ae === t ? "✓ Copied!" : "📋 Copy"
                    }
                  )
                ] })
              ] }),
              /* @__PURE__ */ s.jsx("p", { className: "result-description", children: n.description }),
              n.suggestedDomain && /* @__PURE__ */ s.jsxs("div", { className: "domain-info", children: [
                /* @__PURE__ */ s.jsxs("div", { className: "domain-name", children: [
                  /* @__PURE__ */ s.jsx("span", { className: "domain-label", children: "Domain:" }),
                  /* @__PURE__ */ s.jsxs("code", { className: "domain-text", children: [
                    n.suggestedDomain,
                    ".com"
                  ] })
                ] }),
                /* @__PURE__ */ s.jsxs("div", { className: `domain-status ${n.domainStatus}`, children: [
                  (n.domainStatus === "checking" || P.has(t)) && /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
                    /* @__PURE__ */ s.jsx("span", { className: "domain-spinner", children: "⏳" }),
                    /* @__PURE__ */ s.jsx("span", { children: "Checking..." })
                  ] }),
                  n.domainStatus === "available" && /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
                    /* @__PURE__ */ s.jsx("span", { className: "domain-icon available", children: "✅" }),
                    /* @__PURE__ */ s.jsx("span", { children: "Available" })
                  ] }),
                  n.domainStatus === "taken" && /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
                    /* @__PURE__ */ s.jsx("span", { className: "domain-icon taken", children: "❌" }),
                    /* @__PURE__ */ s.jsx("span", { children: "Taken" })
                  ] }),
                  n.domainStatus === "error" && /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
                    /* @__PURE__ */ s.jsx("span", { className: "domain-icon error", children: "⚠️" }),
                    /* @__PURE__ */ s.jsx("span", { children: "Check manually" })
                  ] })
                ] })
              ] })
            ]
          },
          t
        );
      }) }),
      he && !N && /* @__PURE__ */ s.jsxs("div", { className: "refinement-section", children: [
        /* @__PURE__ */ s.jsx("div", { className: "refinement-info", children: /* @__PURE__ */ s.jsx("p", { children: "💡 I'll keep your liked names and replace the disliked ones with better suggestions!" }) }),
        /* @__PURE__ */ s.jsx(
          "button",
          {
            onClick: _,
            className: "refinement-button",
            disabled: N,
            children: oe ? "🔄 Refining..." : "🎯 Replace Disliked Names"
          }
        )
      ] })
    ] })
  ] }) });
};
export {
  Ee as default
};

import Te, { useState as w } from "react";
var be = { exports: {} }, oe = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Re;
function Pe() {
  if (Re) return oe;
  Re = 1;
  var _ = Symbol.for("react.transitional.element"), F = Symbol.for("react.fragment");
  function b(D, h, g) {
    var P = null;
    if (g !== void 0 && (P = "" + g), h.key !== void 0 && (P = "" + h.key), "key" in h) {
      g = {};
      for (var q in h)
        q !== "key" && (g[q] = h[q]);
    } else g = h;
    return h = g.ref, {
      $$typeof: _,
      type: D,
      key: P,
      ref: h !== void 0 ? h : null,
      props: g
    };
  }
  return oe.Fragment = F, oe.jsx = b, oe.jsxs = b, oe;
}
var le = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Ee;
function Oe() {
  return Ee || (Ee = 1, process.env.NODE_ENV !== "production" && function() {
    function _(s) {
      if (s == null) return null;
      if (typeof s == "function")
        return s.$$typeof === Ne ? null : s.displayName || s.name || null;
      if (typeof s == "string") return s;
      switch (s) {
        case L:
          return "Fragment";
        case ce:
          return "Profiler";
        case Y:
          return "StrictMode";
        case W:
          return "Suspense";
        case ve:
          return "SuspenseList";
        case J:
          return "Activity";
      }
      if (typeof s == "object")
        switch (typeof s.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), s.$$typeof) {
          case X:
            return "Portal";
          case ge:
            return (s.displayName || "Context") + ".Provider";
          case fe:
            return (s._context.displayName || "Context") + ".Consumer";
          case de:
            var d = s.render;
            return s = s.displayName, s || (s = d.displayName || d.name || "", s = s !== "" ? "ForwardRef(" + s + ")" : "ForwardRef"), s;
          case me:
            return d = s.displayName || null, d !== null ? d : _(s.type) || "Memo";
          case Q:
            d = s._payload, s = s._init;
            try {
              return _(s(d));
            } catch {
            }
        }
      return null;
    }
    function F(s) {
      return "" + s;
    }
    function b(s) {
      try {
        F(s);
        var d = !1;
      } catch {
        d = !0;
      }
      if (d) {
        d = console;
        var m = d.error, p = typeof Symbol == "function" && Symbol.toStringTag && s[Symbol.toStringTag] || s.constructor.name || "Object";
        return m.call(
          d,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          p
        ), F(s);
      }
    }
    function D(s) {
      if (s === L) return "<>";
      if (typeof s == "object" && s !== null && s.$$typeof === Q)
        return "<...>";
      try {
        var d = _(s);
        return d ? "<" + d + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function h() {
      var s = K.A;
      return s === null ? null : s.getOwner();
    }
    function g() {
      return Error("react-stack-top-frame");
    }
    function P(s) {
      if (ue.call(s, "key")) {
        var d = Object.getOwnPropertyDescriptor(s, "key").get;
        if (d && d.isReactWarning) return !1;
      }
      return s.key !== void 0;
    }
    function q(s, d) {
      function m() {
        ee || (ee = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          d
        ));
      }
      m.isReactWarning = !0, Object.defineProperty(s, "key", {
        get: m,
        configurable: !0
      });
    }
    function E() {
      var s = _(this.type);
      return C[s] || (C[s] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), s = this.props.ref, s !== void 0 ? s : null;
    }
    function G(s, d, m, p, T, y, ae, re) {
      return m = y.ref, s = {
        $$typeof: H,
        type: s,
        key: d,
        props: y,
        _owner: T
      }, (m !== void 0 ? m : null) !== null ? Object.defineProperty(s, "ref", {
        enumerable: !1,
        get: E
      }) : Object.defineProperty(s, "ref", { enumerable: !1, value: null }), s._store = {}, Object.defineProperty(s._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(s, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(s, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: ae
      }), Object.defineProperty(s, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: re
      }), Object.freeze && (Object.freeze(s.props), Object.freeze(s)), s;
    }
    function V(s, d, m, p, T, y, ae, re) {
      var v = d.children;
      if (v !== void 0)
        if (p)
          if (Z(v)) {
            for (p = 0; p < v.length; p++)
              O(v[p]);
            Object.freeze && Object.freeze(v);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else O(v);
      if (ue.call(d, "key")) {
        v = _(s);
        var I = Object.keys(d).filter(function(pe) {
          return pe !== "key";
        });
        p = 0 < I.length ? "{key: someKey, " + I.join(": ..., ") + ": ...}" : "{key: someKey}", ne[v + p] || (I = 0 < I.length ? "{" + I.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          p,
          v,
          I,
          v
        ), ne[v + p] = !0);
      }
      if (v = null, m !== void 0 && (b(m), v = "" + m), P(d) && (b(d.key), v = "" + d.key), "key" in d) {
        m = {};
        for (var B in d)
          B !== "key" && (m[B] = d[B]);
      } else m = d;
      return v && q(
        m,
        typeof s == "function" ? s.displayName || s.name || "Unknown" : s
      ), G(
        s,
        v,
        y,
        T,
        h(),
        m,
        ae,
        re
      );
    }
    function O(s) {
      typeof s == "object" && s !== null && s.$$typeof === H && s._store && (s._store.validated = 1);
    }
    var z = Te, H = Symbol.for("react.transitional.element"), X = Symbol.for("react.portal"), L = Symbol.for("react.fragment"), Y = Symbol.for("react.strict_mode"), ce = Symbol.for("react.profiler"), fe = Symbol.for("react.consumer"), ge = Symbol.for("react.context"), de = Symbol.for("react.forward_ref"), W = Symbol.for("react.suspense"), ve = Symbol.for("react.suspense_list"), me = Symbol.for("react.memo"), Q = Symbol.for("react.lazy"), J = Symbol.for("react.activity"), Ne = Symbol.for("react.client.reference"), K = z.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, ue = Object.prototype.hasOwnProperty, Z = Array.isArray, he = console.createTask ? console.createTask : function() {
      return null;
    };
    z = {
      "react-stack-bottom-frame": function(s) {
        return s();
      }
    };
    var ee, C = {}, te = z["react-stack-bottom-frame"].bind(
      z,
      g
    )(), se = he(D(g)), ne = {};
    le.Fragment = L, le.jsx = function(s, d, m, p, T) {
      var y = 1e4 > K.recentlyCreatedOwnerStacks++;
      return V(
        s,
        d,
        m,
        !1,
        p,
        T,
        y ? Error("react-stack-top-frame") : te,
        y ? he(D(s)) : se
      );
    }, le.jsxs = function(s, d, m, p, T) {
      var y = 1e4 > K.recentlyCreatedOwnerStacks++;
      return V(
        s,
        d,
        m,
        !0,
        p,
        T,
        y ? Error("react-stack-top-frame") : te,
        y ? he(D(s)) : se
      );
    };
  }()), le;
}
process.env.NODE_ENV === "production" ? be.exports = Pe() : be.exports = Oe();
var e = be.exports;
const $e = ({
  className: _ = "",
  style: F = {}
}) => {
  const [b, D] = w(""), [h, g] = w([]), [P, q] = w([]), [E, G] = w(!1), [V, O] = w(null), [z, H] = w(null), [X, L] = w([]), [Y, ce] = w({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    }
  }), [fe, ge] = w(!1), [de, W] = w(!1), [ve, me] = w(!1), [Q, J] = w(/* @__PURE__ */ new Set()), [Ne, K] = w(/* @__PURE__ */ new Set()), [ue, Z] = w(/* @__PURE__ */ new Set()), [he, ee] = w(0), [C, te] = w(!1), se = 100, ne = () => {
    const t = document.createElement("canvas"), n = t.getContext("2d");
    n.textBaseline = "top", n.font = "14px Arial", n.fillText("Usage tracking", 2, 2);
    const a = t.toDataURL(), o = navigator.userAgent, r = navigator.language, l = Intl.DateTimeFormat().resolvedOptions().timeZone, i = a + o + r + l;
    let c = 0;
    for (let u = 0; u < i.length; u++) {
      const x = i.charCodeAt(u);
      c = (c << 5) - c + x, c = c & c;
    }
    return `podcast_usage_${Math.abs(c)}`;
  }, s = () => {
    const t = ne(), n = (/* @__PURE__ */ new Date()).toDateString(), a = `${t}_${n}`, o = localStorage.getItem(a), r = o ? parseInt(o, 10) : 0;
    return ee(r), r >= se ? (te(!0), !1) : !0;
  }, d = (t = 1) => {
    const n = ne(), a = (/* @__PURE__ */ new Date()).toDateString(), o = `${n}_${a}`, r = localStorage.getItem(o), i = (r ? parseInt(r, 10) : 0) + t;
    localStorage.setItem(o, i.toString()), ee(i), i >= se && te(!0);
  };
  Te.useEffect(() => {
    s();
  }, []);
  const m = (t) => {
    let n = t.toLowerCase().replace(/[^a-z0-9\s]/g, "").replace(/\s+/g, " ").replace(/^the\s+/, "").trim();
    const a = ["the", "and", "for", "with", "from", "show", "podcast", "cast", "of", "in", "on", "at", "to", "a", "an"], o = n.split(" "), r = o.filter(
      (i) => i.length > 2 && !a.includes(i)
    );
    if (r.length === 1) {
      const i = r[0];
      return i.length >= 6 && i.length <= 15 ? i : i.length < 6 ? i + "pod" : p(i);
    }
    if (r.length >= 2) {
      const i = r[0], c = r[1], u = i + c;
      if (u.length >= 6 && u.length <= 15)
        return u;
      const x = p(i), M = p(c), N = x + M;
      if (N.length >= 6 && N.length <= 15)
        return N;
      if (N.length > 15)
        return x + "-" + M;
    }
    if (r.length > 0) {
      const i = p(r[0]), c = ["cast", "pod", "show", "talk"];
      for (const u of c) {
        const x = i + u;
        if (x.length >= 6 && x.length <= 15)
          return x;
      }
      if (i.length >= 6 && i.length <= 15)
        return i;
    }
    const l = o[0];
    return l && l.length >= 3 ? p(l) + "pod" : "podcast" + Math.random().toString(36).substring(2, 5);
  }, p = (t) => {
    if (t.length <= 8) return t;
    const n = {
      // Business & Professional
      business: "biz",
      entrepreneur: "entre",
      marketing: "market",
      finance: "fin",
      startup: "start",
      leadership: "lead",
      strategy: "strat",
      success: "win",
      growth: "grow",
      innovation: "innov",
      management: "manage",
      // Technology
      technology: "tech",
      development: "dev",
      digital: "digi",
      software: "soft",
      coding: "code",
      programming: "prog",
      // Content & Media
      stories: "story",
      journey: "path",
      adventure: "quest",
      creative: "create",
      entertainment: "fun",
      education: "learn",
      knowledge: "know",
      wisdom: "wise",
      // Lifestyle & Personal
      lifestyle: "life",
      wellness: "well",
      fitness: "fit",
      health: "heal",
      mindset: "mind",
      motivation: "motive",
      inspiration: "inspire",
      // Geographic & Cultural
      american: "usa",
      european: "euro",
      international: "global",
      community: "comm",
      culture: "cult",
      society: "social"
    };
    if (n[t])
      return n[t];
    const a = ["super", "mega", "ultra", "micro", "mini", "multi"];
    for (const r of a)
      if (t.startsWith(r) && t.length > r.length + 3) {
        const l = t.substring(r.length);
        if (l.length <= 8)
          return l;
      }
    const o = ["ing", "tion", "sion", "ness", "ment", "able", "ible"];
    for (const r of o)
      if (t.endsWith(r) && t.length > r.length + 4) {
        const l = t.substring(0, t.length - r.length);
        if (l.length >= 4 && l.length <= 8)
          return l;
      }
    return t.length > 8 ? t.substring(0, 8) : t;
  }, T = async (t) => {
    try {
      const n = await fetch(`https://dns.google/resolve?name=${t}&type=A`, {
        method: "GET",
        headers: {
          Accept: "application/json"
        }
      });
      if (!n.ok)
        return "error";
      const a = await n.json();
      return a.Answer && a.Answer.length > 0 ? "taken" : "available";
    } catch (n) {
      return console.warn("Domain check failed:", n), "error";
    }
  }, y = async (t) => {
    const n = [...t];
    for (let a = 0; a < n.length; a++) {
      const o = m(n[a].name);
      n[a].suggestedDomain = o, n[a].domainStatus = "checking", J((r) => /* @__PURE__ */ new Set([...r, a]));
    }
    g(n);
    for (let a = 0; a < n.length; a++) {
      const o = `${n[a].suggestedDomain}.com`;
      try {
        const r = await T(o);
        g((l) => {
          const i = [...l];
          return i[a] && (i[a].domainStatus = r), i;
        });
      } catch {
        g((l) => {
          const i = [...l];
          return i[a] && (i[a].domainStatus = "error"), i;
        });
      } finally {
        J((r) => {
          const l = new Set(r);
          return l.delete(a), l;
        });
      }
    }
  }, ae = (t, n) => {
    if (t.length === 0) return null;
    const a = t.map((i) => i.name.split(" ").length), o = n.map((i) => i.name.split(" ").length), r = a.reduce((i, c) => i + c, 0) / a.length, l = o.length > 0 ? o.reduce((i, c) => i + c, 0) / o.length : 0;
    return r <= 2 && l > 2 ? "short" : r <= 4 && (l <= 2 || l > 4) ? "medium" : r > 4 && l <= 4 ? "long" : r <= 2 ? "short" : r <= 4 ? "medium" : "long";
  }, re = (t, n) => {
    if (t.length === 0) return null;
    const a = t.map((r) => r.description.toLowerCase()).join(" "), o = n.map((r) => r.description.toLowerCase()).join(" ");
    return (a.includes("professional") || a.includes("business")) && !o.includes("professional") && !o.includes("business") ? "professional" : (a.includes("creative") || a.includes("unique")) && !o.includes("creative") && !o.includes("unique") ? "creative" : (a.includes("fun") || a.includes("playful")) && !o.includes("fun") && !o.includes("playful") ? "playful" : "descriptive";
  }, v = (t) => {
    const n = [];
    t.forEach((o) => {
      const r = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], l = o.name.toLowerCase().split(/\s+/).filter(
        (i) => i.length > 2 && !r.includes(i)
      );
      n.push(...l);
    });
    const a = {};
    return n.forEach((o) => a[o] = (a[o] || 0) + 1), Object.entries(a).sort(([, o], [, r]) => r - o).slice(0, 5).map(([o]) => o);
  }, I = (t) => {
    const n = t.filter((o) => o.liked === !0), a = t.filter((o) => o.liked === !1);
    return {
      preferredLength: ae(n, a),
      preferredStyle: re(n, a),
      likedKeywords: v(n),
      dislikedKeywords: v(a),
      preferredStructure: null
      // Simplified for now
    };
  }, B = (t, n) => {
    const a = [
      ...n.likedNames.map((i) => i.name.toLowerCase()),
      ...n.dislikedNames.map((i) => i.name.toLowerCase()),
      ...h.map((i) => i.name.toLowerCase())
    ], o = `Create 4 unique, high-converting podcast names for: ${t}`;
    let r = `

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;
    a.length > 0 && (r += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${a.map((i) => `- ${i}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);
    let l = "";
    if (n.patterns.preferredLength && (l += `
Focus on ${{
      short: "1-2 words (punchy and memorable)",
      medium: "2-3 words (balanced and brandable)",
      long: "3-4 words (descriptive but still catchy)"
    }[n.patterns.preferredLength]}. `), n.patterns.preferredStyle && (l += `Use ${{
      descriptive: "clear, straightforward names that explain the content",
      creative: "imaginative, metaphorical, or playful names",
      professional: "authoritative, business-focused names",
      playful: "fun, energetic, engaging names"
    }[n.patterns.preferredStyle] || n.patterns.preferredStyle}. `), n.patterns.likedKeywords.length > 0 && (l += `
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (l += `
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `), n.likedNames.length > 0) {
      const i = n.likedNames.slice(-2).map((c) => c.name).join('", "');
      l += `
Generate names with similar appeal to: "${i}" (but completely different concepts). `;
    }
    return `${o}${r}${l}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;
  }, pe = (t, n, a = 1) => {
    const o = [
      ...n.likedNames.map((c) => c.name.toLowerCase()),
      ...n.dislikedNames.map((c) => c.name.toLowerCase()),
      ...h.map((c) => c.name.toLowerCase())
    ], r = `Create ${a} unique, high-converting podcast name${a > 1 ? "s" : ""} for: ${t}`;
    let l = `

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;
    o.length > 0 && (l += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${o.map((c) => `- ${c}`).join(`
`)}
Do not create names similar to any of the above.`);
    let i = "";
    return n.patterns.likedKeywords.length > 0 && (i += `
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (i += `
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `), `${r}${l}${i}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works"}${a > 1 ? ', {"name": "Unique Name 2", "description": "Why this works"}' : ""}]}`;
  }, xe = async (t = !1) => {
    if (!b.trim()) {
      O("Please describe what your podcast is about");
      return;
    }
    if (!s()) {
      O(null);
      return;
    }
    G(!0), O(null), g([]), t ? W(!0) : (L([]), ge(!1), W(!1), me(!1));
    try {
      const n = t ? B(b, Y) : `Create 4 unique, high-converting podcast names for: ${b}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`, a = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: n
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!a.ok)
        throw new Error(`API request failed: ${a.status} ${a.statusText}`);
      const o = await a.json();
      if (!o.candidates || !o.candidates[0] || !o.candidates[0].content)
        throw new Error("Invalid response format from API");
      const l = o.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!l)
        throw new Error("No valid JSON found in API response");
      const i = JSON.parse(l[0]);
      if (!i.podcast_names || !Array.isArray(i.podcast_names))
        throw new Error("Invalid response structure");
      g(i.podcast_names), d(4), y(i.podcast_names);
      const c = i.podcast_names.map((u, x) => ({
        name: u.name,
        description: u.description,
        liked: null,
        timestamp: Date.now(),
        index: x
      }));
      L(c);
    } catch (n) {
      console.error("Error generating podcast names:", n), O(n instanceof Error ? n.message : "An unexpected error occurred");
    } finally {
      G(!1), W(!1);
    }
  }, je = async (t, n) => {
    try {
      await navigator.clipboard.writeText(t), H(n), setTimeout(() => H(null), 2e3);
    } catch (a) {
      console.error("Failed to copy text:", a);
    }
  }, we = async (t, n) => {
    const a = h[t];
    a && (K((o) => /* @__PURE__ */ new Set([...o, t])), n ? (q((o) => o.find((r) => r.name === a.name) ? o : [...o, a]), ce((o) => {
      const r = { ...o };
      return r.dislikedNames = r.dislikedNames.filter((l) => l.name !== a.name), r.likedNames.find((l) => l.name === a.name) || r.likedNames.push({
        name: a.name,
        description: a.description,
        liked: !0,
        timestamp: Date.now(),
        index: t
      }), r.patterns = I([...r.likedNames, ...r.dislikedNames]), r;
    })) : ce((o) => {
      const r = { ...o };
      return r.likedNames = r.likedNames.filter((l) => l.name !== a.name), r.dislikedNames.find((l) => l.name === a.name) || r.dislikedNames.push({
        name: a.name,
        description: a.description,
        liked: !1,
        timestamp: Date.now(),
        index: t
      }), r.patterns = I([...r.likedNames, ...r.dislikedNames]), r;
    }), Z((o) => /* @__PURE__ */ new Set([...o, t])), b.trim() && Ce(t), ve || me(!0));
  }, Ce = async (t) => {
    var n, a, o, r, l, i;
    if (s())
      try {
        J((A) => /* @__PURE__ */ new Set([...A, t]));
        const c = B(b, Y), u = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: c
              }]
            }]
          })
        });
        if (!u.ok)
          throw new Error("Failed to generate replacement suggestion");
        const M = (l = (r = (o = (a = (n = (await u.json()).candidates) == null ? void 0 : n[0]) == null ? void 0 : a.content) == null ? void 0 : o.parts) == null ? void 0 : r[0]) == null ? void 0 : l.text;
        if (!M)
          throw new Error("No content in API response");
        const N = M.match(/\{[\s\S]*\}/);
        if (!N)
          throw new Error("No valid JSON found in response");
        const U = (i = JSON.parse(N[0]).names) == null ? void 0 : i[0];
        if (U && (g((A) => {
          const S = [...A];
          return S[t] = {
            name: U.name,
            description: U.description,
            suggestedDomain: U.suggestedDomain,
            domainStatus: "checking"
          }, S;
        }), K((A) => {
          const S = new Set(A);
          return S.delete(t), S;
        }), Z((A) => {
          const S = new Set(A);
          return S.delete(t), S;
        }), U.suggestedDomain)) {
          const A = await T(U.suggestedDomain);
          g((S) => {
            const j = [...S];
            return j[t] && (j[t].domainStatus = A), j;
          });
        }
      } catch (c) {
        console.error("Error generating replacement suggestion:", c), Z((u) => {
          const x = new Set(u);
          return x.delete(t), x;
        });
      } finally {
        J((c) => {
          const u = new Set(c);
          return u.delete(t), u;
        });
      }
  }, Ae = () => {
    b.trim() && _e(b);
  }, _e = async (t) => {
    var n, a, o, r, l, i, c, u, x, M;
    G(!0), O(""), W(!0);
    try {
      const N = h.filter((j, R) => {
        const f = X.find((k) => k.index === R);
        return (f == null ? void 0 : f.liked) === !0;
      }), Se = h.filter((j, R) => {
        const f = X.find((k) => k.index === R);
        return (f == null ? void 0 : f.liked) === !1;
      }).length, U = Math.max(1, Se), S = Math.min(5, N.length + U) - N.length;
      if (S <= 0) {
        const j = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: pe(t, Y, 1)
              }]
            }]
          })
        });
        if (!j.ok)
          throw new Error(`API request failed: ${j.status}`);
        const f = (l = (r = (o = (a = (n = (await j.json()).candidates) == null ? void 0 : n[0]) == null ? void 0 : a.content) == null ? void 0 : o.parts) == null ? void 0 : r[0]) == null ? void 0 : l.text;
        if (!f)
          throw new Error("No content received from API");
        const k = f.match(/\{[\s\S]*\}/);
        if (!k)
          throw new Error("No valid JSON found in response");
        const $ = JSON.parse(k[0]);
        if (!$.podcast_names || !Array.isArray($.podcast_names))
          throw new Error("Invalid response format");
        const ie = [...N, ...$.podcast_names].slice(0, 5);
        g(ie), y(ie);
      } else {
        const j = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: pe(t, Y, S)
              }]
            }]
          })
        });
        if (!j.ok)
          throw new Error(`API request failed: ${j.status}`);
        const f = (M = (x = (u = (c = (i = (await j.json()).candidates) == null ? void 0 : i[0]) == null ? void 0 : c.content) == null ? void 0 : u.parts) == null ? void 0 : x[0]) == null ? void 0 : M.text;
        if (!f)
          throw new Error("No content received from API");
        const k = f.match(/\{[\s\S]*\}/);
        if (!k)
          throw new Error("No valid JSON found in response");
        const $ = JSON.parse(k[0]);
        if (!$.podcast_names || !Array.isArray($.podcast_names))
          throw new Error("Invalid response format");
        const ie = [...N, ...$.podcast_names];
        g(ie), y(ie);
      }
      L((j) => j.filter((R) => {
        const f = h[R.index];
        return N.some((k) => k.name === (f == null ? void 0 : f.name));
      })), L((j) => j.map((R) => {
        const f = h[R.index], k = N.findIndex(($) => $.name === (f == null ? void 0 : f.name));
        return k >= 0 ? { ...R, index: k } : R;
      }).filter((R) => R.index >= 0));
    } catch (N) {
      console.error("Error generating refined names:", N), O(N instanceof Error ? N.message : "Failed to generate refined names. Please try again.");
    } finally {
      G(!1), W(!1);
    }
  }, ye = (t) => {
    t.preventDefault(), xe();
  }, ke = (t) => {
    t.key === "Enter" && !t.shiftKey && (t.preventDefault(), xe());
  };
  return /* @__PURE__ */ e.jsx("div", { className: `podcast-name-generator ${_}`, style: F, children: /* @__PURE__ */ e.jsxs("div", { className: "generator-container", children: [
    /* @__PURE__ */ e.jsxs("div", { className: "header-section", children: [
      /* @__PURE__ */ e.jsx("h1", { className: "main-title", children: "Free Podcast Name Generator" }),
      /* @__PURE__ */ e.jsx("h2", { className: "main-subtitle", children: "Create the Perfect Name for Your Podcast in Seconds" })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: "benefits-section", children: [
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "100% Free Forever" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "No Sign-up Required" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "Instant Results" })
      ] })
    ] }),
    C && /* @__PURE__ */ e.jsx("div", { className: "limit-reached-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "limit-content", children: [
      /* @__PURE__ */ e.jsx("span", { className: "limit-icon", children: "⚠️" }),
      /* @__PURE__ */ e.jsx("div", { className: "limit-text", children: /* @__PURE__ */ e.jsx("p", { children: "You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below." }) })
    ] }) }),
    h.length === 0 && /* @__PURE__ */ e.jsx("div", { className: "initial-input-section", children: /* @__PURE__ */ e.jsx("form", { onSubmit: ye, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
      /* @__PURE__ */ e.jsx(
        "textarea",
        {
          value: b,
          onChange: (t) => D(t.target.value),
          onKeyPress: ke,
          placeholder: "Describe what your podcast is about",
          className: "input-field",
          rows: 3,
          disabled: E
        }
      ),
      /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
        /* @__PURE__ */ e.jsx(
          "button",
          {
            type: "submit",
            disabled: E || !b.trim() || C,
            className: `generate-button ${C ? "disabled" : ""}`,
            children: E ? "Generating..." : C ? "Daily Limit Reached" : "Generate Names"
          }
        ),
        /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
          ] }),
          /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
            /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star star-partial", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
            ] }),
            /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
          ] })
        ] })
      ] })
    ] }) }) }),
    V && /* @__PURE__ */ e.jsxs("div", { className: "error-message", children: [
      /* @__PURE__ */ e.jsx("span", { className: "error-icon", children: "⚠️" }),
      V
    ] }),
    E && /* @__PURE__ */ e.jsxs("div", { className: "loading-container", children: [
      /* @__PURE__ */ e.jsx("div", { className: "loading-spinner" }),
      /* @__PURE__ */ e.jsx("p", { children: de ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
    ] }),
    h.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "results-container", children: [
      P.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "favorites-section", children: [
        /* @__PURE__ */ e.jsxs("div", { className: "favorites-header", children: [
          /* @__PURE__ */ e.jsxs("h3", { children: [
            "🏆 Your Winning Podcast Names (",
            P.length,
            ")"
          ] }),
          /* @__PURE__ */ e.jsx("p", { className: "favorites-subtitle", children: "Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!" })
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "favorites-grid", children: P.map((t, n) => /* @__PURE__ */ e.jsxs("div", { className: "favorite-card", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "favorite-content", children: [
            /* @__PURE__ */ e.jsx("h4", { className: "favorite-name", children: t.name }),
            /* @__PURE__ */ e.jsx("p", { className: "favorite-description", children: t.description }),
            t.suggestedDomain && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
              /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
              /* @__PURE__ */ e.jsx("span", { className: "domain-name", children: t.suggestedDomain }),
              /* @__PURE__ */ e.jsx("span", { className: `domain-status ${t.domainStatus}`, children: t.domainStatus === "available" ? "✅ Available" : t.domainStatus === "taken" ? "❌ Taken" : t.domainStatus === "error" ? "⚠️ Check manually" : "🔍 Checking..." })
            ] })
          ] }),
          /* @__PURE__ */ e.jsx("div", { className: "favorite-actions", children: /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: () => je(t.name, -1),
              className: "copy-button small",
              title: "Copy to clipboard",
              children: "📋 Copy"
            }
          ) })
        ] }, `fav-${n}`)) })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "input-section-simple", children: [
        /* @__PURE__ */ e.jsx("div", { className: "input-help-message-simple", children: /* @__PURE__ */ e.jsxs("p", { className: "input-sub-description", children: [
          "💡 Want different suggestions? Update your description below - ",
          /* @__PURE__ */ e.jsx("strong", { children: "your favorites will stay safe!" })
        ] }) }),
        /* @__PURE__ */ e.jsx("form", { onSubmit: ye, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
          /* @__PURE__ */ e.jsx(
            "textarea",
            {
              value: b,
              onChange: (t) => D(t.target.value),
              onKeyPress: ke,
              placeholder: "Describe what your podcast is about",
              className: "input-field",
              rows: 3,
              disabled: E
            }
          ),
          /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
            /* @__PURE__ */ e.jsx(
              "button",
              {
                type: "submit",
                disabled: E || !b.trim() || C,
                className: `generate-button ${C ? "disabled" : ""}`,
                children: E ? "Generating..." : C ? "Daily Limit Reached" : "Generate Names"
              }
            ),
            /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
              /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
              ] }),
              /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
                /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star star-partial", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
                ] }),
                /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
              ] })
            ] })
          ] })
        ] }) })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "suggestions-section", children: [
        /* @__PURE__ */ e.jsx("div", { className: "suggestions-header", children: /* @__PURE__ */ e.jsx("h3", { children: "🎯 Current Suggestions" }) }),
        /* @__PURE__ */ e.jsx("div", { className: "onboarding-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "onboarding-content", children: [
          /* @__PURE__ */ e.jsx("span", { className: "onboarding-icon", children: "💡" }),
          /* @__PURE__ */ e.jsxs("div", { className: "onboarding-text", children: [
            /* @__PURE__ */ e.jsx("strong", { children: "Smart AI Learning:" }),
            " The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."
          ] })
        ] }) }),
        /* @__PURE__ */ e.jsx("div", { className: "results-grid", children: h.map((t, n) => {
          const a = X.find((u) => u.index === n), o = (a == null ? void 0 : a.liked) === !0, r = (a == null ? void 0 : a.liked) === !1, l = Ne.has(n), i = ue.has(n), c = Q.has(n);
          return l && !i && !c ? null : /* @__PURE__ */ e.jsxs(
            "div",
            {
              className: `result-card ${o ? "liked" : ""} ${r ? "disliked" : ""} ${l ? "hidden" : ""} ${i ? "pending" : ""}`,
              style: {
                opacity: l ? 0.3 : 1,
                pointerEvents: l ? "none" : "auto"
              },
              children: [
                /* @__PURE__ */ e.jsxs("div", { className: "result-header", children: [
                  /* @__PURE__ */ e.jsx("h4", { className: "result-name", children: c ? "Generating new suggestion..." : t.name }),
                  /* @__PURE__ */ e.jsxs("div", { className: "result-actions", children: [
                    /* @__PURE__ */ e.jsxs("div", { className: "feedback-buttons", children: [
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => we(n, !0),
                          className: `feedback-button like-button ${o ? "active" : ""}`,
                          title: "I like this name",
                          disabled: l || c,
                          children: "👍"
                        }
                      ),
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => we(n, !1),
                          className: `feedback-button dislike-button ${r ? "active" : ""} ${c ? "loading" : ""}`,
                          title: c ? "Generating replacement..." : "I don't like this name",
                          disabled: l || c,
                          children: c ? "🔄" : "👎"
                        }
                      )
                    ] }),
                    /* @__PURE__ */ e.jsx(
                      "button",
                      {
                        onClick: () => je(t.name, n),
                        className: "copy-button",
                        title: "Copy podcast name",
                        disabled: l || c,
                        children: z === n ? "✓ Copied!" : "📋 Copy"
                      }
                    )
                  ] })
                ] }),
                /* @__PURE__ */ e.jsx("p", { className: "result-description", children: c ? "Creating a better suggestion based on your preferences..." : t.description }),
                t.suggestedDomain && !c && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
                  /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
                  /* @__PURE__ */ e.jsxs("code", { className: "domain-text", children: [
                    t.suggestedDomain,
                    ".com"
                  ] }),
                  /* @__PURE__ */ e.jsxs("span", { className: `domain-status ${t.domainStatus}`, children: [
                    (t.domainStatus === "checking" || Q.has(n)) && "⏳ Checking...",
                    t.domainStatus === "available" && "✅ Available",
                    t.domainStatus === "taken" && "❌ Taken",
                    t.domainStatus === "error" && "⚠️ Check manually"
                  ] })
                ] })
              ]
            },
            n
          );
        }) })
      ] }),
      fe && !E && /* @__PURE__ */ e.jsxs("div", { className: "refinement-section", children: [
        /* @__PURE__ */ e.jsx("div", { className: "refinement-info", children: /* @__PURE__ */ e.jsx("p", { children: "💡 I'll keep your liked names and replace the disliked ones with better suggestions!" }) }),
        /* @__PURE__ */ e.jsx(
          "button",
          {
            onClick: Ae,
            className: "refinement-button",
            disabled: E,
            children: de ? "🔄 Refining..." : "🎯 Replace Disliked Names"
          }
        )
      ] })
    ] })
  ] }) });
};
export {
  $e as default
};

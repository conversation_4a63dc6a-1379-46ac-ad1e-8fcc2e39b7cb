import je, { useState as w } from "react";
var ve = { exports: {} }, re = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var we;
function xe() {
  if (we) return re;
  we = 1;
  var R = Symbol.for("react.transitional.element"), q = Symbol.for("react.fragment");
  function I(k, y, u) {
    var h = null;
    if (u !== void 0 && (h = "" + u), y.key !== void 0 && (h = "" + y.key), "key" in y) {
      u = {};
      for (var P in y)
        P !== "key" && (u[P] = y[P]);
    } else u = y;
    return y = u.ref, {
      $$typeof: R,
      type: k,
      key: h,
      ref: y !== void 0 ? y : null,
      props: u
    };
  }
  return re.Fragment = q, re.jsx = I, re.jsxs = I, re;
}
var oe = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ye;
function Ee() {
  return ye || (ye = 1, process.env.NODE_ENV !== "production" && function() {
    function R(e) {
      if (e == null) return null;
      if (typeof e == "function")
        return e.$$typeof === de ? null : e.displayName || e.name || null;
      if (typeof e == "string") return e;
      switch (e) {
        case $:
          return "Fragment";
        case b:
          return "Profiler";
        case U:
          return "StrictMode";
        case le:
          return "Suspense";
        case D:
          return "SuspenseList";
        case ke:
          return "Activity";
      }
      if (typeof e == "object")
        switch (typeof e.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), e.$$typeof) {
          case ce:
            return "Portal";
          case ge:
            return (e.displayName || "Context") + ".Provider";
          case W:
            return (e._context.displayName || "Context") + ".Consumer";
          case Ne:
            var i = e.render;
            return e = e.displayName, e || (e = i.displayName || i.name || "", e = e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef"), e;
          case X:
            return i = e.displayName || null, i !== null ? i : R(e.type) || "Memo";
          case Z:
            i = e._payload, e = e._init;
            try {
              return R(e(i));
            } catch {
            }
        }
      return null;
    }
    function q(e) {
      return "" + e;
    }
    function I(e) {
      try {
        q(e);
        var i = !1;
      } catch {
        i = !0;
      }
      if (i) {
        i = console;
        var d = i.error, m = typeof Symbol == "function" && Symbol.toStringTag && e[Symbol.toStringTag] || e.constructor.name || "Object";
        return d.call(
          i,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          m
        ), q(e);
      }
    }
    function k(e) {
      if (e === $) return "<>";
      if (typeof e == "object" && e !== null && e.$$typeof === Z)
        return "<...>";
      try {
        var i = R(e);
        return i ? "<" + i + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function y() {
      var e = z.A;
      return e === null ? null : e.getOwner();
    }
    function u() {
      return Error("react-stack-top-frame");
    }
    function h(e) {
      if (Q.call(e, "key")) {
        var i = Object.getOwnPropertyDescriptor(e, "key").get;
        if (i && i.isReactWarning) return !1;
      }
      return e.key !== void 0;
    }
    function P(e, i) {
      function d() {
        ee || (ee = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          i
        ));
      }
      d.isReactWarning = !0, Object.defineProperty(e, "key", {
        get: d,
        configurable: !0
      });
    }
    function he() {
      var e = R(this.type);
      return te[e] || (te[e] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), e = this.props.ref, e !== void 0 ? e : null;
    }
    function T(e, i, d, m, _, j, V, ne) {
      return d = j.ref, e = {
        $$typeof: ie,
        type: e,
        key: i,
        props: j,
        _owner: _
      }, (d !== void 0 ? d : null) !== null ? Object.defineProperty(e, "ref", {
        enumerable: !1,
        get: he
      }) : Object.defineProperty(e, "ref", { enumerable: !1, value: null }), e._store = {}, Object.defineProperty(e._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(e, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(e, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: V
      }), Object.defineProperty(e, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: ne
      }), Object.freeze && (Object.freeze(e.props), Object.freeze(e)), e;
    }
    function A(e, i, d, m, _, j, V, ne) {
      var p = i.children;
      if (p !== void 0)
        if (m)
          if (L(p)) {
            for (m = 0; m < p.length; m++)
              H(p[m]);
            Object.freeze && Object.freeze(p);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else H(p);
      if (Q.call(i, "key")) {
        p = R(e);
        var O = Object.keys(i).filter(function(n) {
          return n !== "key";
        });
        m = 0 < O.length ? "{key: someKey, " + O.join(": ..., ") + ": ...}" : "{key: someKey}", me[p + m] || (O = 0 < O.length ? "{" + O.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          m,
          p,
          O,
          p
        ), me[p + m] = !0);
      }
      if (p = null, d !== void 0 && (I(d), p = "" + d), h(i) && (I(i.key), p = "" + i.key), "key" in i) {
        d = {};
        for (var se in i)
          se !== "key" && (d[se] = i[se]);
      } else d = i;
      return p && P(
        d,
        typeof e == "function" ? e.displayName || e.name || "Unknown" : e
      ), T(
        e,
        p,
        j,
        _,
        y(),
        d,
        V,
        ne
      );
    }
    function H(e) {
      typeof e == "object" && e !== null && e.$$typeof === ie && e._store && (e._store.validated = 1);
    }
    var S = je, ie = Symbol.for("react.transitional.element"), ce = Symbol.for("react.portal"), $ = Symbol.for("react.fragment"), U = Symbol.for("react.strict_mode"), b = Symbol.for("react.profiler"), W = Symbol.for("react.consumer"), ge = Symbol.for("react.context"), Ne = Symbol.for("react.forward_ref"), le = Symbol.for("react.suspense"), D = Symbol.for("react.suspense_list"), X = Symbol.for("react.memo"), Z = Symbol.for("react.lazy"), ke = Symbol.for("react.activity"), de = Symbol.for("react.client.reference"), z = S.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, Q = Object.prototype.hasOwnProperty, L = Array.isArray, B = console.createTask ? console.createTask : function() {
      return null;
    };
    S = {
      "react-stack-bottom-frame": function(e) {
        return e();
      }
    };
    var ee, te = {}, K = S["react-stack-bottom-frame"].bind(
      S,
      u
    )(), ue = B(k(u)), me = {};
    oe.Fragment = $, oe.jsx = function(e, i, d, m, _) {
      var j = 1e4 > z.recentlyCreatedOwnerStacks++;
      return A(
        e,
        i,
        d,
        !1,
        m,
        _,
        j ? Error("react-stack-top-frame") : K,
        j ? B(k(e)) : ue
      );
    }, oe.jsxs = function(e, i, d, m, _) {
      var j = 1e4 > z.recentlyCreatedOwnerStacks++;
      return A(
        e,
        i,
        d,
        !0,
        m,
        _,
        j ? Error("react-stack-top-frame") : K,
        j ? B(k(e)) : ue
      );
    };
  }()), oe;
}
process.env.NODE_ENV === "production" ? ve.exports = xe() : ve.exports = Ee();
var a = ve.exports;
const Se = ({
  apiKey: R = "AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",
  className: q = "",
  style: I = {}
}) => {
  const [k, y] = w(""), [u, h] = w([]), [P, he] = w([]), [T, A] = w(!1), [H, S] = w(null), [ie, ce] = w(null), [$, U] = w([]), [b, W] = w({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    },
    generationRound: 0
  }), [ge, Ne] = w(!1), [le, D] = w(!1), [X, Z] = w([]), [ke, de] = w(!0), [z, Q] = w(!1), [L, B] = w(/* @__PURE__ */ new Set()), ee = (n) => n.toLowerCase().replace(/[^a-z0-9\s]/g, "").replace(/\s+/g, "").replace(/^the/, "").substring(0, 50), te = async (n) => {
    try {
      const s = await fetch(`https://dns.google/resolve?name=${n}&type=A`, {
        method: "GET",
        headers: {
          Accept: "application/json"
        }
      });
      if (!s.ok)
        return "error";
      const t = await s.json();
      return t.Answer && t.Answer.length > 0 ? "taken" : "available";
    } catch (s) {
      return console.warn("Domain check failed:", s), "error";
    }
  }, K = async (n) => {
    const s = [...n];
    for (let t = 0; t < s.length; t++) {
      const r = ee(s[t].name);
      s[t].suggestedDomain = r, s[t].domainStatus = "checking", B((o) => /* @__PURE__ */ new Set([...o, t]));
    }
    h(s);
    for (let t = 0; t < s.length; t++) {
      const r = `${s[t].suggestedDomain}.com`;
      try {
        const o = await te(r);
        h((c) => {
          const l = [...c];
          return l[t] && (l[t].domainStatus = o), l;
        });
      } catch {
        h((c) => {
          const l = [...c];
          return l[t] && (l[t].domainStatus = "error"), l;
        });
      } finally {
        B((o) => {
          const c = new Set(o);
          return c.delete(t), c;
        });
      }
    }
  }, ue = (n, s) => {
    if (n.length === 0) return null;
    const t = n.map((l) => l.name.split(" ").length), r = s.map((l) => l.name.split(" ").length), o = t.reduce((l, C) => l + C, 0) / t.length, c = r.length > 0 ? r.reduce((l, C) => l + C, 0) / r.length : 0;
    return o <= 2 && c > 2 ? "short" : o <= 4 && (c <= 2 || c > 4) ? "medium" : o > 4 && c <= 4 ? "long" : o <= 2 ? "short" : o <= 4 ? "medium" : "long";
  }, me = (n, s) => {
    if (n.length === 0) return null;
    const t = n.map((o) => o.description.toLowerCase()).join(" "), r = s.map((o) => o.description.toLowerCase()).join(" ");
    return (t.includes("professional") || t.includes("business")) && !r.includes("professional") && !r.includes("business") ? "professional" : (t.includes("creative") || t.includes("unique")) && !r.includes("creative") && !r.includes("unique") ? "creative" : (t.includes("fun") || t.includes("playful")) && !r.includes("fun") && !r.includes("playful") ? "playful" : "descriptive";
  }, e = (n) => {
    const s = [];
    n.forEach((r) => {
      const o = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], c = r.name.toLowerCase().split(/\s+/).filter(
        (l) => l.length > 2 && !o.includes(l)
      );
      s.push(...c);
    });
    const t = {};
    return s.forEach((r) => t[r] = (t[r] || 0) + 1), Object.entries(t).sort(([, r], [, o]) => o - r).slice(0, 5).map(([r]) => r);
  }, i = (n) => {
    const s = n.filter((r) => r.liked === !0), t = n.filter((r) => r.liked === !1);
    return {
      preferredLength: ue(s, t),
      preferredStyle: me(s, t),
      likedKeywords: e(s),
      dislikedKeywords: e(t),
      preferredStructure: null
      // Simplified for now
    };
  }, d = (n, s) => {
    const t = `Create 4 high-converting and catchy podcast names based on the following user input: ${n}.`;
    let r = "";
    if (s.patterns.preferredLength) {
      const o = {
        short: "1-2 words",
        medium: "3-4 words",
        long: "5+ words"
      };
      r += `Focus on ${s.patterns.preferredLength} names (${o[s.patterns.preferredLength]}). `;
    }
    if (s.patterns.preferredStyle && (r += `Use a ${s.patterns.preferredStyle} style. `), s.patterns.likedKeywords.length > 0 && (r += `Incorporate concepts similar to: ${s.patterns.likedKeywords.join(", ")}. `), s.patterns.dislikedKeywords.length > 0 && (r += `Avoid concepts like: ${s.patterns.dislikedKeywords.join(", ")}. `), s.likedNames.length > 0) {
      const o = s.likedNames.slice(-2).map((c) => c.name).join('", "');
      r += `Generate names with similar appeal to: "${o}". `;
    }
    return `${t} ${r} Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`;
  }, m = async (n = !1) => {
    if (!k.trim()) {
      S("Please describe what your podcast is about");
      return;
    }
    A(!0), S(null), h([]), n ? D(!0) : (U([]), Ne(!1), D(!1), de(!0), Q(!1));
    try {
      const s = n ? d(k, b) : `Create 4 high-converting and catchy podcast names based on the following user input: ${k}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 4 names are creative, memorable, and relevant to the input topic.`, t = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${R}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: s
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!t.ok)
        throw new Error(`API request failed: ${t.status} ${t.statusText}`);
      const r = await t.json();
      if (!r.candidates || !r.candidates[0] || !r.candidates[0].content)
        throw new Error("Invalid response format from API");
      const c = r.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!c)
        throw new Error("No valid JSON found in API response");
      const l = JSON.parse(c[0]);
      if (!l.podcast_names || !Array.isArray(l.podcast_names))
        throw new Error("Invalid response structure");
      h(l.podcast_names), K(l.podcast_names), Z((x) => [...x, l.podcast_names]), W((x) => ({
        ...x,
        generationRound: x.generationRound + 1
      }));
      const C = l.podcast_names.map((x, F) => ({
        name: x.name,
        description: x.description,
        liked: null,
        timestamp: Date.now(),
        index: F
      }));
      U(C);
    } catch (s) {
      console.error("Error generating podcast names:", s), S(s instanceof Error ? s.message : "An unexpected error occurred");
    } finally {
      A(!1), D(!1);
    }
  }, _ = async (n, s) => {
    try {
      await navigator.clipboard.writeText(n), ce(s), setTimeout(() => ce(null), 2e3);
    } catch (t) {
      console.error("Failed to copy text:", t);
    }
  }, j = async (n, s) => {
    const t = u[n];
    t && (s ? (he((r) => r.find((o) => o.name === t.name) ? r : [...r, t]), h((r) => r.filter((o, c) => c !== n)), W((r) => {
      const o = { ...r };
      return o.dislikedNames = o.dislikedNames.filter((c) => c.name !== t.name), o.likedNames.find((c) => c.name === t.name) || o.likedNames.push({
        name: t.name,
        description: t.description,
        liked: !0,
        timestamp: Date.now(),
        index: n
      }), o.patterns = i([...o.likedNames, ...o.dislikedNames]), o;
    }), k.trim() && V()) : (h((r) => r.filter((o, c) => c !== n)), W((r) => {
      const o = { ...r };
      return o.likedNames = o.likedNames.filter((c) => c.name !== t.name), o.dislikedNames.find((c) => c.name === t.name) || o.dislikedNames.push({
        name: t.name,
        description: t.description,
        liked: !1,
        timestamp: Date.now(),
        index: n
      }), o.patterns = i([...o.likedNames, ...o.dislikedNames]), o;
    }), k.trim() && V()), z || (Q(!0), de(!1)));
  }, V = async () => {
    var n, s, t, r, o;
    try {
      A(!0);
      const c = d(k, b), l = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=" + R, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: c.replace("Create 4", "Create 1")
              // Only need 1 new suggestion
            }]
          }]
        })
      });
      if (!l.ok)
        throw new Error(`API request failed: ${l.status}`);
      const x = (o = (r = (t = (s = (n = (await l.json()).candidates) == null ? void 0 : n[0]) == null ? void 0 : s.content) == null ? void 0 : t.parts) == null ? void 0 : r[0]) == null ? void 0 : o.text;
      if (!x)
        throw new Error("No content received from API");
      const F = x.match(/\{[\s\S]*\}/);
      if (!F)
        throw new Error("No valid JSON found in response");
      const J = JSON.parse(F[0]);
      if (!J.podcast_names || !Array.isArray(J.podcast_names) || J.podcast_names.length === 0)
        throw new Error("Invalid response format");
      const E = J.podcast_names[0];
      h((pe) => [...pe, E]);
      const fe = ee(E.name), be = await te(fe);
      h((pe) => {
        const Y = [...pe], g = Y.length - 1;
        return Y[g] = {
          ...Y[g],
          suggestedDomain: fe,
          domainStatus: be
        }, Y;
      });
    } catch (c) {
      console.error("Error generating new suggestion:", c), S("Failed to generate new suggestion. Please try again.");
    } finally {
      A(!1);
    }
  }, ne = () => {
    k.trim() && p(k);
  }, p = async (n) => {
    var s, t, r, o, c, l, C, x, F, J;
    A(!0), S(""), D(!0);
    try {
      const E = u.filter((g, f) => {
        const N = $.find((v) => v.index === f);
        return (N == null ? void 0 : N.liked) === !0;
      }), fe = u.filter((g, f) => {
        const N = $.find((v) => v.index === f);
        return (N == null ? void 0 : N.liked) === !1;
      }).length, be = Math.max(1, fe), Y = Math.min(5, E.length + be) - E.length;
      if (Y <= 0) {
        const g = d(n, b), f = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=" + R, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: g.replace("Create 5", "Create 1")
              }]
            }]
          })
        });
        if (!f.ok)
          throw new Error(`API request failed: ${f.status}`);
        const v = (c = (o = (r = (t = (s = (await f.json()).candidates) == null ? void 0 : s[0]) == null ? void 0 : t.content) == null ? void 0 : r.parts) == null ? void 0 : o[0]) == null ? void 0 : c.text;
        if (!v)
          throw new Error("No content received from API");
        const M = v.match(/\{[\s\S]*\}/);
        if (!M)
          throw new Error("No valid JSON found in response");
        const G = JSON.parse(M[0]);
        if (!G.podcast_names || !Array.isArray(G.podcast_names))
          throw new Error("Invalid response format");
        const ae = [...E, ...G.podcast_names].slice(0, 5);
        h(ae), K(ae);
      } else {
        const g = d(n, b), f = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=" + R, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: g.replace("Create 5", `Create ${Y}`)
              }]
            }]
          })
        });
        if (!f.ok)
          throw new Error(`API request failed: ${f.status}`);
        const v = (J = (F = (x = (C = (l = (await f.json()).candidates) == null ? void 0 : l[0]) == null ? void 0 : C.content) == null ? void 0 : x.parts) == null ? void 0 : F[0]) == null ? void 0 : J.text;
        if (!v)
          throw new Error("No content received from API");
        const M = v.match(/\{[\s\S]*\}/);
        if (!M)
          throw new Error("No valid JSON found in response");
        const G = JSON.parse(M[0]);
        if (!G.podcast_names || !Array.isArray(G.podcast_names))
          throw new Error("Invalid response format");
        const ae = [...E, ...G.podcast_names];
        h(ae), K(ae);
      }
      Z((g) => [...g, u]), W((g) => ({
        ...g,
        generationRound: g.generationRound + 1
      })), U((g) => g.filter((f) => {
        const N = u[f.index];
        return E.some((v) => v.name === (N == null ? void 0 : N.name));
      })), U((g) => g.map((f) => {
        const N = u[f.index], v = E.findIndex((M) => M.name === (N == null ? void 0 : N.name));
        return v >= 0 ? { ...f, index: v } : f;
      }).filter((f) => f.index >= 0));
    } catch (E) {
      console.error("Error generating refined names:", E), S(E instanceof Error ? E.message : "Failed to generate refined names. Please try again.");
    } finally {
      A(!1), D(!1);
    }
  }, O = (n) => {
    n.preventDefault(), m();
  }, se = (n) => {
    n.key === "Enter" && !n.shiftKey && (n.preventDefault(), m());
  };
  return /* @__PURE__ */ a.jsx("div", { className: `podcast-name-generator ${q}`, style: I, children: /* @__PURE__ */ a.jsxs("div", { className: "generator-container", children: [
    /* @__PURE__ */ a.jsx("h2", { className: "generator-title", children: "Podcast Name Generator" }),
    /* @__PURE__ */ a.jsx("p", { className: "generator-subtitle", children: "Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI" }),
    ke && /* @__PURE__ */ a.jsx("div", { className: "onboarding-banner", children: /* @__PURE__ */ a.jsxs("div", { className: "onboarding-content", children: [
      /* @__PURE__ */ a.jsx("span", { className: "onboarding-icon", children: "💡" }),
      /* @__PURE__ */ a.jsxs("div", { className: "onboarding-text", children: [
        /* @__PURE__ */ a.jsx("strong", { children: "Smart AI Learning:" }),
        " The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."
      ] })
    ] }) }),
    /* @__PURE__ */ a.jsx("form", { onSubmit: O, className: "input-form", children: /* @__PURE__ */ a.jsxs("div", { className: "input-container", children: [
      /* @__PURE__ */ a.jsx(
        "textarea",
        {
          value: k,
          onChange: (n) => y(n.target.value),
          onKeyPress: se,
          placeholder: "Describe what your podcast is about",
          className: "input-field",
          rows: 3,
          disabled: T
        }
      ),
      /* @__PURE__ */ a.jsx(
        "button",
        {
          type: "submit",
          disabled: T || !k.trim(),
          className: "generate-button",
          children: T ? "Generating..." : "Generate Names"
        }
      )
    ] }) }),
    H && /* @__PURE__ */ a.jsxs("div", { className: "error-message", children: [
      /* @__PURE__ */ a.jsx("span", { className: "error-icon", children: "⚠️" }),
      H
    ] }),
    T && /* @__PURE__ */ a.jsxs("div", { className: "loading-container", children: [
      /* @__PURE__ */ a.jsx("div", { className: "loading-spinner" }),
      /* @__PURE__ */ a.jsx("p", { children: le ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
    ] }),
    u.length > 0 && /* @__PURE__ */ a.jsxs("div", { className: "results-container", children: [
      /* @__PURE__ */ a.jsxs("h3", { className: "results-title", children: [
        b.generationRound > 1 ? "Refined Podcast Name Suggestions" : "Your Podcast Name Suggestions",
        X.length > 1 && /* @__PURE__ */ a.jsxs("span", { className: "generation-counter", children: [
          " (Round ",
          b.generationRound,
          ")"
        ] })
      ] }),
      b.generationRound > 1 && /* @__PURE__ */ a.jsxs("p", { className: "refinement-info", children: [
        "✨ These names are tailored based on your preferences from ",
        X.length - 1,
        " previous round",
        X.length > 2 ? "s" : ""
      ] }),
      b.generationRound === 1 && !z && /* @__PURE__ */ a.jsx("div", { className: "feedback-hint", children: /* @__PURE__ */ a.jsxs("div", { className: "feedback-hint-content", children: [
        /* @__PURE__ */ a.jsx("span", { className: "feedback-hint-icon", children: "👆" }),
        /* @__PURE__ */ a.jsxs("p", { children: [
          /* @__PURE__ */ a.jsx("strong", { children: "Like what you see?" }),
          " Use the 👍👎 buttons below! Liked names move to favorites, disliked names disappear and get replaced with better AI suggestions!"
        ] })
      ] }) }),
      P.length > 0 && /* @__PURE__ */ a.jsxs("div", { className: "favorites-section", children: [
        /* @__PURE__ */ a.jsxs("div", { className: "favorites-header", children: [
          /* @__PURE__ */ a.jsxs("h3", { children: [
            "⭐ Favoured Podcast Names (",
            P.length,
            ")"
          ] }),
          /* @__PURE__ */ a.jsx("p", { className: "favorites-subtitle", children: "Names you loved - the AI is learning from these to suggest better options!" })
        ] }),
        /* @__PURE__ */ a.jsx("div", { className: "favorites-grid", children: P.map((n, s) => /* @__PURE__ */ a.jsxs("div", { className: "favorite-card", children: [
          /* @__PURE__ */ a.jsxs("div", { className: "favorite-content", children: [
            /* @__PURE__ */ a.jsx("h4", { className: "favorite-name", children: n.name }),
            /* @__PURE__ */ a.jsx("p", { className: "favorite-description", children: n.description }),
            n.suggestedDomain && /* @__PURE__ */ a.jsxs("div", { className: "domain-info inline", children: [
              /* @__PURE__ */ a.jsx("span", { className: "domain-label", children: "Domain:" }),
              /* @__PURE__ */ a.jsx("span", { className: "domain-name", children: n.suggestedDomain }),
              /* @__PURE__ */ a.jsx("span", { className: `domain-status ${n.domainStatus}`, children: n.domainStatus === "available" ? "✅ Available" : n.domainStatus === "taken" ? "❌ Taken" : n.domainStatus === "error" ? "⚠️ Check manually" : "🔍 Checking..." })
            ] })
          ] }),
          /* @__PURE__ */ a.jsx("div", { className: "favorite-actions", children: /* @__PURE__ */ a.jsx(
            "button",
            {
              onClick: () => _(n.name, -1),
              className: "copy-button small",
              title: "Copy to clipboard",
              children: "📋 Copy"
            }
          ) })
        ] }, `fav-${s}`)) })
      ] }),
      /* @__PURE__ */ a.jsxs("div", { className: "suggestions-section", children: [
        /* @__PURE__ */ a.jsxs("div", { className: "suggestions-header", children: [
          /* @__PURE__ */ a.jsxs("h3", { children: [
            "🎯 Current Suggestions (",
            u.length,
            "/4)"
          ] }),
          /* @__PURE__ */ a.jsx("p", { className: "suggestions-subtitle", children: b.likedNames.length > 0 || b.dislikedNames.length > 0 ? `AI is learning from your ${b.likedNames.length} likes and ${b.dislikedNames.length} dislikes to improve these suggestions!` : "Rate these suggestions to help the AI learn your preferences and generate better names!" })
        ] }),
        /* @__PURE__ */ a.jsx("div", { className: "results-grid", children: u.map((n, s) => {
          const t = $.find((c) => c.index === s), r = (t == null ? void 0 : t.liked) === !0, o = (t == null ? void 0 : t.liked) === !1;
          return /* @__PURE__ */ a.jsxs(
            "div",
            {
              className: `result-card ${r ? "liked" : ""} ${o ? "disliked" : ""}`,
              children: [
                /* @__PURE__ */ a.jsxs("div", { className: "result-header", children: [
                  /* @__PURE__ */ a.jsxs("h4", { className: "result-name", children: [
                    n.name,
                    r && b.generationRound > 1 && /* @__PURE__ */ a.jsx("span", { className: "kept-indicator", title: "This name was kept from your previous selection", children: "⭐" })
                  ] }),
                  /* @__PURE__ */ a.jsxs("div", { className: "result-actions", children: [
                    /* @__PURE__ */ a.jsxs("div", { className: "feedback-buttons", children: [
                      /* @__PURE__ */ a.jsx(
                        "button",
                        {
                          onClick: () => j(s, !0),
                          className: `feedback-button like-button ${r ? "active" : ""}`,
                          title: "I like this name",
                          disabled: T,
                          children: "👍"
                        }
                      ),
                      /* @__PURE__ */ a.jsx(
                        "button",
                        {
                          onClick: () => j(s, !1),
                          className: `feedback-button dislike-button ${o ? "active" : ""} ${L.has(s) ? "loading" : ""}`,
                          title: L.has(s) ? "Generating replacement..." : "I don't like this name",
                          disabled: T || L.has(s),
                          children: L.has(s) ? "🔄" : "👎"
                        }
                      )
                    ] }),
                    /* @__PURE__ */ a.jsx(
                      "button",
                      {
                        onClick: () => _(n.name, s),
                        className: "copy-button",
                        title: "Copy podcast name",
                        children: ie === s ? "✓ Copied!" : "📋 Copy"
                      }
                    )
                  ] })
                ] }),
                /* @__PURE__ */ a.jsx("p", { className: "result-description", children: n.description }),
                n.suggestedDomain && /* @__PURE__ */ a.jsxs("div", { className: "domain-info inline", children: [
                  /* @__PURE__ */ a.jsx("span", { className: "domain-label", children: "Domain:" }),
                  /* @__PURE__ */ a.jsxs("code", { className: "domain-text", children: [
                    n.suggestedDomain,
                    ".com"
                  ] }),
                  /* @__PURE__ */ a.jsxs("span", { className: `domain-status ${n.domainStatus}`, children: [
                    (n.domainStatus === "checking" || L.has(s)) && "⏳ Checking...",
                    n.domainStatus === "available" && "✅ Available",
                    n.domainStatus === "taken" && "❌ Taken",
                    n.domainStatus === "error" && "⚠️ Check manually"
                  ] })
                ] })
              ]
            },
            s
          );
        }) })
      ] }),
      ge && !T && /* @__PURE__ */ a.jsxs("div", { className: "refinement-section", children: [
        /* @__PURE__ */ a.jsx("div", { className: "refinement-info", children: /* @__PURE__ */ a.jsx("p", { children: "💡 I'll keep your liked names and replace the disliked ones with better suggestions!" }) }),
        /* @__PURE__ */ a.jsx(
          "button",
          {
            onClick: ne,
            className: "refinement-button",
            disabled: T,
            children: le ? "🔄 Refining..." : "🎯 Replace Disliked Names"
          }
        )
      ] })
    ] })
  ] }) });
};
export {
  Se as default
};

import te, { useState as h } from "react";
var X = { exports: {} }, A = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Z;
function re() {
  if (Z) return A;
  Z = 1;
  var g = Symbol.for("react.transitional.element"), N = Symbol.for("react.fragment");
  function v(f, u, m) {
    var k = null;
    if (m !== void 0 && (k = "" + m), u.key !== void 0 && (k = "" + u.key), "key" in u) {
      m = {};
      for (var d in u)
        d !== "key" && (m[d] = u[d]);
    } else m = u;
    return u = m.ref, {
      $$typeof: g,
      type: f,
      key: k,
      ref: u !== void 0 ? u : null,
      props: m
    };
  }
  return A.Fragment = N, A.jsx = v, A.jsxs = v, A;
}
var C = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Q;
function ne() {
  return Q || (Q = 1, process.env.NODE_ENV !== "production" && function() {
    function g(e) {
      if (e == null) return null;
      if (typeof e == "function")
        return e.$$typeof === B ? null : e.displayName || e.name || null;
      if (typeof e == "string") return e;
      switch (e) {
        case b:
          return "Fragment";
        case U:
          return "Profiler";
        case D:
          return "StrictMode";
        case S:
          return "Suspense";
        case K:
          return "SuspenseList";
        case G:
          return "Activity";
      }
      if (typeof e == "object")
        switch (typeof e.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), e.$$typeof) {
          case T:
            return "Portal";
          case z:
            return (e.displayName || "Context") + ".Provider";
          case F:
            return (e._context.displayName || "Context") + ".Consumer";
          case _:
            var t = e.render;
            return e = e.displayName, e || (e = t.displayName || t.name || "", e = e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef"), e;
          case q:
            return t = e.displayName || null, t !== null ? t : g(e.type) || "Memo";
          case Y:
            t = e._payload, e = e._init;
            try {
              return g(e(t));
            } catch {
            }
        }
      return null;
    }
    function N(e) {
      return "" + e;
    }
    function v(e) {
      try {
        N(e);
        var t = !1;
      } catch {
        t = !0;
      }
      if (t) {
        t = console;
        var r = t.error, o = typeof Symbol == "function" && Symbol.toStringTag && e[Symbol.toStringTag] || e.constructor.name || "Object";
        return r.call(
          t,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          o
        ), N(e);
      }
    }
    function f(e) {
      if (e === b) return "<>";
      if (typeof e == "object" && e !== null && e.$$typeof === Y)
        return "<...>";
      try {
        var t = g(e);
        return t ? "<" + t + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function u() {
      var e = P.A;
      return e === null ? null : e.getOwner();
    }
    function m() {
      return Error("react-stack-top-frame");
    }
    function k(e) {
      if (E.call(e, "key")) {
        var t = Object.getOwnPropertyDescriptor(e, "key").get;
        if (t && t.isReactWarning) return !1;
      }
      return e.key !== void 0;
    }
    function d(e, t) {
      function r() {
        M || (M = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          t
        ));
      }
      r.isReactWarning = !0, Object.defineProperty(e, "key", {
        get: r,
        configurable: !0
      });
    }
    function $() {
      var e = g(this.type);
      return W[e] || (W[e] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), e = this.props.ref, e !== void 0 ? e : null;
    }
    function I(e, t, r, o, a, l, p, O) {
      return r = l.ref, e = {
        $$typeof: x,
        type: e,
        key: t,
        props: l,
        _owner: a
      }, (r !== void 0 ? r : null) !== null ? Object.defineProperty(e, "ref", {
        enumerable: !1,
        get: $
      }) : Object.defineProperty(e, "ref", { enumerable: !1, value: null }), e._store = {}, Object.defineProperty(e._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(e, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(e, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: p
      }), Object.defineProperty(e, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: O
      }), Object.freeze && (Object.freeze(e.props), Object.freeze(e)), e;
    }
    function w(e, t, r, o, a, l, p, O) {
      var c = t.children;
      if (c !== void 0)
        if (o)
          if (V(c)) {
            for (o = 0; o < c.length; o++)
              L(c[o]);
            Object.freeze && Object.freeze(c);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else L(c);
      if (E.call(t, "key")) {
        c = g(e);
        var j = Object.keys(t).filter(function(ee) {
          return ee !== "key";
        });
        o = 0 < j.length ? "{key: someKey, " + j.join(": ..., ") + ": ...}" : "{key: someKey}", n[c + o] || (j = 0 < j.length ? "{" + j.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          o,
          c,
          j,
          c
        ), n[c + o] = !0);
      }
      if (c = null, r !== void 0 && (v(r), c = "" + r), k(t) && (v(t.key), c = "" + t.key), "key" in t) {
        r = {};
        for (var H in t)
          H !== "key" && (r[H] = t[H]);
      } else r = t;
      return c && d(
        r,
        typeof e == "function" ? e.displayName || e.name || "Unknown" : e
      ), I(
        e,
        c,
        l,
        a,
        u(),
        r,
        p,
        O
      );
    }
    function L(e) {
      typeof e == "object" && e !== null && e.$$typeof === x && e._store && (e._store.validated = 1);
    }
    var y = te, x = Symbol.for("react.transitional.element"), T = Symbol.for("react.portal"), b = Symbol.for("react.fragment"), D = Symbol.for("react.strict_mode"), U = Symbol.for("react.profiler"), F = Symbol.for("react.consumer"), z = Symbol.for("react.context"), _ = Symbol.for("react.forward_ref"), S = Symbol.for("react.suspense"), K = Symbol.for("react.suspense_list"), q = Symbol.for("react.memo"), Y = Symbol.for("react.lazy"), G = Symbol.for("react.activity"), B = Symbol.for("react.client.reference"), P = y.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, E = Object.prototype.hasOwnProperty, V = Array.isArray, R = console.createTask ? console.createTask : function() {
      return null;
    };
    y = {
      "react-stack-bottom-frame": function(e) {
        return e();
      }
    };
    var M, W = {}, J = y["react-stack-bottom-frame"].bind(
      y,
      m
    )(), s = R(f(m)), n = {};
    C.Fragment = b, C.jsx = function(e, t, r, o, a) {
      var l = 1e4 > P.recentlyCreatedOwnerStacks++;
      return w(
        e,
        t,
        r,
        !1,
        o,
        a,
        l ? Error("react-stack-top-frame") : J,
        l ? R(f(e)) : s
      );
    }, C.jsxs = function(e, t, r, o, a) {
      var l = 1e4 > P.recentlyCreatedOwnerStacks++;
      return w(
        e,
        t,
        r,
        !0,
        o,
        a,
        l ? Error("react-stack-top-frame") : J,
        l ? R(f(e)) : s
      );
    };
  }()), C;
}
process.env.NODE_ENV === "production" ? X.exports = re() : X.exports = ne();
var i = X.exports;
const ae = ({
  apiKey: g = "AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",
  className: N = "",
  style: v = {}
}) => {
  const [f, u] = h(""), [m, k] = h([]), [d, $] = h(!1), [I, w] = h(null), [L, y] = h(null), [x, T] = h([]), [b, D] = h({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    },
    generationRound: 0
  }), [U, F] = h(!1), [z, _] = h(!1), [S, K] = h([]), q = (s, n) => {
    if (s.length === 0) return null;
    const e = s.map((a) => a.name.split(" ").length), t = n.map((a) => a.name.split(" ").length), r = e.reduce((a, l) => a + l, 0) / e.length, o = t.length > 0 ? t.reduce((a, l) => a + l, 0) / t.length : 0;
    return r <= 2 && o > 2 ? "short" : r <= 4 && (o <= 2 || o > 4) ? "medium" : r > 4 && o <= 4 ? "long" : r <= 2 ? "short" : r <= 4 ? "medium" : "long";
  }, Y = (s, n) => {
    if (s.length === 0) return null;
    const e = s.map((r) => r.description.toLowerCase()).join(" "), t = n.map((r) => r.description.toLowerCase()).join(" ");
    return (e.includes("professional") || e.includes("business")) && !t.includes("professional") && !t.includes("business") ? "professional" : (e.includes("creative") || e.includes("unique")) && !t.includes("creative") && !t.includes("unique") ? "creative" : (e.includes("fun") || e.includes("playful")) && !t.includes("fun") && !t.includes("playful") ? "playful" : "descriptive";
  }, G = (s) => {
    const n = [];
    s.forEach((t) => {
      const r = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], o = t.name.toLowerCase().split(/\s+/).filter(
        (a) => a.length > 2 && !r.includes(a)
      );
      n.push(...o);
    });
    const e = {};
    return n.forEach((t) => e[t] = (e[t] || 0) + 1), Object.entries(e).sort(([, t], [, r]) => r - t).slice(0, 5).map(([t]) => t);
  }, B = (s) => {
    const n = s.filter((t) => t.liked === !0), e = s.filter((t) => t.liked === !1);
    return {
      preferredLength: q(n, e),
      preferredStyle: Y(n, e),
      likedKeywords: G(n),
      dislikedKeywords: G(e),
      preferredStructure: null
      // Simplified for now
    };
  }, P = (s, n) => {
    const e = `Create 5 high-converting and catchy podcast names based on the following user input: ${s}.`;
    let t = "";
    if (n.patterns.preferredLength) {
      const r = {
        short: "1-2 words",
        medium: "3-4 words",
        long: "5+ words"
      };
      t += `Focus on ${n.patterns.preferredLength} names (${r[n.patterns.preferredLength]}). `;
    }
    if (n.patterns.preferredStyle && (t += `Use a ${n.patterns.preferredStyle} style. `), n.patterns.likedKeywords.length > 0 && (t += `Incorporate concepts similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (t += `Avoid concepts like: ${n.patterns.dislikedKeywords.join(", ")}. `), n.likedNames.length > 0) {
      const r = n.likedNames.slice(-2).map((o) => o.name).join('", "');
      t += `Generate names with similar appeal to: "${r}". `;
    }
    return `${e} ${t} Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`;
  }, E = async (s = !1) => {
    if (!f.trim()) {
      w("Please describe what your podcast is about");
      return;
    }
    $(!0), w(null), k([]), s ? _(!0) : (T([]), F(!1), _(!1));
    try {
      const n = s ? P(f, b) : `Create 5 high-converting and catchy podcast names based on the following user input: ${f}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`, e = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${g}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: n
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!e.ok)
        throw new Error(`API request failed: ${e.status} ${e.statusText}`);
      const t = await e.json();
      if (!t.candidates || !t.candidates[0] || !t.candidates[0].content)
        throw new Error("Invalid response format from API");
      const o = t.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!o)
        throw new Error("No valid JSON found in API response");
      const a = JSON.parse(o[0]);
      if (!a.podcast_names || !Array.isArray(a.podcast_names))
        throw new Error("Invalid response structure");
      k(a.podcast_names), K((p) => [...p, a.podcast_names]), D((p) => ({
        ...p,
        generationRound: p.generationRound + 1
      }));
      const l = a.podcast_names.map((p, O) => ({
        name: p.name,
        description: p.description,
        liked: null,
        timestamp: Date.now(),
        index: O
      }));
      T(l);
    } catch (n) {
      console.error("Error generating podcast names:", n), w(n instanceof Error ? n.message : "An unexpected error occurred");
    } finally {
      $(!1), _(!1);
    }
  }, V = async (s, n) => {
    try {
      await navigator.clipboard.writeText(s), y(n), setTimeout(() => y(null), 2e3);
    } catch (e) {
      console.error("Failed to copy text:", e);
    }
  }, R = (s, n) => {
    const e = [...x];
    e[s] = {
      ...e[s],
      liked: n,
      timestamp: Date.now()
    }, T(e);
    const t = { ...b }, r = e[s];
    n ? (t.dislikedNames = t.dislikedNames.filter((a) => a.name !== r.name), t.likedNames.find((a) => a.name === r.name) || t.likedNames.push(r)) : (t.likedNames = t.likedNames.filter((a) => a.name !== r.name), t.dislikedNames.find((a) => a.name === r.name) || t.dislikedNames.push(r)), t.patterns = B([...t.likedNames, ...t.dislikedNames]), D(t), t.likedNames.length + t.dislikedNames.length >= 2 && F(!0);
  }, M = () => {
    E(!0);
  }, W = (s) => {
    s.preventDefault(), E();
  }, J = (s) => {
    s.key === "Enter" && !s.shiftKey && (s.preventDefault(), E());
  };
  return /* @__PURE__ */ i.jsx("div", { className: `podcast-name-generator ${N}`, style: v, children: /* @__PURE__ */ i.jsxs("div", { className: "generator-container", children: [
    /* @__PURE__ */ i.jsx("h2", { className: "generator-title", children: "Podcast Name Generator" }),
    /* @__PURE__ */ i.jsx("p", { className: "generator-subtitle", children: "Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI" }),
    /* @__PURE__ */ i.jsx("form", { onSubmit: W, className: "input-form", children: /* @__PURE__ */ i.jsxs("div", { className: "input-container", children: [
      /* @__PURE__ */ i.jsx(
        "textarea",
        {
          value: f,
          onChange: (s) => u(s.target.value),
          onKeyPress: J,
          placeholder: "Describe what your podcast is about",
          className: "input-field",
          rows: 3,
          disabled: d
        }
      ),
      /* @__PURE__ */ i.jsx(
        "button",
        {
          type: "submit",
          disabled: d || !f.trim(),
          className: "generate-button",
          children: d ? "Generating..." : "Generate Names"
        }
      )
    ] }) }),
    I && /* @__PURE__ */ i.jsxs("div", { className: "error-message", children: [
      /* @__PURE__ */ i.jsx("span", { className: "error-icon", children: "⚠️" }),
      I
    ] }),
    d && /* @__PURE__ */ i.jsxs("div", { className: "loading-container", children: [
      /* @__PURE__ */ i.jsx("div", { className: "loading-spinner" }),
      /* @__PURE__ */ i.jsx("p", { children: z ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
    ] }),
    m.length > 0 && /* @__PURE__ */ i.jsxs("div", { className: "results-container", children: [
      /* @__PURE__ */ i.jsxs("h3", { className: "results-title", children: [
        b.generationRound > 1 ? "Refined Podcast Name Suggestions" : "Your Podcast Name Suggestions",
        S.length > 1 && /* @__PURE__ */ i.jsxs("span", { className: "generation-counter", children: [
          " (Round ",
          b.generationRound,
          ")"
        ] })
      ] }),
      b.generationRound > 1 && /* @__PURE__ */ i.jsxs("p", { className: "refinement-info", children: [
        "✨ These names are tailored based on your preferences from ",
        S.length - 1,
        " previous round",
        S.length > 2 ? "s" : ""
      ] }),
      /* @__PURE__ */ i.jsx("div", { className: "results-grid", children: m.map((s, n) => {
        const e = x.find((o) => o.index === n), t = (e == null ? void 0 : e.liked) === !0, r = (e == null ? void 0 : e.liked) === !1;
        return /* @__PURE__ */ i.jsxs(
          "div",
          {
            className: `result-card ${t ? "liked" : ""} ${r ? "disliked" : ""}`,
            children: [
              /* @__PURE__ */ i.jsxs("div", { className: "result-header", children: [
                /* @__PURE__ */ i.jsx("h4", { className: "result-name", children: s.name }),
                /* @__PURE__ */ i.jsxs("div", { className: "result-actions", children: [
                  /* @__PURE__ */ i.jsxs("div", { className: "feedback-buttons", children: [
                    /* @__PURE__ */ i.jsx(
                      "button",
                      {
                        onClick: () => R(n, !0),
                        className: `feedback-button like-button ${t ? "active" : ""}`,
                        title: "I like this name",
                        disabled: d,
                        children: "👍"
                      }
                    ),
                    /* @__PURE__ */ i.jsx(
                      "button",
                      {
                        onClick: () => R(n, !1),
                        className: `feedback-button dislike-button ${r ? "active" : ""}`,
                        title: "I don't like this name",
                        disabled: d,
                        children: "👎"
                      }
                    )
                  ] }),
                  /* @__PURE__ */ i.jsx(
                    "button",
                    {
                      onClick: () => V(s.name, n),
                      className: "copy-button",
                      title: "Copy podcast name",
                      children: L === n ? "✓ Copied!" : "📋 Copy"
                    }
                  )
                ] })
              ] }),
              /* @__PURE__ */ i.jsx("p", { className: "result-description", children: s.description })
            ]
          },
          n
        );
      }) }),
      U && !d && /* @__PURE__ */ i.jsxs("div", { className: "refinement-section", children: [
        /* @__PURE__ */ i.jsx("div", { className: "refinement-info", children: /* @__PURE__ */ i.jsx("p", { children: "💡 Based on your feedback, I can generate better names that match your preferences!" }) }),
        /* @__PURE__ */ i.jsx(
          "button",
          {
            onClick: M,
            className: "refinement-button",
            disabled: d,
            children: "🎯 Generate Better Names"
          }
        )
      ] })
    ] })
  ] }) });
};
export {
  ae as default
};

import oe, { useState as p } from "react";
var ne = { exports: {} }, L = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var re;
function ie() {
  if (re) return L;
  re = 1;
  var N = Symbol.for("react.transitional.element"), E = Symbol.for("react.fragment");
  function x(b, f, h) {
    var k = null;
    if (h !== void 0 && (k = "" + h), f.key !== void 0 && (k = "" + f.key), "key" in f) {
      h = {};
      for (var m in f)
        m !== "key" && (h[m] = f[m]);
    } else h = f;
    return f = h.ref, {
      $$typeof: N,
      type: b,
      key: k,
      ref: f !== void 0 ? f : null,
      props: h
    };
  }
  return L.Fragment = E, L.jsx = x, L.jsxs = x, L;
}
var Y = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var se;
function ce() {
  return se || (se = 1, process.env.NODE_ENV !== "production" && function() {
    function N(e) {
      if (e == null) return null;
      if (typeof e == "function")
        return e.$$typeof === K ? null : e.displayName || e.name || null;
      if (typeof e == "string") return e;
      switch (e) {
        case v:
          return "Fragment";
        case X:
          return "Profiler";
        case U:
          return "StrictMode";
        case P:
          return "Suspense";
        case Q:
          return "SuspenseList";
        case J:
          return "Activity";
      }
      if (typeof e == "object")
        switch (typeof e.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), e.$$typeof) {
          case S:
            return "Portal";
          case Z:
            return (e.displayName || "Context") + ".Provider";
          case W:
            return (e._context.displayName || "Context") + ".Consumer";
          case _:
            var o = e.render;
            return e = e.displayName, e || (e = o.displayName || o.name || "", e = e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef"), e;
          case ee:
            return o = e.displayName || null, o !== null ? o : N(e.type) || "Memo";
          case A:
            o = e._payload, e = e._init;
            try {
              return N(e(o));
            } catch {
            }
        }
      return null;
    }
    function E(e) {
      return "" + e;
    }
    function x(e) {
      try {
        E(e);
        var o = !1;
      } catch {
        o = !0;
      }
      if (o) {
        o = console;
        var l = o.error, u = typeof Symbol == "function" && Symbol.toStringTag && e[Symbol.toStringTag] || e.constructor.name || "Object";
        return l.call(
          o,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          u
        ), E(e);
      }
    }
    function b(e) {
      if (e === v) return "<>";
      if (typeof e == "object" && e !== null && e.$$typeof === A)
        return "<...>";
      try {
        var o = N(e);
        return o ? "<" + o + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function f() {
      var e = O.A;
      return e === null ? null : e.getOwner();
    }
    function h() {
      return Error("react-stack-top-frame");
    }
    function k(e) {
      if (C.call(e, "key")) {
        var o = Object.getOwnPropertyDescriptor(e, "key").get;
        if (o && o.isReactWarning) return !1;
      }
      return e.key !== void 0;
    }
    function m(e, o) {
      function l() {
        q || (q = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          o
        ));
      }
      l.isReactWarning = !0, Object.defineProperty(e, "key", {
        get: l,
        configurable: !0
      });
    }
    function G() {
      var e = N(this.type);
      return B[e] || (B[e] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), e = this.props.ref, e !== void 0 ? e : null;
    }
    function M(e, o, l, u, j, g, I, a) {
      return l = g.ref, e = {
        $$typeof: T,
        type: e,
        key: o,
        props: g,
        _owner: j
      }, (l !== void 0 ? l : null) !== null ? Object.defineProperty(e, "ref", {
        enumerable: !1,
        get: G
      }) : Object.defineProperty(e, "ref", { enumerable: !1, value: null }), e._store = {}, Object.defineProperty(e._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(e, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(e, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: I
      }), Object.defineProperty(e, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: a
      }), Object.freeze && (Object.freeze(e.props), Object.freeze(e)), e;
    }
    function R(e, o, l, u, j, g, I, a) {
      var t = o.children;
      if (t !== void 0)
        if (u)
          if (te(t)) {
            for (u = 0; u < t.length; u++)
              z(t[u]);
            Object.freeze && Object.freeze(t);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else z(t);
      if (C.call(o, "key")) {
        t = N(e);
        var n = Object.keys(o).filter(function(i) {
          return i !== "key";
        });
        u = 0 < n.length ? "{key: someKey, " + n.join(": ..., ") + ": ...}" : "{key: someKey}", H[t + u] || (n = 0 < n.length ? "{" + n.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          u,
          t,
          n,
          t
        ), H[t + u] = !0);
      }
      if (t = null, l !== void 0 && (x(l), t = "" + l), k(o) && (x(o.key), t = "" + o.key), "key" in o) {
        l = {};
        for (var s in o)
          s !== "key" && (l[s] = o[s]);
      } else l = o;
      return t && m(
        l,
        typeof e == "function" ? e.displayName || e.name || "Unknown" : e
      ), M(
        e,
        t,
        g,
        j,
        f(),
        l,
        I,
        a
      );
    }
    function z(e) {
      typeof e == "object" && e !== null && e.$$typeof === T && e._store && (e._store.validated = 1);
    }
    var y = oe, T = Symbol.for("react.transitional.element"), S = Symbol.for("react.portal"), v = Symbol.for("react.fragment"), U = Symbol.for("react.strict_mode"), X = Symbol.for("react.profiler"), W = Symbol.for("react.consumer"), Z = Symbol.for("react.context"), _ = Symbol.for("react.forward_ref"), P = Symbol.for("react.suspense"), Q = Symbol.for("react.suspense_list"), ee = Symbol.for("react.memo"), A = Symbol.for("react.lazy"), J = Symbol.for("react.activity"), K = Symbol.for("react.client.reference"), O = y.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, C = Object.prototype.hasOwnProperty, te = Array.isArray, $ = console.createTask ? console.createTask : function() {
      return null;
    };
    y = {
      "react-stack-bottom-frame": function(e) {
        return e();
      }
    };
    var q, B = {}, V = y["react-stack-bottom-frame"].bind(
      y,
      h
    )(), D = $(b(h)), H = {};
    Y.Fragment = v, Y.jsx = function(e, o, l, u, j) {
      var g = 1e4 > O.recentlyCreatedOwnerStacks++;
      return R(
        e,
        o,
        l,
        !1,
        u,
        j,
        g ? Error("react-stack-top-frame") : V,
        g ? $(b(e)) : D
      );
    }, Y.jsxs = function(e, o, l, u, j) {
      var g = 1e4 > O.recentlyCreatedOwnerStacks++;
      return R(
        e,
        o,
        l,
        !0,
        u,
        j,
        g ? Error("react-stack-top-frame") : V,
        g ? $(b(e)) : D
      );
    };
  }()), Y;
}
process.env.NODE_ENV === "production" ? ne.exports = ie() : ne.exports = ce();
var r = ne.exports;
const de = ({
  apiKey: N = "AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",
  className: E = "",
  style: x = {}
}) => {
  const [b, f] = p(""), [h, k] = p([]), [m, G] = p(!1), [M, R] = p(null), [z, y] = p(null), [T, S] = p([]), [v, U] = p({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    },
    generationRound: 0
  }), [X, W] = p(!1), [Z, _] = p(!1), [P, Q] = p([]), [ee, A] = p(!0), [J, K] = p(!1), [O, C] = p(/* @__PURE__ */ new Set()), te = (a) => a.toLowerCase().replace(/[^a-z0-9\s]/g, "").replace(/\s+/g, "").replace(/^the/, "").substring(0, 50), $ = async (a) => {
    try {
      const t = await fetch(`https://dns.google/resolve?name=${a}&type=A`, {
        method: "GET",
        headers: {
          Accept: "application/json"
        }
      });
      if (!t.ok)
        return "error";
      const n = await t.json();
      return n.Answer && n.Answer.length > 0 ? "taken" : "available";
    } catch (t) {
      return console.warn("Domain check failed:", t), "error";
    }
  }, q = async (a) => {
    const t = [...a];
    for (let n = 0; n < t.length; n++) {
      const s = te(t[n].name);
      t[n].suggestedDomain = s, t[n].domainStatus = "checking", C((i) => /* @__PURE__ */ new Set([...i, n]));
    }
    k(t);
    for (let n = 0; n < t.length; n++) {
      const s = `${t[n].suggestedDomain}.com`;
      try {
        const i = await $(s);
        k((d) => {
          const c = [...d];
          return c[n] && (c[n].domainStatus = i), c;
        });
      } catch {
        k((d) => {
          const c = [...d];
          return c[n] && (c[n].domainStatus = "error"), c;
        });
      } finally {
        C((i) => {
          const d = new Set(i);
          return d.delete(n), d;
        });
      }
    }
  }, B = (a, t) => {
    if (a.length === 0) return null;
    const n = a.map((c) => c.name.split(" ").length), s = t.map((c) => c.name.split(" ").length), i = n.reduce((c, F) => c + F, 0) / n.length, d = s.length > 0 ? s.reduce((c, F) => c + F, 0) / s.length : 0;
    return i <= 2 && d > 2 ? "short" : i <= 4 && (d <= 2 || d > 4) ? "medium" : i > 4 && d <= 4 ? "long" : i <= 2 ? "short" : i <= 4 ? "medium" : "long";
  }, V = (a, t) => {
    if (a.length === 0) return null;
    const n = a.map((i) => i.description.toLowerCase()).join(" "), s = t.map((i) => i.description.toLowerCase()).join(" ");
    return (n.includes("professional") || n.includes("business")) && !s.includes("professional") && !s.includes("business") ? "professional" : (n.includes("creative") || n.includes("unique")) && !s.includes("creative") && !s.includes("unique") ? "creative" : (n.includes("fun") || n.includes("playful")) && !s.includes("fun") && !s.includes("playful") ? "playful" : "descriptive";
  }, D = (a) => {
    const t = [];
    a.forEach((s) => {
      const i = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], d = s.name.toLowerCase().split(/\s+/).filter(
        (c) => c.length > 2 && !i.includes(c)
      );
      t.push(...d);
    });
    const n = {};
    return t.forEach((s) => n[s] = (n[s] || 0) + 1), Object.entries(n).sort(([, s], [, i]) => i - s).slice(0, 5).map(([s]) => s);
  }, H = (a) => {
    const t = a.filter((s) => s.liked === !0), n = a.filter((s) => s.liked === !1);
    return {
      preferredLength: B(t, n),
      preferredStyle: V(t, n),
      likedKeywords: D(t),
      dislikedKeywords: D(n),
      preferredStructure: null
      // Simplified for now
    };
  }, e = (a, t) => {
    const n = `Create 5 high-converting and catchy podcast names based on the following user input: ${a}.`;
    let s = "";
    if (t.patterns.preferredLength) {
      const i = {
        short: "1-2 words",
        medium: "3-4 words",
        long: "5+ words"
      };
      s += `Focus on ${t.patterns.preferredLength} names (${i[t.patterns.preferredLength]}). `;
    }
    if (t.patterns.preferredStyle && (s += `Use a ${t.patterns.preferredStyle} style. `), t.patterns.likedKeywords.length > 0 && (s += `Incorporate concepts similar to: ${t.patterns.likedKeywords.join(", ")}. `), t.patterns.dislikedKeywords.length > 0 && (s += `Avoid concepts like: ${t.patterns.dislikedKeywords.join(", ")}. `), t.likedNames.length > 0) {
      const i = t.likedNames.slice(-2).map((d) => d.name).join('", "');
      s += `Generate names with similar appeal to: "${i}". `;
    }
    return `${n} ${s} Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`;
  }, o = async (a = !1) => {
    if (!b.trim()) {
      R("Please describe what your podcast is about");
      return;
    }
    G(!0), R(null), k([]), a ? _(!0) : (S([]), W(!1), _(!1), A(!0), K(!1));
    try {
      const t = a ? e(b, v) : `Create 5 high-converting and catchy podcast names based on the following user input: ${b}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`, n = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${N}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: t
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!n.ok)
        throw new Error(`API request failed: ${n.status} ${n.statusText}`);
      const s = await n.json();
      if (!s.candidates || !s.candidates[0] || !s.candidates[0].content)
        throw new Error("Invalid response format from API");
      const d = s.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!d)
        throw new Error("No valid JSON found in API response");
      const c = JSON.parse(d[0]);
      if (!c.podcast_names || !Array.isArray(c.podcast_names))
        throw new Error("Invalid response structure");
      k(c.podcast_names), q(c.podcast_names), Q((w) => [...w, c.podcast_names]), U((w) => ({
        ...w,
        generationRound: w.generationRound + 1
      }));
      const F = c.podcast_names.map((w, ae) => ({
        name: w.name,
        description: w.description,
        liked: null,
        timestamp: Date.now(),
        index: ae
      }));
      S(F);
    } catch (t) {
      console.error("Error generating podcast names:", t), R(t instanceof Error ? t.message : "An unexpected error occurred");
    } finally {
      G(!1), _(!1);
    }
  }, l = async (a, t) => {
    try {
      await navigator.clipboard.writeText(a), y(t), setTimeout(() => y(null), 2e3);
    } catch (n) {
      console.error("Failed to copy text:", n);
    }
  }, u = (a, t) => {
    const n = [...T];
    n[a] = {
      ...n[a],
      liked: t,
      timestamp: Date.now()
    }, S(n);
    const s = { ...v }, i = n[a];
    t ? (s.dislikedNames = s.dislikedNames.filter((c) => c.name !== i.name), s.likedNames.find((c) => c.name === i.name) || s.likedNames.push(i)) : (s.likedNames = s.likedNames.filter((c) => c.name !== i.name), s.dislikedNames.find((c) => c.name === i.name) || s.dislikedNames.push(i)), s.patterns = H([...s.likedNames, ...s.dislikedNames]), U(s), s.likedNames.length + s.dislikedNames.length >= 2 && W(!0), J || (K(!0), A(!1));
  }, j = () => {
    o(!0);
  }, g = (a) => {
    a.preventDefault(), o();
  }, I = (a) => {
    a.key === "Enter" && !a.shiftKey && (a.preventDefault(), o());
  };
  return /* @__PURE__ */ r.jsx("div", { className: `podcast-name-generator ${E}`, style: x, children: /* @__PURE__ */ r.jsxs("div", { className: "generator-container", children: [
    /* @__PURE__ */ r.jsx("h2", { className: "generator-title", children: "Podcast Name Generator" }),
    /* @__PURE__ */ r.jsx("p", { className: "generator-subtitle", children: "Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI" }),
    ee && /* @__PURE__ */ r.jsx("div", { className: "onboarding-banner", children: /* @__PURE__ */ r.jsxs("div", { className: "onboarding-content", children: [
      /* @__PURE__ */ r.jsx("span", { className: "onboarding-icon", children: "💡" }),
      /* @__PURE__ */ r.jsxs("div", { className: "onboarding-text", children: [
        /* @__PURE__ */ r.jsx("strong", { children: "Pro Tip:" }),
        " After generating names, use the 👍👎 buttons to teach the AI your preferences. It will then generate better, more personalized suggestions just for you!"
      ] })
    ] }) }),
    /* @__PURE__ */ r.jsx("form", { onSubmit: g, className: "input-form", children: /* @__PURE__ */ r.jsxs("div", { className: "input-container", children: [
      /* @__PURE__ */ r.jsx(
        "textarea",
        {
          value: b,
          onChange: (a) => f(a.target.value),
          onKeyPress: I,
          placeholder: "Describe what your podcast is about",
          className: "input-field",
          rows: 3,
          disabled: m
        }
      ),
      /* @__PURE__ */ r.jsx(
        "button",
        {
          type: "submit",
          disabled: m || !b.trim(),
          className: "generate-button",
          children: m ? "Generating..." : "Generate Names"
        }
      )
    ] }) }),
    M && /* @__PURE__ */ r.jsxs("div", { className: "error-message", children: [
      /* @__PURE__ */ r.jsx("span", { className: "error-icon", children: "⚠️" }),
      M
    ] }),
    m && /* @__PURE__ */ r.jsxs("div", { className: "loading-container", children: [
      /* @__PURE__ */ r.jsx("div", { className: "loading-spinner" }),
      /* @__PURE__ */ r.jsx("p", { children: Z ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
    ] }),
    h.length > 0 && /* @__PURE__ */ r.jsxs("div", { className: "results-container", children: [
      /* @__PURE__ */ r.jsxs("h3", { className: "results-title", children: [
        v.generationRound > 1 ? "Refined Podcast Name Suggestions" : "Your Podcast Name Suggestions",
        P.length > 1 && /* @__PURE__ */ r.jsxs("span", { className: "generation-counter", children: [
          " (Round ",
          v.generationRound,
          ")"
        ] })
      ] }),
      v.generationRound > 1 && /* @__PURE__ */ r.jsxs("p", { className: "refinement-info", children: [
        "✨ These names are tailored based on your preferences from ",
        P.length - 1,
        " previous round",
        P.length > 2 ? "s" : ""
      ] }),
      v.generationRound === 1 && !J && /* @__PURE__ */ r.jsx("div", { className: "feedback-hint", children: /* @__PURE__ */ r.jsxs("div", { className: "feedback-hint-content", children: [
        /* @__PURE__ */ r.jsx("span", { className: "feedback-hint-icon", children: "👆" }),
        /* @__PURE__ */ r.jsxs("p", { children: [
          /* @__PURE__ */ r.jsx("strong", { children: "Like what you see?" }),
          " Use the 👍👎 buttons below to rate each name. The AI will learn your style and generate even better suggestions!"
        ] })
      ] }) }),
      /* @__PURE__ */ r.jsx("div", { className: "results-grid", children: h.map((a, t) => {
        const n = T.find((d) => d.index === t), s = (n == null ? void 0 : n.liked) === !0, i = (n == null ? void 0 : n.liked) === !1;
        return /* @__PURE__ */ r.jsxs(
          "div",
          {
            className: `result-card ${s ? "liked" : ""} ${i ? "disliked" : ""}`,
            children: [
              /* @__PURE__ */ r.jsxs("div", { className: "result-header", children: [
                /* @__PURE__ */ r.jsx("h4", { className: "result-name", children: a.name }),
                /* @__PURE__ */ r.jsxs("div", { className: "result-actions", children: [
                  /* @__PURE__ */ r.jsxs("div", { className: "feedback-buttons", children: [
                    /* @__PURE__ */ r.jsx(
                      "button",
                      {
                        onClick: () => u(t, !0),
                        className: `feedback-button like-button ${s ? "active" : ""}`,
                        title: "I like this name",
                        disabled: m,
                        children: "👍"
                      }
                    ),
                    /* @__PURE__ */ r.jsx(
                      "button",
                      {
                        onClick: () => u(t, !1),
                        className: `feedback-button dislike-button ${i ? "active" : ""}`,
                        title: "I don't like this name",
                        disabled: m,
                        children: "👎"
                      }
                    )
                  ] }),
                  /* @__PURE__ */ r.jsx(
                    "button",
                    {
                      onClick: () => l(a.name, t),
                      className: "copy-button",
                      title: "Copy podcast name",
                      children: z === t ? "✓ Copied!" : "📋 Copy"
                    }
                  )
                ] })
              ] }),
              /* @__PURE__ */ r.jsx("p", { className: "result-description", children: a.description }),
              a.suggestedDomain && /* @__PURE__ */ r.jsxs("div", { className: "domain-info", children: [
                /* @__PURE__ */ r.jsxs("div", { className: "domain-name", children: [
                  /* @__PURE__ */ r.jsx("span", { className: "domain-label", children: "Domain:" }),
                  /* @__PURE__ */ r.jsxs("code", { className: "domain-text", children: [
                    a.suggestedDomain,
                    ".com"
                  ] })
                ] }),
                /* @__PURE__ */ r.jsxs("div", { className: `domain-status ${a.domainStatus}`, children: [
                  (a.domainStatus === "checking" || O.has(t)) && /* @__PURE__ */ r.jsxs(r.Fragment, { children: [
                    /* @__PURE__ */ r.jsx("span", { className: "domain-spinner", children: "⏳" }),
                    /* @__PURE__ */ r.jsx("span", { children: "Checking..." })
                  ] }),
                  a.domainStatus === "available" && /* @__PURE__ */ r.jsxs(r.Fragment, { children: [
                    /* @__PURE__ */ r.jsx("span", { className: "domain-icon available", children: "✅" }),
                    /* @__PURE__ */ r.jsx("span", { children: "Available" })
                  ] }),
                  a.domainStatus === "taken" && /* @__PURE__ */ r.jsxs(r.Fragment, { children: [
                    /* @__PURE__ */ r.jsx("span", { className: "domain-icon taken", children: "❌" }),
                    /* @__PURE__ */ r.jsx("span", { children: "Taken" })
                  ] }),
                  a.domainStatus === "error" && /* @__PURE__ */ r.jsxs(r.Fragment, { children: [
                    /* @__PURE__ */ r.jsx("span", { className: "domain-icon error", children: "⚠️" }),
                    /* @__PURE__ */ r.jsx("span", { children: "Check manually" })
                  ] })
                ] })
              ] })
            ]
          },
          t
        );
      }) }),
      X && !m && /* @__PURE__ */ r.jsxs("div", { className: "refinement-section", children: [
        /* @__PURE__ */ r.jsx("div", { className: "refinement-info", children: /* @__PURE__ */ r.jsx("p", { children: "💡 Based on your feedback, I can generate better names that match your preferences!" }) }),
        /* @__PURE__ */ r.jsx(
          "button",
          {
            onClick: j,
            className: "refinement-button",
            disabled: m,
            children: "🎯 Generate Better Names"
          }
        )
      ] })
    ] })
  ] }) });
};
export {
  de as default
};

import Re, { useState as y } from "react";
var xe = { exports: {} }, ie = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ke;
function Ce() {
  if (ke) return ie;
  ke = 1;
  var O = Symbol.for("react.transitional.element"), G = Symbol.for("react.fragment");
  function w(U, f, v) {
    var D = null;
    if (v !== void 0 && (D = "" + v), f.key !== void 0 && (D = "" + f.key), "key" in f) {
      v = {};
      for (var W in f)
        W !== "key" && (v[W] = f[W]);
    } else v = f;
    return f = v.ref, {
      $$typeof: O,
      type: U,
      key: D,
      ref: f !== void 0 ? f : null,
      props: v
    };
  }
  return ie.Fragment = G, ie.jsx = w, ie.jsxs = w, ie;
}
var le = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Se;
function Pe() {
  return Se || (Se = 1, process.env.NODE_ENV !== "production" && function() {
    function O(s) {
      if (s == null) return null;
      if (typeof s == "function")
        return s.$$typeof === Ne ? null : s.displayName || s.name || null;
      if (typeof s == "string") return s;
      switch (s) {
        case P:
          return "Fragment";
        case ce:
          return "Profiler";
        case J:
          return "StrictMode";
        case F:
          return "Suspense";
        case ve:
          return "SuspenseList";
        case K:
          return "Activity";
      }
      if (typeof s == "object")
        switch (typeof s.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), s.$$typeof) {
          case ee:
            return "Portal";
          case ge:
            return (s.displayName || "Context") + ".Provider";
          case pe:
            return (s._context.displayName || "Context") + ".Consumer";
          case de:
            var c = s.render;
            return s = s.displayName, s || (s = c.displayName || c.name || "", s = s !== "" ? "ForwardRef(" + s + ")" : "ForwardRef"), s;
          case me:
            return c = s.displayName || null, c !== null ? c : O(s.type) || "Memo";
          case te:
            c = s._payload, s = s._init;
            try {
              return O(s(c));
            } catch {
            }
        }
      return null;
    }
    function G(s) {
      return "" + s;
    }
    function w(s) {
      try {
        G(s);
        var c = !1;
      } catch {
        c = !0;
      }
      if (c) {
        c = console;
        var m = c.error, N = typeof Symbol == "function" && Symbol.toStringTag && s[Symbol.toStringTag] || s.constructor.name || "Object";
        return m.call(
          c,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          N
        ), G(s);
      }
    }
    function U(s) {
      if (s === P) return "<>";
      if (typeof s == "object" && s !== null && s.$$typeof === te)
        return "<...>";
      try {
        var c = O(s);
        return c ? "<" + c + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function f() {
      var s = q.A;
      return s === null ? null : s.getOwner();
    }
    function v() {
      return Error("react-stack-top-frame");
    }
    function D(s) {
      if (be.call(s, "key")) {
        var c = Object.getOwnPropertyDescriptor(s, "key").get;
        if (c && c.isReactWarning) return !1;
      }
      return s.key !== void 0;
    }
    function W(s, c) {
      function m() {
        se || (se = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          c
        ));
      }
      m.isReactWarning = !0, Object.defineProperty(s, "key", {
        get: m,
        configurable: !0
      });
    }
    function A() {
      var s = O(this.type);
      return ne[s] || (ne[s] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), s = this.props.ref, s !== void 0 ? s : null;
    }
    function z(s, c, m, N, _, R, V, X) {
      return m = R.ref, s = {
        $$typeof: Z,
        type: s,
        key: c,
        props: R,
        _owner: _
      }, (m !== void 0 ? m : null) !== null ? Object.defineProperty(s, "ref", {
        enumerable: !1,
        get: A
      }) : Object.defineProperty(s, "ref", { enumerable: !1, value: null }), s._store = {}, Object.defineProperty(s._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(s, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(s, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: V
      }), Object.defineProperty(s, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: X
      }), Object.freeze && (Object.freeze(s.props), Object.freeze(s)), s;
    }
    function Q(s, c, m, N, _, R, V, X) {
      var x = c.children;
      if (x !== void 0)
        if (N)
          if (ue(x)) {
            for (N = 0; N < x.length; N++)
              L(x[N]);
            Object.freeze && Object.freeze(x);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else L(x);
      if (be.call(c, "key")) {
        x = O(s);
        var $ = Object.keys(c).filter(function(fe) {
          return fe !== "key";
        });
        N = 0 < $.length ? "{key: someKey, " + $.join(": ..., ") + ": ...}" : "{key: someKey}", he[x + N] || ($ = 0 < $.length ? "{" + $.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          N,
          x,
          $,
          x
        ), he[x + N] = !0);
      }
      if (x = null, m !== void 0 && (w(m), x = "" + m), D(c) && (w(c.key), x = "" + c.key), "key" in c) {
        m = {};
        for (var H in c)
          H !== "key" && (m[H] = c[H]);
      } else m = c;
      return x && W(
        m,
        typeof s == "function" ? s.displayName || s.name || "Unknown" : s
      ), z(
        s,
        x,
        R,
        _,
        f(),
        m,
        V,
        X
      );
    }
    function L(s) {
      typeof s == "object" && s !== null && s.$$typeof === Z && s._store && (s._store.validated = 1);
    }
    var Y = Re, Z = Symbol.for("react.transitional.element"), ee = Symbol.for("react.portal"), P = Symbol.for("react.fragment"), J = Symbol.for("react.strict_mode"), ce = Symbol.for("react.profiler"), pe = Symbol.for("react.consumer"), ge = Symbol.for("react.context"), de = Symbol.for("react.forward_ref"), F = Symbol.for("react.suspense"), ve = Symbol.for("react.suspense_list"), me = Symbol.for("react.memo"), te = Symbol.for("react.lazy"), K = Symbol.for("react.activity"), Ne = Symbol.for("react.client.reference"), q = Y.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, be = Object.prototype.hasOwnProperty, ue = Array.isArray, T = console.createTask ? console.createTask : function() {
      return null;
    };
    Y = {
      "react-stack-bottom-frame": function(s) {
        return s();
      }
    };
    var se, ne = {}, ae = Y["react-stack-bottom-frame"].bind(
      Y,
      v
    )(), B = T(U(v)), he = {};
    le.Fragment = P, le.jsx = function(s, c, m, N, _) {
      var R = 1e4 > q.recentlyCreatedOwnerStacks++;
      return Q(
        s,
        c,
        m,
        !1,
        N,
        _,
        R ? Error("react-stack-top-frame") : ae,
        R ? T(U(s)) : B
      );
    }, le.jsxs = function(s, c, m, N, _) {
      var R = 1e4 > q.recentlyCreatedOwnerStacks++;
      return Q(
        s,
        c,
        m,
        !0,
        N,
        _,
        R ? Error("react-stack-top-frame") : ae,
        R ? T(U(s)) : B
      );
    };
  }()), le;
}
process.env.NODE_ENV === "production" ? xe.exports = Ce() : xe.exports = Pe();
var e = xe.exports;
const $e = ({
  className: O = "",
  style: G = {}
}) => {
  const [w, U] = y(""), [f, v] = y([]), [D, W] = y([]), [A, z] = y(!1), [Q, L] = y(null), [Y, Z] = y(null), [ee, P] = y([]), [J, ce] = y({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    }
  }), [pe, ge] = y(!1), [de, F] = y(!1), [ve, me] = y(!1), [te, K] = y(/* @__PURE__ */ new Set()), [Ne, q] = y(/* @__PURE__ */ new Set()), [be, ue] = y(0), [T, se] = y(!1), ne = 100, ae = () => {
    const t = document.createElement("canvas"), n = t.getContext("2d");
    n.textBaseline = "top", n.font = "14px Arial", n.fillText("Usage tracking", 2, 2);
    const a = t.toDataURL(), i = navigator.userAgent, r = navigator.language, l = Intl.DateTimeFormat().resolvedOptions().timeZone, o = a + i + r + l;
    let d = 0;
    for (let u = 0; u < o.length; u++) {
      const h = o.charCodeAt(u);
      d = (d << 5) - d + h, d = d & d;
    }
    return `podcast_usage_${Math.abs(d)}`;
  }, B = () => {
    const t = ae(), n = (/* @__PURE__ */ new Date()).toDateString(), a = `${t}_${n}`, i = localStorage.getItem(a), r = i ? parseInt(i, 10) : 0;
    return ue(r), r >= ne ? (se(!0), !1) : !0;
  }, he = (t = 1) => {
    const n = ae(), a = (/* @__PURE__ */ new Date()).toDateString(), i = `${n}_${a}`, r = localStorage.getItem(i), o = (r ? parseInt(r, 10) : 0) + t;
    localStorage.setItem(i, o.toString()), ue(o), o >= ne && se(!0);
  };
  Re.useEffect(() => {
    B();
  }, []);
  const s = (t) => {
    let n = t.toLowerCase().replace(/[^a-z0-9\s]/g, "").replace(/\s+/g, " ").replace(/^the\s+/, "").trim();
    const a = ["the", "and", "for", "with", "from", "show", "podcast", "cast", "of", "in", "on", "at", "to", "a", "an"], i = n.split(" "), r = i.filter(
      (o) => o.length > 2 && !a.includes(o)
    );
    if (r.length === 1) {
      const o = r[0];
      return o.length >= 6 && o.length <= 15 ? o : o.length < 6 ? o + "pod" : c(o);
    }
    if (r.length >= 2) {
      const o = r[0], d = r[1], u = o + d;
      if (u.length >= 6 && u.length <= 15)
        return u;
      const h = c(o), E = c(d), j = h + E;
      if (j.length >= 6 && j.length <= 15)
        return j;
      if (j.length > 15)
        return h + "-" + E;
    }
    if (r.length > 0) {
      const o = c(r[0]), d = ["cast", "pod", "show", "talk"];
      for (const u of d) {
        const h = o + u;
        if (h.length >= 6 && h.length <= 15)
          return h;
      }
      if (o.length >= 6 && o.length <= 15)
        return o;
    }
    const l = i[0];
    return l && l.length >= 3 ? c(l) + "pod" : "podcast" + Math.random().toString(36).substring(2, 5);
  }, c = (t) => {
    if (t.length <= 8) return t;
    const n = {
      // Business & Professional
      business: "biz",
      entrepreneur: "entre",
      marketing: "market",
      finance: "fin",
      startup: "start",
      leadership: "lead",
      strategy: "strat",
      success: "win",
      growth: "grow",
      innovation: "innov",
      management: "manage",
      // Technology
      technology: "tech",
      development: "dev",
      digital: "digi",
      software: "soft",
      coding: "code",
      programming: "prog",
      // Content & Media
      stories: "story",
      journey: "path",
      adventure: "quest",
      creative: "create",
      entertainment: "fun",
      education: "learn",
      knowledge: "know",
      wisdom: "wise",
      // Lifestyle & Personal
      lifestyle: "life",
      wellness: "well",
      fitness: "fit",
      health: "heal",
      mindset: "mind",
      motivation: "motive",
      inspiration: "inspire",
      // Geographic & Cultural
      american: "usa",
      european: "euro",
      international: "global",
      community: "comm",
      culture: "cult",
      society: "social"
    };
    if (n[t])
      return n[t];
    const a = ["super", "mega", "ultra", "micro", "mini", "multi"];
    for (const r of a)
      if (t.startsWith(r) && t.length > r.length + 3) {
        const l = t.substring(r.length);
        if (l.length <= 8)
          return l;
      }
    const i = ["ing", "tion", "sion", "ness", "ment", "able", "ible"];
    for (const r of i)
      if (t.endsWith(r) && t.length > r.length + 4) {
        const l = t.substring(0, t.length - r.length);
        if (l.length >= 4 && l.length <= 8)
          return l;
      }
    return t.length > 8 ? t.substring(0, 8) : t;
  }, m = async (t) => {
    try {
      const n = await fetch(`https://dns.google/resolve?name=${t}&type=A`, {
        method: "GET",
        headers: {
          Accept: "application/json"
        }
      });
      if (!n.ok)
        return "error";
      const a = await n.json();
      return a.Answer && a.Answer.length > 0 ? "taken" : "available";
    } catch (n) {
      return console.warn("Domain check failed:", n), "error";
    }
  }, N = async (t) => {
    const n = [...t];
    for (let a = 0; a < n.length; a++) {
      const i = s(n[a].name);
      n[a].suggestedDomain = i, n[a].domainStatus = "checking", K((r) => /* @__PURE__ */ new Set([...r, a]));
    }
    v(n);
    for (let a = 0; a < n.length; a++) {
      const i = `${n[a].suggestedDomain}.com`;
      try {
        const r = await m(i);
        v((l) => {
          const o = [...l];
          return o[a] && (o[a].domainStatus = r), o;
        });
      } catch {
        v((l) => {
          const o = [...l];
          return o[a] && (o[a].domainStatus = "error"), o;
        });
      } finally {
        K((r) => {
          const l = new Set(r);
          return l.delete(a), l;
        });
      }
    }
  }, _ = (t, n) => {
    if (t.length === 0) return null;
    const a = t.map((o) => o.name.split(" ").length), i = n.map((o) => o.name.split(" ").length), r = a.reduce((o, d) => o + d, 0) / a.length, l = i.length > 0 ? i.reduce((o, d) => o + d, 0) / i.length : 0;
    return r <= 2 && l > 2 ? "short" : r <= 4 && (l <= 2 || l > 4) ? "medium" : r > 4 && l <= 4 ? "long" : r <= 2 ? "short" : r <= 4 ? "medium" : "long";
  }, R = (t, n) => {
    if (t.length === 0) return null;
    const a = t.map((r) => r.description.toLowerCase()).join(" "), i = n.map((r) => r.description.toLowerCase()).join(" ");
    return (a.includes("professional") || a.includes("business")) && !i.includes("professional") && !i.includes("business") ? "professional" : (a.includes("creative") || a.includes("unique")) && !i.includes("creative") && !i.includes("unique") ? "creative" : (a.includes("fun") || a.includes("playful")) && !i.includes("fun") && !i.includes("playful") ? "playful" : "descriptive";
  }, V = (t) => {
    const n = [];
    t.forEach((i) => {
      const r = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], l = i.name.toLowerCase().split(/\s+/).filter(
        (o) => o.length > 2 && !r.includes(o)
      );
      n.push(...l);
    });
    const a = {};
    return n.forEach((i) => a[i] = (a[i] || 0) + 1), Object.entries(a).sort(([, i], [, r]) => r - i).slice(0, 5).map(([i]) => i);
  }, X = (t) => {
    const n = t.filter((i) => i.liked === !0), a = t.filter((i) => i.liked === !1);
    return {
      preferredLength: _(n, a),
      preferredStyle: R(n, a),
      likedKeywords: V(n),
      dislikedKeywords: V(a),
      preferredStructure: null
      // Simplified for now
    };
  }, x = (t, n) => {
    const a = [
      ...n.likedNames.map((o) => o.name.toLowerCase()),
      ...n.dislikedNames.map((o) => o.name.toLowerCase()),
      ...f.map((o) => o.name.toLowerCase())
    ], i = `Create 4 unique, high-converting podcast names for: ${t}`;
    let r = `

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;
    a.length > 0 && (r += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${a.map((o) => `- ${o}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);
    let l = "";
    if (n.patterns.preferredLength && (l += `
Focus on ${{
      short: "1-2 words (punchy and memorable)",
      medium: "2-3 words (balanced and brandable)",
      long: "3-4 words (descriptive but still catchy)"
    }[n.patterns.preferredLength]}. `), n.patterns.preferredStyle && (l += `Use ${{
      descriptive: "clear, straightforward names that explain the content",
      creative: "imaginative, metaphorical, or playful names",
      professional: "authoritative, business-focused names",
      playful: "fun, energetic, engaging names"
    }[n.patterns.preferredStyle] || n.patterns.preferredStyle}. `), n.patterns.likedKeywords.length > 0 && (l += `
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (l += `
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `), n.likedNames.length > 0) {
      const o = n.likedNames.slice(-2).map((d) => d.name).join('", "');
      l += `
Generate names with similar appeal to: "${o}" (but completely different concepts). `;
    }
    return `${i}${r}${l}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;
  }, $ = (t, n, a = 1) => {
    const i = [
      ...n.likedNames.map((d) => d.name.toLowerCase()),
      ...n.dislikedNames.map((d) => d.name.toLowerCase()),
      ...f.map((d) => d.name.toLowerCase())
    ], r = `Create ${a} unique, high-converting podcast name${a > 1 ? "s" : ""} for: ${t}`;
    let l = `

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;
    i.length > 0 && (l += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${i.map((d) => `- ${d}`).join(`
`)}
Do not create names similar to any of the above.`);
    let o = "";
    return n.patterns.likedKeywords.length > 0 && (o += `
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (o += `
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `), `${r}${l}${o}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${a > 1 ? ', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}' : ""}]}`;
  }, H = async (t = !1) => {
    if (!w.trim()) {
      L("Please describe what your podcast is about");
      return;
    }
    if (!B()) {
      L(null);
      return;
    }
    z(!0), L(null), v([]), t ? F(!0) : (P([]), ge(!1), F(!1), me(!1));
    try {
      const n = t ? x(w, J) : `Create 4 unique, high-converting podcast names for: ${w}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`, a = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: n
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!a.ok)
        throw new Error(`API request failed: ${a.status} ${a.statusText}`);
      const i = await a.json();
      if (!i.candidates || !i.candidates[0] || !i.candidates[0].content)
        throw new Error("Invalid response format from API");
      const l = i.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!l)
        throw new Error("No valid JSON found in API response");
      const o = JSON.parse(l[0]);
      if (!o.podcast_names || !Array.isArray(o.podcast_names))
        throw new Error("Invalid response structure");
      v(o.podcast_names), he(4), N(o.podcast_names);
      const d = o.podcast_names.map((u, h) => ({
        name: u.name,
        description: u.description,
        liked: null,
        timestamp: Date.now(),
        index: h
      }));
      P(d);
    } catch (n) {
      console.error("Error generating podcast names:", n), L(n instanceof Error ? n.message : "An unexpected error occurred");
    } finally {
      z(!1), F(!1);
    }
  }, fe = async (t, n) => {
    try {
      await navigator.clipboard.writeText(t), Z(n), setTimeout(() => Z(null), 2e3);
    } catch (a) {
      console.error("Failed to copy text:", a);
    }
  }, je = async (t, n) => {
    const a = f[t];
    a && (n ? (W((i) => i.find((r) => r.name === a.name) ? i : [...i, a]), ce((i) => {
      const r = { ...i };
      return r.dislikedNames = r.dislikedNames.filter((l) => l.name !== a.name), r.likedNames.find((l) => l.name === a.name) || r.likedNames.push({
        name: a.name,
        description: a.description,
        liked: !0,
        timestamp: Date.now(),
        index: t
      }), r.patterns = X([...r.likedNames, ...r.dislikedNames]), r;
    }), q((i) => /* @__PURE__ */ new Set([...i, t]))) : (q((i) => /* @__PURE__ */ new Set([...i, t])), ce((i) => {
      const r = { ...i };
      return r.likedNames = r.likedNames.filter((l) => l.name !== a.name), r.dislikedNames.find((l) => l.name === a.name) || r.dislikedNames.push({
        name: a.name,
        description: a.description,
        liked: !1,
        timestamp: Date.now(),
        index: t
      }), r.patterns = X([...r.likedNames, ...r.dislikedNames]), r;
    })), w.trim() && Ee(t), ve || me(!0));
  }, Ee = async (t) => {
    var n, a, i, r, l, o;
    if (B()) {
      console.log(`🔄 Starting replacement generation for index ${t}`);
      try {
        const d = $(w, J, 1);
        console.log(`📝 Generated single name prompt for index ${t}:`, d.substring(0, 100) + "..."), console.log(`🌐 Making API call for index ${t}...`);
        const u = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: d
              }]
            }]
          })
        });
        if (console.log(`📡 API response status for index ${t}:`, u.status), !u.ok)
          throw new Error(`Failed to generate replacement suggestion: ${u.status} ${u.statusText}`);
        const h = await u.json();
        console.log(`📦 API response data for index ${t}:`, h);
        const E = (l = (r = (i = (a = (n = h.candidates) == null ? void 0 : n[0]) == null ? void 0 : a.content) == null ? void 0 : i.parts) == null ? void 0 : r[0]) == null ? void 0 : l.text;
        if (!E)
          throw new Error("No content in API response");
        console.log(`📄 API content for index ${t}:`, E.substring(0, 200) + "...");
        const j = E.match(/\{[\s\S]*\}/);
        if (!j)
          throw console.error(`❌ No valid JSON found in response for index ${t}:`, E), new Error("No valid JSON found in response");
        const re = JSON.parse(j[0]);
        console.log(`🔍 Parsed response for index ${t}:`, re);
        const C = (o = re.podcast_names) == null ? void 0 : o[0];
        if (C) {
          if (console.log(`✅ New name generated for index ${t}:`, C), v((I) => {
            const b = [...I];
            return b[t] = {
              name: C.name,
              description: C.description,
              suggestedDomain: C.suggestedDomain,
              domainStatus: "checking"
            }, console.log(`🔄 Updated results for index ${t}:`, b[t]), b;
          }), q((I) => {
            const b = new Set(I);
            return b.delete(t), console.log(`🗑️ Removed index ${t} from pending replacements. Remaining:`, Array.from(b)), b;
          }), P((I) => {
            const b = I.filter((p) => p.index !== t);
            return console.log(`🧹 Cleared feedback for index ${t}. Remaining feedback:`, b), b;
          }), C.suggestedDomain) {
            console.log(`🌐 Checking domain availability for ${C.suggestedDomain}...`), K((b) => /* @__PURE__ */ new Set([...b, t]));
            const I = await m(C.suggestedDomain);
            console.log(`🏷️ Domain status for ${C.suggestedDomain}:`, I), v((b) => {
              const p = [...b];
              return p[t] && (p[t].domainStatus = I), p;
            }), K((b) => {
              const p = new Set(b);
              return p.delete(t), p;
            });
          }
          console.log(`🎉 Successfully completed replacement for index ${t}`);
        } else
          throw console.error(`❌ No new name found in parsed response for index ${t}:`, re), new Error("No new name found in API response");
      } catch (d) {
        console.error(`❌ Error generating replacement suggestion for index ${t}:`, d), q((u) => {
          const h = new Set(u);
          return h.delete(t), console.log(`🗑️ Removed index ${t} from pending on error. Remaining:`, Array.from(h)), h;
        }), P((u) => {
          const h = u.filter((E) => E.index !== t);
          return console.log(`🧹 Cleared feedback for index ${t} on error. Remaining:`, h), h;
        });
      }
    }
  }, Ae = () => {
    w.trim() && Te(w);
  }, Te = async (t) => {
    var n, a, i, r, l, o, d, u, h, E;
    z(!0), L(""), F(!0);
    try {
      const j = f.filter((p, S) => {
        const g = ee.find((k) => k.index === S);
        return (g == null ? void 0 : g.liked) === !0;
      }), re = f.filter((p, S) => {
        const g = ee.find((k) => k.index === S);
        return (g == null ? void 0 : g.liked) === !1;
      }).length, C = Math.max(1, re), b = Math.min(5, j.length + C) - j.length;
      if (b <= 0) {
        const p = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: $(t, J, 1)
              }]
            }]
          })
        });
        if (!p.ok)
          throw new Error(`API request failed: ${p.status}`);
        const g = (l = (r = (i = (a = (n = (await p.json()).candidates) == null ? void 0 : n[0]) == null ? void 0 : a.content) == null ? void 0 : i.parts) == null ? void 0 : r[0]) == null ? void 0 : l.text;
        if (!g)
          throw new Error("No content received from API");
        const k = g.match(/\{[\s\S]*\}/);
        if (!k)
          throw new Error("No valid JSON found in response");
        const M = JSON.parse(k[0]);
        if (!M.podcast_names || !Array.isArray(M.podcast_names))
          throw new Error("Invalid response format");
        const oe = [...j, ...M.podcast_names].slice(0, 5);
        v(oe), N(oe);
      } else {
        const p = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: $(t, J, b)
              }]
            }]
          })
        });
        if (!p.ok)
          throw new Error(`API request failed: ${p.status}`);
        const g = (E = (h = (u = (d = (o = (await p.json()).candidates) == null ? void 0 : o[0]) == null ? void 0 : d.content) == null ? void 0 : u.parts) == null ? void 0 : h[0]) == null ? void 0 : E.text;
        if (!g)
          throw new Error("No content received from API");
        const k = g.match(/\{[\s\S]*\}/);
        if (!k)
          throw new Error("No valid JSON found in response");
        const M = JSON.parse(k[0]);
        if (!M.podcast_names || !Array.isArray(M.podcast_names))
          throw new Error("Invalid response format");
        const oe = [...j, ...M.podcast_names];
        v(oe), N(oe);
      }
      P((p) => p.filter((S) => {
        const g = f[S.index];
        return j.some((k) => k.name === (g == null ? void 0 : g.name));
      })), P((p) => p.map((S) => {
        const g = f[S.index], k = j.findIndex((M) => M.name === (g == null ? void 0 : g.name));
        return k >= 0 ? { ...S, index: k } : S;
      }).filter((S) => S.index >= 0));
    } catch (j) {
      console.error("Error generating refined names:", j), L(j instanceof Error ? j.message : "Failed to generate refined names. Please try again.");
    } finally {
      z(!1), F(!1);
    }
  }, we = (t) => {
    t.preventDefault(), H();
  }, ye = (t) => {
    t.key === "Enter" && !t.shiftKey && (t.preventDefault(), H());
  };
  return /* @__PURE__ */ e.jsx("div", { className: `podcast-name-generator ${O}`, style: G, children: /* @__PURE__ */ e.jsxs("div", { className: "generator-container", children: [
    /* @__PURE__ */ e.jsxs("div", { className: "header-section", children: [
      /* @__PURE__ */ e.jsx("h1", { className: "main-title", children: "Free Podcast Name Generator" }),
      /* @__PURE__ */ e.jsx("h2", { className: "main-subtitle", children: "Create the Perfect Name for Your Podcast in Seconds" })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: "benefits-section", children: [
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "100% Free Forever" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "No Sign-up Required" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "Instant Results" })
      ] })
    ] }),
    T && /* @__PURE__ */ e.jsx("div", { className: "limit-reached-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "limit-content", children: [
      /* @__PURE__ */ e.jsx("span", { className: "limit-icon", children: "⚠️" }),
      /* @__PURE__ */ e.jsx("div", { className: "limit-text", children: /* @__PURE__ */ e.jsx("p", { children: "You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below." }) })
    ] }) }),
    f.length === 0 && /* @__PURE__ */ e.jsx("div", { className: "initial-input-section", children: /* @__PURE__ */ e.jsx("form", { onSubmit: we, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
      /* @__PURE__ */ e.jsx(
        "textarea",
        {
          value: w,
          onChange: (t) => U(t.target.value),
          onKeyPress: ye,
          placeholder: "Describe what your podcast is about",
          className: "input-field",
          rows: 3,
          disabled: A
        }
      ),
      /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
        /* @__PURE__ */ e.jsx(
          "button",
          {
            type: "submit",
            disabled: A || !w.trim() || T,
            className: `generate-button ${T ? "disabled" : ""}`,
            children: A ? "Generating..." : T ? "Daily Limit Reached" : "Generate Names"
          }
        ),
        /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
          ] }),
          /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
            /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star star-partial", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
            ] }),
            /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
          ] })
        ] })
      ] })
    ] }) }) }),
    Q && /* @__PURE__ */ e.jsxs("div", { className: "error-message", children: [
      /* @__PURE__ */ e.jsx("span", { className: "error-icon", children: "⚠️" }),
      Q
    ] }),
    A && /* @__PURE__ */ e.jsxs("div", { className: "loading-container", children: [
      /* @__PURE__ */ e.jsx("div", { className: "loading-spinner" }),
      /* @__PURE__ */ e.jsx("p", { children: de ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
    ] }),
    f.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "results-container", children: [
      D.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "favorites-section", children: [
        /* @__PURE__ */ e.jsxs("div", { className: "favorites-header", children: [
          /* @__PURE__ */ e.jsxs("h3", { children: [
            "🏆 Your Winning Podcast Names (",
            D.length,
            ")"
          ] }),
          /* @__PURE__ */ e.jsx("p", { className: "favorites-subtitle", children: "Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!" })
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "favorites-grid", children: D.map((t, n) => /* @__PURE__ */ e.jsxs("div", { className: "favorite-card", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "favorite-content", children: [
            /* @__PURE__ */ e.jsx("h4", { className: "favorite-name", children: t.name }),
            /* @__PURE__ */ e.jsx("p", { className: "favorite-description", children: t.description }),
            t.suggestedDomain && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
              /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
              /* @__PURE__ */ e.jsx("span", { className: "domain-name", children: t.suggestedDomain }),
              /* @__PURE__ */ e.jsx("span", { className: `domain-status ${t.domainStatus}`, children: t.domainStatus === "available" ? "✅ Available" : t.domainStatus === "taken" ? "❌ Taken" : t.domainStatus === "error" ? "⚠️ Check manually" : "🔍 Checking..." })
            ] })
          ] }),
          /* @__PURE__ */ e.jsx("div", { className: "favorite-actions", children: /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: () => fe(t.name, -1),
              className: "copy-button small",
              title: "Copy to clipboard",
              children: "📋 Copy"
            }
          ) })
        ] }, `fav-${n}`)) })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "input-section-simple", children: [
        /* @__PURE__ */ e.jsx("div", { className: "input-help-message-simple", children: /* @__PURE__ */ e.jsxs("p", { className: "input-sub-description", children: [
          "💡 Want different suggestions? Update your description below - ",
          /* @__PURE__ */ e.jsx("strong", { children: "your favorites will stay safe!" })
        ] }) }),
        /* @__PURE__ */ e.jsx("form", { onSubmit: we, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
          /* @__PURE__ */ e.jsx(
            "textarea",
            {
              value: w,
              onChange: (t) => U(t.target.value),
              onKeyPress: ye,
              placeholder: "Describe what your podcast is about",
              className: "input-field",
              rows: 3,
              disabled: A
            }
          ),
          /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
            /* @__PURE__ */ e.jsx(
              "button",
              {
                type: "submit",
                disabled: A || !w.trim() || T,
                className: `generate-button ${T ? "disabled" : ""}`,
                children: A ? "Generating..." : T ? "Daily Limit Reached" : "Generate Names"
              }
            ),
            /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
              /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
              ] }),
              /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
                /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star star-partial", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
                ] }),
                /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
              ] })
            ] })
          ] })
        ] }) })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "suggestions-section", children: [
        /* @__PURE__ */ e.jsx("div", { className: "suggestions-header", children: /* @__PURE__ */ e.jsx("h3", { children: "🎯 Current Suggestions" }) }),
        /* @__PURE__ */ e.jsx("div", { className: "onboarding-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "onboarding-content", children: [
          /* @__PURE__ */ e.jsx("span", { className: "onboarding-icon", children: "💡" }),
          /* @__PURE__ */ e.jsxs("div", { className: "onboarding-text", children: [
            /* @__PURE__ */ e.jsx("strong", { children: "Smart AI Learning:" }),
            " The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."
          ] })
        ] }) }),
        /* @__PURE__ */ e.jsx("div", { className: "results-grid", children: f.map((t, n) => {
          const a = ee.find((d) => d.index === n), i = (a == null ? void 0 : a.liked) === !0, r = (a == null ? void 0 : a.liked) === !1, l = Ne.has(n), o = te.has(n);
          return /* @__PURE__ */ e.jsxs(
            "div",
            {
              className: `result-card ${i ? "liked" : ""} ${r ? "disliked" : ""} ${l ? "pending" : ""}`,
              style: {
                opacity: l ? 0.6 : 1,
                pointerEvents: l ? "none" : "auto"
              },
              children: [
                /* @__PURE__ */ e.jsxs("div", { className: "result-header", children: [
                  /* @__PURE__ */ e.jsx("h4", { className: "result-name", children: l ? i ? "Generating new suggestion..." : "Generating better suggestion..." : t.name }),
                  /* @__PURE__ */ e.jsxs("div", { className: "result-actions", children: [
                    /* @__PURE__ */ e.jsxs("div", { className: "feedback-buttons", children: [
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => je(n, !0),
                          className: `feedback-button like-button ${i ? "active" : ""}`,
                          title: "I like this name",
                          disabled: l,
                          children: "👍"
                        }
                      ),
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => je(n, !1),
                          className: `feedback-button dislike-button ${r ? "active" : ""} ${l ? "loading" : ""}`,
                          title: l ? "Generating replacement..." : "I don't like this name",
                          disabled: l,
                          children: l ? "🔄" : "👎"
                        }
                      )
                    ] }),
                    /* @__PURE__ */ e.jsx(
                      "button",
                      {
                        onClick: () => fe(t.name, n),
                        className: "copy-button",
                        title: "Copy podcast name",
                        disabled: l,
                        children: Y === n ? "✓ Copied!" : "📋 Copy"
                      }
                    )
                  ] })
                ] }),
                /* @__PURE__ */ e.jsx("p", { className: "result-description", children: l ? i ? "Added to favorites! Generating a new suggestion..." : "Creating a better suggestion based on your preferences..." : t.description }),
                t.suggestedDomain && !o && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
                  /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
                  /* @__PURE__ */ e.jsxs("code", { className: "domain-text", children: [
                    t.suggestedDomain,
                    ".com"
                  ] }),
                  /* @__PURE__ */ e.jsxs("span", { className: `domain-status ${t.domainStatus}`, children: [
                    (t.domainStatus === "checking" || te.has(n)) && "⏳ Checking...",
                    t.domainStatus === "available" && "✅ Available",
                    t.domainStatus === "taken" && "❌ Taken",
                    t.domainStatus === "error" && "⚠️ Check manually"
                  ] })
                ] })
              ]
            },
            n
          );
        }) })
      ] }),
      pe && !A && /* @__PURE__ */ e.jsxs("div", { className: "refinement-section", children: [
        /* @__PURE__ */ e.jsx("div", { className: "refinement-info", children: /* @__PURE__ */ e.jsx("p", { children: "💡 I'll keep your liked names and replace the disliked ones with better suggestions!" }) }),
        /* @__PURE__ */ e.jsx(
          "button",
          {
            onClick: Ae,
            className: "refinement-button",
            disabled: A,
            children: de ? "🔄 Refining..." : "🎯 Replace Disliked Names"
          }
        )
      ] })
    ] })
  ] }) });
};
export {
  $e as default
};

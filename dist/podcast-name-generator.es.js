import Ae, { useState as y } from "react";
var xe = { exports: {} }, ie = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Ee;
function Oe() {
  if (Ee) return ie;
  Ee = 1;
  var $ = Symbol.for("react.transitional.element"), Y = Symbol.for("react.fragment");
  function j(M, f, v) {
    var O = null;
    if (v !== void 0 && (O = "" + v), f.key !== void 0 && (O = "" + f.key), "key" in f) {
      v = {};
      for (var F in f)
        F !== "key" && (v[F] = f[F]);
    } else v = f;
    return f = v.ref, {
      $$typeof: $,
      type: M,
      key: O,
      ref: f !== void 0 ? f : null,
      props: v
    };
  }
  return ie.Fragment = Y, ie.jsx = j, ie.jsxs = j, ie;
}
var le = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Te;
function De() {
  return Te || (Te = 1, process.env.NODE_ENV !== "production" && function() {
    function $(s) {
      if (s == null) return null;
      if (typeof s == "function")
        return s.$$typeof === Ne ? null : s.displayName || s.name || null;
      if (typeof s == "string") return s;
      switch (s) {
        case C:
          return "Fragment";
        case ce:
          return "Profiler";
        case K:
          return "StrictMode";
        case W:
          return "Suspense";
        case ve:
          return "SuspenseList";
        case B:
          return "Activity";
      }
      if (typeof s == "object")
        switch (typeof s.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), s.$$typeof) {
          case ee:
            return "Portal";
          case ge:
            return (s.displayName || "Context") + ".Provider";
          case pe:
            return (s._context.displayName || "Context") + ".Consumer";
          case de:
            var d = s.render;
            return s = s.displayName, s || (s = d.displayName || d.name || "", s = s !== "" ? "ForwardRef(" + s + ")" : "ForwardRef"), s;
          case me:
            return d = s.displayName || null, d !== null ? d : $(s.type) || "Memo";
          case te:
            d = s._payload, s = s._init;
            try {
              return $(s(d));
            } catch {
            }
        }
      return null;
    }
    function Y(s) {
      return "" + s;
    }
    function j(s) {
      try {
        Y(s);
        var d = !1;
      } catch {
        d = !0;
      }
      if (d) {
        d = console;
        var m = d.error, b = typeof Symbol == "function" && Symbol.toStringTag && s[Symbol.toStringTag] || s.constructor.name || "Object";
        return m.call(
          d,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          b
        ), Y(s);
      }
    }
    function M(s) {
      if (s === C) return "<>";
      if (typeof s == "object" && s !== null && s.$$typeof === te)
        return "<...>";
      try {
        var d = $(s);
        return d ? "<" + d + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function f() {
      var s = U.A;
      return s === null ? null : s.getOwner();
    }
    function v() {
      return Error("react-stack-top-frame");
    }
    function O(s) {
      if (ue.call(s, "key")) {
        var d = Object.getOwnPropertyDescriptor(s, "key").get;
        if (d && d.isReactWarning) return !1;
      }
      return s.key !== void 0;
    }
    function F(s, d) {
      function m() {
        se || (se = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          d
        ));
      }
      m.isReactWarning = !0, Object.defineProperty(s, "key", {
        get: m,
        configurable: !0
      });
    }
    function T() {
      var s = $(this.type);
      return be[s] || (be[s] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), s = this.props.ref, s !== void 0 ? s : null;
    }
    function z(s, d, m, b, _, k, X, G) {
      return m = k.ref, s = {
        $$typeof: Z,
        type: s,
        key: d,
        props: k,
        _owner: _
      }, (m !== void 0 ? m : null) !== null ? Object.defineProperty(s, "ref", {
        enumerable: !1,
        get: T
      }) : Object.defineProperty(s, "ref", { enumerable: !1, value: null }), s._store = {}, Object.defineProperty(s._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(s, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(s, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: X
      }), Object.defineProperty(s, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: G
      }), Object.freeze && (Object.freeze(s.props), Object.freeze(s)), s;
    }
    function Q(s, d, m, b, _, k, X, G) {
      var w = d.children;
      if (w !== void 0)
        if (b)
          if (he(w)) {
            for (b = 0; b < w.length; b++)
              D(w[b]);
            Object.freeze && Object.freeze(w);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else D(w);
      if (ue.call(d, "key")) {
        w = $(s);
        var q = Object.keys(d).filter(function(fe) {
          return fe !== "key";
        });
        b = 0 < q.length ? "{key: someKey, " + q.join(": ..., ") + ": ...}" : "{key: someKey}", ae[w + b] || (q = 0 < q.length ? "{" + q.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          b,
          w,
          q,
          w
        ), ae[w + b] = !0);
      }
      if (w = null, m !== void 0 && (j(m), w = "" + m), O(d) && (j(d.key), w = "" + d.key), "key" in d) {
        m = {};
        for (var H in d)
          H !== "key" && (m[H] = d[H]);
      } else m = d;
      return w && F(
        m,
        typeof s == "function" ? s.displayName || s.name || "Unknown" : s
      ), z(
        s,
        w,
        k,
        _,
        f(),
        m,
        X,
        G
      );
    }
    function D(s) {
      typeof s == "object" && s !== null && s.$$typeof === Z && s._store && (s._store.validated = 1);
    }
    var J = Ae, Z = Symbol.for("react.transitional.element"), ee = Symbol.for("react.portal"), C = Symbol.for("react.fragment"), K = Symbol.for("react.strict_mode"), ce = Symbol.for("react.profiler"), pe = Symbol.for("react.consumer"), ge = Symbol.for("react.context"), de = Symbol.for("react.forward_ref"), W = Symbol.for("react.suspense"), ve = Symbol.for("react.suspense_list"), me = Symbol.for("react.memo"), te = Symbol.for("react.lazy"), B = Symbol.for("react.activity"), Ne = Symbol.for("react.client.reference"), U = J.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, ue = Object.prototype.hasOwnProperty, he = Array.isArray, V = console.createTask ? console.createTask : function() {
      return null;
    };
    J = {
      "react-stack-bottom-frame": function(s) {
        return s();
      }
    };
    var se, be = {}, ne = J["react-stack-bottom-frame"].bind(
      J,
      v
    )(), P = V(M(v)), ae = {};
    le.Fragment = C, le.jsx = function(s, d, m, b, _) {
      var k = 1e4 > U.recentlyCreatedOwnerStacks++;
      return Q(
        s,
        d,
        m,
        !1,
        b,
        _,
        k ? Error("react-stack-top-frame") : ne,
        k ? V(M(s)) : P
      );
    }, le.jsxs = function(s, d, m, b, _) {
      var k = 1e4 > U.recentlyCreatedOwnerStacks++;
      return Q(
        s,
        d,
        m,
        !0,
        b,
        _,
        k ? Error("react-stack-top-frame") : ne,
        k ? V(M(s)) : P
      );
    };
  }()), le;
}
process.env.NODE_ENV === "production" ? xe.exports = Oe() : xe.exports = De();
var e = xe.exports;
const Ie = ({
  className: $ = "",
  style: Y = {}
}) => {
  const [j, M] = y(""), [f, v] = y([]), [O, F] = y([]), [T, z] = y(!1), [Q, D] = y(null), [J, Z] = y(null), [ee, C] = y([]), [K, ce] = y({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    }
  }), [pe, ge] = y(!1), [de, W] = y(!1), [ve, me] = y(!1), [te, B] = y(/* @__PURE__ */ new Set()), [Ne, U] = y(/* @__PURE__ */ new Set()), [ue, he] = y(/* @__PURE__ */ new Set()), [V, se] = y(null), [be, ne] = y(0), [P, ae] = y(!1), s = 100, d = () => {
    const t = document.createElement("canvas"), a = t.getContext("2d");
    a.textBaseline = "top", a.font = "14px Arial", a.fillText("Usage tracking", 2, 2);
    const r = t.toDataURL(), i = navigator.userAgent, l = navigator.language, o = Intl.DateTimeFormat().resolvedOptions().timeZone, n = r + i + l + o;
    let c = 0;
    for (let u = 0; u < n.length; u++) {
      const h = n.charCodeAt(u);
      c = (c << 5) - c + h, c = c & c;
    }
    return `podcast_usage_${Math.abs(c)}`;
  }, m = () => {
    const t = d(), a = (/* @__PURE__ */ new Date()).toDateString(), r = `${t}_${a}`, i = localStorage.getItem(r), l = i ? parseInt(i, 10) : 0;
    return ne(l), l >= s ? (ae(!0), !1) : !0;
  }, b = (t = 1) => {
    const a = d(), r = (/* @__PURE__ */ new Date()).toDateString(), i = `${a}_${r}`, l = localStorage.getItem(i), n = (l ? parseInt(l, 10) : 0) + t;
    localStorage.setItem(i, n.toString()), ne(n), n >= s && ae(!0);
  };
  Ae.useEffect(() => {
    m();
  }, []);
  const _ = (t) => {
    let a = t.toLowerCase().replace(/[^a-z0-9\s]/g, "").replace(/\s+/g, " ").replace(/^the\s+/, "").trim();
    const r = ["the", "and", "for", "with", "from", "show", "podcast", "cast", "of", "in", "on", "at", "to", "a", "an"], i = a.split(" "), l = i.filter(
      (n) => n.length > 2 && !r.includes(n)
    );
    if (l.length === 1) {
      const n = l[0];
      return n.length >= 6 && n.length <= 15 ? n : n.length < 6 ? n + "pod" : k(n);
    }
    if (l.length >= 2) {
      const n = l[0], c = l[1], u = n + c;
      if (u.length >= 6 && u.length <= 15)
        return u;
      const h = k(n), E = k(c), x = h + E;
      if (x.length >= 6 && x.length <= 15)
        return x;
      if (x.length > 15)
        return h + "-" + E;
    }
    if (l.length > 0) {
      const n = k(l[0]), c = ["cast", "pod", "show", "talk"];
      for (const u of c) {
        const h = n + u;
        if (h.length >= 6 && h.length <= 15)
          return h;
      }
      if (n.length >= 6 && n.length <= 15)
        return n;
    }
    const o = i[0];
    return o && o.length >= 3 ? k(o) + "pod" : "podcast" + Math.random().toString(36).substring(2, 5);
  }, k = (t) => {
    if (t.length <= 8) return t;
    const a = {
      // Business & Professional
      business: "biz",
      entrepreneur: "entre",
      marketing: "market",
      finance: "fin",
      startup: "start",
      leadership: "lead",
      strategy: "strat",
      success: "win",
      growth: "grow",
      innovation: "innov",
      management: "manage",
      // Technology
      technology: "tech",
      development: "dev",
      digital: "digi",
      software: "soft",
      coding: "code",
      programming: "prog",
      // Content & Media
      stories: "story",
      journey: "path",
      adventure: "quest",
      creative: "create",
      entertainment: "fun",
      education: "learn",
      knowledge: "know",
      wisdom: "wise",
      // Lifestyle & Personal
      lifestyle: "life",
      wellness: "well",
      fitness: "fit",
      health: "heal",
      mindset: "mind",
      motivation: "motive",
      inspiration: "inspire",
      // Geographic & Cultural
      american: "usa",
      european: "euro",
      international: "global",
      community: "comm",
      culture: "cult",
      society: "social"
    };
    if (a[t])
      return a[t];
    const r = ["super", "mega", "ultra", "micro", "mini", "multi"];
    for (const l of r)
      if (t.startsWith(l) && t.length > l.length + 3) {
        const o = t.substring(l.length);
        if (o.length <= 8)
          return o;
      }
    const i = ["ing", "tion", "sion", "ness", "ment", "able", "ible"];
    for (const l of i)
      if (t.endsWith(l) && t.length > l.length + 4) {
        const o = t.substring(0, t.length - l.length);
        if (o.length >= 4 && o.length <= 8)
          return o;
      }
    return t.length > 8 ? t.substring(0, 8) : t;
  }, X = async (t) => {
    try {
      const a = await fetch(`https://dns.google/resolve?name=${t}&type=A`, {
        method: "GET",
        headers: {
          Accept: "application/json"
        }
      });
      if (!a.ok)
        return "error";
      const r = await a.json();
      return r.Answer && r.Answer.length > 0 ? "taken" : "available";
    } catch (a) {
      return console.warn("Domain check failed:", a), "error";
    }
  }, G = async (t) => {
    const a = [...t];
    for (let r = 0; r < a.length; r++) {
      const i = _(a[r].name);
      a[r].suggestedDomain = i, a[r].domainStatus = "checking", B((l) => /* @__PURE__ */ new Set([...l, r]));
    }
    v(a);
    for (let r = 0; r < a.length; r++) {
      const i = `${a[r].suggestedDomain}.com`;
      try {
        const l = await X(i);
        v((o) => {
          const n = [...o];
          return n[r] && (n[r].domainStatus = l), n;
        });
      } catch {
        v((o) => {
          const n = [...o];
          return n[r] && (n[r].domainStatus = "error"), n;
        });
      } finally {
        B((l) => {
          const o = new Set(l);
          return o.delete(r), o;
        });
      }
    }
  }, w = (t, a) => {
    if (t.length === 0) return null;
    const r = t.map((n) => n.name.split(" ").length), i = a.map((n) => n.name.split(" ").length), l = r.reduce((n, c) => n + c, 0) / r.length, o = i.length > 0 ? i.reduce((n, c) => n + c, 0) / i.length : 0;
    return l <= 2 && o > 2 ? "short" : l <= 4 && (o <= 2 || o > 4) ? "medium" : l > 4 && o <= 4 ? "long" : l <= 2 ? "short" : l <= 4 ? "medium" : "long";
  }, q = (t, a) => {
    if (t.length === 0) return null;
    const r = t.map((l) => l.description.toLowerCase()).join(" "), i = a.map((l) => l.description.toLowerCase()).join(" ");
    return (r.includes("professional") || r.includes("business")) && !i.includes("professional") && !i.includes("business") ? "professional" : (r.includes("creative") || r.includes("unique")) && !i.includes("creative") && !i.includes("unique") ? "creative" : (r.includes("fun") || r.includes("playful")) && !i.includes("fun") && !i.includes("playful") ? "playful" : "descriptive";
  }, H = (t) => {
    const a = [];
    t.forEach((i) => {
      const l = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], o = i.name.toLowerCase().split(/\s+/).filter(
        (n) => n.length > 2 && !l.includes(n)
      );
      a.push(...o);
    });
    const r = {};
    return a.forEach((i) => r[i] = (r[i] || 0) + 1), Object.entries(r).sort(([, i], [, l]) => l - i).slice(0, 5).map(([i]) => i);
  }, fe = (t) => {
    const a = t.filter((i) => i.liked === !0), r = t.filter((i) => i.liked === !1);
    return {
      preferredLength: w(a, r),
      preferredStyle: q(a, r),
      likedKeywords: H(a),
      dislikedKeywords: H(r),
      preferredStructure: null
      // Simplified for now
    };
  }, Ce = (t, a) => {
    const r = [
      ...a.likedNames.map((n) => n.name.toLowerCase()),
      ...a.dislikedNames.map((n) => n.name.toLowerCase()),
      ...f.map((n) => n.name.toLowerCase())
    ], i = `Create 4 unique, high-converting podcast names for: ${t}`;
    let l = `

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;
    r.length > 0 && (l += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${r.map((n) => `- ${n}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);
    let o = "";
    if (a.patterns.preferredLength && (o += `
Focus on ${{
      short: "1-2 words (punchy and memorable)",
      medium: "2-3 words (balanced and brandable)",
      long: "3-4 words (descriptive but still catchy)"
    }[a.patterns.preferredLength]}. `), a.patterns.preferredStyle && (o += `Use ${{
      descriptive: "clear, straightforward names that explain the content",
      creative: "imaginative, metaphorical, or playful names",
      professional: "authoritative, business-focused names",
      playful: "fun, energetic, engaging names"
    }[a.patterns.preferredStyle] || a.patterns.preferredStyle}. `), a.patterns.likedKeywords.length > 0 && (o += `
Incorporate themes similar to: ${a.patterns.likedKeywords.join(", ")}. `), a.patterns.dislikedKeywords.length > 0 && (o += `
Avoid themes like: ${a.patterns.dislikedKeywords.join(", ")}. `), a.likedNames.length > 0) {
      const n = a.likedNames.slice(-2).map((c) => c.name).join('", "');
      o += `
Generate names with similar appeal to: "${n}" (but completely different concepts). `;
    }
    return `${i}${l}${o}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;
  }, we = (t, a, r = 1) => {
    const i = [
      ...a.likedNames.map((c) => c.name.toLowerCase()),
      ...a.dislikedNames.map((c) => c.name.toLowerCase()),
      ...f.map((c) => c.name.toLowerCase())
    ], l = `Create ${r} unique, high-converting podcast name${r > 1 ? "s" : ""} for: ${t}`;
    let o = `

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;
    i.length > 0 && (o += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${i.map((c) => `- ${c}`).join(`
`)}
Do not create names similar to any of the above.`);
    let n = "";
    return a.patterns.likedKeywords.length > 0 && (n += `
Incorporate themes similar to: ${a.patterns.likedKeywords.join(", ")}. `), a.patterns.dislikedKeywords.length > 0 && (n += `
Avoid themes like: ${a.patterns.dislikedKeywords.join(", ")}. `), `${l}${o}${n}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${r > 1 ? ', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}' : ""}]}`;
  }, je = async (t = !1) => {
    if (!j.trim()) {
      D("Please describe what your podcast is about");
      return;
    }
    if (!m()) {
      D(null);
      return;
    }
    z(!0), D(null), v([]), t ? W(!0) : (C([]), ge(!1), W(!1), me(!1));
    try {
      const a = t ? Ce(j, K) : `Create 4 unique, high-converting podcast names for: ${j}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`, r = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: a
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!r.ok)
        throw new Error(`API request failed: ${r.status} ${r.statusText}`);
      const i = await r.json();
      if (!i.candidates || !i.candidates[0] || !i.candidates[0].content)
        throw new Error("Invalid response format from API");
      const o = i.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!o)
        throw new Error("No valid JSON found in API response");
      const n = JSON.parse(o[0]);
      if (!n.podcast_names || !Array.isArray(n.podcast_names))
        throw new Error("Invalid response structure");
      v(n.podcast_names), b(4), G(n.podcast_names);
      const c = n.podcast_names.map((u, h) => ({
        name: u.name,
        description: u.description,
        liked: null,
        timestamp: Date.now(),
        index: h
      }));
      C(c);
    } catch (a) {
      console.error("Error generating podcast names:", a), D(a instanceof Error ? a.message : "An unexpected error occurred");
    } finally {
      z(!1), W(!1);
    }
  }, ye = async (t, a) => {
    try {
      await navigator.clipboard.writeText(t), Z(a), setTimeout(() => Z(null), 2e3);
    } catch (r) {
      console.error("Failed to copy text:", r);
    }
  }, ke = async (t, a) => {
    const r = f[t];
    if (r) {
      if (a) {
        const i = window.scrollY, l = () => window.scrollTo(0, i);
        window.addEventListener("scroll", l, { passive: !1 }), he((o) => /* @__PURE__ */ new Set([...o, t])), se(`"${r.name}" added to favorites!`), setTimeout(() => se(null), 2e3), setTimeout(() => {
          F((o) => o.find((n) => n.name === r.name) ? o : [...o, r]), setTimeout(() => {
            window.scrollTo(0, i);
          }, 0);
        }, 100), setTimeout(() => {
          he((o) => {
            const n = new Set(o);
            return n.delete(t), n;
          }), window.removeEventListener("scroll", l), window.scrollTo(0, i);
        }, 700), ce((o) => {
          const n = { ...o };
          return n.dislikedNames = n.dislikedNames.filter((c) => c.name !== r.name), n.likedNames.find((c) => c.name === r.name) || n.likedNames.push({
            name: r.name,
            description: r.description,
            liked: !0,
            timestamp: Date.now(),
            index: t
          }), n.patterns = fe([...n.likedNames, ...n.dislikedNames]), n;
        }), U((o) => /* @__PURE__ */ new Set([...o, t]));
      } else {
        const i = window.scrollY;
        U((l) => /* @__PURE__ */ new Set([...l, t])), ce((l) => {
          const o = { ...l };
          return o.likedNames = o.likedNames.filter((n) => n.name !== r.name), o.dislikedNames.find((n) => n.name === r.name) || o.dislikedNames.push({
            name: r.name,
            description: r.description,
            liked: !1,
            timestamp: Date.now(),
            index: t
          }), o.patterns = fe([...o.likedNames, ...o.dislikedNames]), o;
        }), setTimeout(() => {
          window.scrollTo(0, i);
        }, 0);
      }
      j.trim() && Pe(t), ve || me(!0);
    }
  }, Pe = async (t) => {
    var a, r, i, l, o, n;
    if (m()) {
      console.log(`🔄 Starting replacement generation for index ${t}`);
      try {
        const c = we(j, K, 1);
        console.log(`📝 Generated single name prompt for index ${t}:`, c.substring(0, 100) + "..."), console.log(`🌐 Making API call for index ${t}...`);
        const u = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: c
              }]
            }]
          })
        });
        if (console.log(`📡 API response status for index ${t}:`, u.status), !u.ok)
          throw new Error(`Failed to generate replacement suggestion: ${u.status} ${u.statusText}`);
        const h = await u.json();
        console.log(`📦 API response data for index ${t}:`, h);
        const E = (o = (l = (i = (r = (a = h.candidates) == null ? void 0 : a[0]) == null ? void 0 : r.content) == null ? void 0 : i.parts) == null ? void 0 : l[0]) == null ? void 0 : o.text;
        if (!E)
          throw new Error("No content in API response");
        console.log(`📄 API content for index ${t}:`, E.substring(0, 200) + "...");
        const x = E.match(/\{[\s\S]*\}/);
        if (!x)
          throw console.error(`❌ No valid JSON found in response for index ${t}:`, E), new Error("No valid JSON found in response");
        const re = JSON.parse(x[0]);
        console.log(`🔍 Parsed response for index ${t}:`, re);
        const A = (n = re.podcast_names) == null ? void 0 : n[0];
        if (A) {
          if (console.log(`✅ New name generated for index ${t}:`, A), v((L) => {
            const N = [...L];
            return N[t] = {
              name: A.name,
              description: A.description,
              suggestedDomain: A.suggestedDomain,
              domainStatus: "checking"
            }, console.log(`🔄 Updated results for index ${t}:`, N[t]), N;
          }), U((L) => {
            const N = new Set(L);
            return N.delete(t), console.log(`🗑️ Removed index ${t} from pending replacements. Remaining:`, Array.from(N)), N;
          }), C((L) => {
            const N = L.filter((p) => p.index !== t);
            return console.log(`🧹 Cleared feedback for index ${t}. Remaining feedback:`, N), N;
          }), A.suggestedDomain) {
            console.log(`🌐 Checking domain availability for ${A.suggestedDomain}...`), B((N) => /* @__PURE__ */ new Set([...N, t]));
            const L = await X(A.suggestedDomain);
            console.log(`🏷️ Domain status for ${A.suggestedDomain}:`, L), v((N) => {
              const p = [...N];
              return p[t] && (p[t].domainStatus = L), p;
            }), B((N) => {
              const p = new Set(N);
              return p.delete(t), p;
            });
          }
          console.log(`🎉 Successfully completed replacement for index ${t}`);
        } else
          throw console.error(`❌ No new name found in parsed response for index ${t}:`, re), new Error("No new name found in API response");
      } catch (c) {
        console.error(`❌ Error generating replacement suggestion for index ${t}:`, c), U((u) => {
          const h = new Set(u);
          return h.delete(t), console.log(`🗑️ Removed index ${t} from pending on error. Remaining:`, Array.from(h)), h;
        }), C((u) => {
          const h = u.filter((E) => E.index !== t);
          return console.log(`🧹 Cleared feedback for index ${t} on error. Remaining:`, h), h;
        });
      }
    }
  }, _e = () => {
    j.trim() && $e(j);
  }, $e = async (t) => {
    var a, r, i, l, o, n, c, u, h, E;
    z(!0), D(""), W(!0);
    try {
      const x = f.filter((p, R) => {
        const g = ee.find((S) => S.index === R);
        return (g == null ? void 0 : g.liked) === !0;
      }), re = f.filter((p, R) => {
        const g = ee.find((S) => S.index === R);
        return (g == null ? void 0 : g.liked) === !1;
      }).length, A = Math.max(1, re), N = Math.min(5, x.length + A) - x.length;
      if (N <= 0) {
        const p = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: we(t, K, 1)
              }]
            }]
          })
        });
        if (!p.ok)
          throw new Error(`API request failed: ${p.status}`);
        const g = (o = (l = (i = (r = (a = (await p.json()).candidates) == null ? void 0 : a[0]) == null ? void 0 : r.content) == null ? void 0 : i.parts) == null ? void 0 : l[0]) == null ? void 0 : o.text;
        if (!g)
          throw new Error("No content received from API");
        const S = g.match(/\{[\s\S]*\}/);
        if (!S)
          throw new Error("No valid JSON found in response");
        const I = JSON.parse(S[0]);
        if (!I.podcast_names || !Array.isArray(I.podcast_names))
          throw new Error("Invalid response format");
        const oe = [...x, ...I.podcast_names].slice(0, 5);
        v(oe), G(oe);
      } else {
        const p = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: we(t, K, N)
              }]
            }]
          })
        });
        if (!p.ok)
          throw new Error(`API request failed: ${p.status}`);
        const g = (E = (h = (u = (c = (n = (await p.json()).candidates) == null ? void 0 : n[0]) == null ? void 0 : c.content) == null ? void 0 : u.parts) == null ? void 0 : h[0]) == null ? void 0 : E.text;
        if (!g)
          throw new Error("No content received from API");
        const S = g.match(/\{[\s\S]*\}/);
        if (!S)
          throw new Error("No valid JSON found in response");
        const I = JSON.parse(S[0]);
        if (!I.podcast_names || !Array.isArray(I.podcast_names))
          throw new Error("Invalid response format");
        const oe = [...x, ...I.podcast_names];
        v(oe), G(oe);
      }
      C((p) => p.filter((R) => {
        const g = f[R.index];
        return x.some((S) => S.name === (g == null ? void 0 : g.name));
      })), C((p) => p.map((R) => {
        const g = f[R.index], S = x.findIndex((I) => I.name === (g == null ? void 0 : g.name));
        return S >= 0 ? { ...R, index: S } : R;
      }).filter((R) => R.index >= 0));
    } catch (x) {
      console.error("Error generating refined names:", x), D(x instanceof Error ? x.message : "Failed to generate refined names. Please try again.");
    } finally {
      z(!1), W(!1);
    }
  }, Se = (t) => {
    t.preventDefault(), je();
  }, Re = (t) => {
    t.key === "Enter" && !t.shiftKey && (t.preventDefault(), je());
  };
  return /* @__PURE__ */ e.jsx("div", { className: `podcast-name-generator ${$}`, style: Y, children: /* @__PURE__ */ e.jsxs("div", { className: "generator-container", children: [
    /* @__PURE__ */ e.jsxs("div", { className: "header-section", children: [
      /* @__PURE__ */ e.jsx("h1", { className: "main-title", children: "Free Podcast Name Generator" }),
      /* @__PURE__ */ e.jsx("h2", { className: "main-subtitle", children: "Create the Perfect Name for Your Podcast in Seconds" })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: "benefits-section", children: [
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "100% Free Forever" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "No Sign-up Required" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "Instant Results" })
      ] })
    ] }),
    P && /* @__PURE__ */ e.jsx("div", { className: "limit-reached-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "limit-content", children: [
      /* @__PURE__ */ e.jsx("span", { className: "limit-icon", children: "⚠️" }),
      /* @__PURE__ */ e.jsx("div", { className: "limit-text", children: /* @__PURE__ */ e.jsx("p", { children: "You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below." }) })
    ] }) }),
    f.length === 0 && /* @__PURE__ */ e.jsx("div", { className: "initial-input-section", children: /* @__PURE__ */ e.jsx("form", { onSubmit: Se, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
      /* @__PURE__ */ e.jsx(
        "textarea",
        {
          value: j,
          onChange: (t) => M(t.target.value),
          onKeyPress: Re,
          placeholder: "Describe what your podcast is about",
          className: "input-field",
          rows: 3,
          disabled: T
        }
      ),
      /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
        /* @__PURE__ */ e.jsx(
          "button",
          {
            type: "submit",
            disabled: T || !j.trim() || P,
            className: `generate-button ${P ? "disabled" : ""}`,
            children: T ? "Generating..." : P ? "Daily Limit Reached" : "Generate Names"
          }
        ),
        /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
          ] }),
          /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
            /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star star-partial", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
            ] }),
            /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
          ] })
        ] })
      ] })
    ] }) }) }),
    Q && /* @__PURE__ */ e.jsxs("div", { className: "error-message", children: [
      /* @__PURE__ */ e.jsx("span", { className: "error-icon", children: "⚠️" }),
      Q
    ] }),
    V && /* @__PURE__ */ e.jsxs("div", { className: "success-message", children: [
      /* @__PURE__ */ e.jsx("span", { className: "success-icon", children: "✨" }),
      V
    ] }),
    T && /* @__PURE__ */ e.jsxs("div", { className: "loading-container", children: [
      /* @__PURE__ */ e.jsx("div", { className: "loading-spinner" }),
      /* @__PURE__ */ e.jsx("p", { children: de ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
    ] }),
    f.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "results-container", children: [
      O.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "favorites-section", children: [
        /* @__PURE__ */ e.jsxs("div", { className: "favorites-header", children: [
          /* @__PURE__ */ e.jsxs("h3", { children: [
            "🏆 Your Winning Podcast Names (",
            O.length,
            ")"
          ] }),
          /* @__PURE__ */ e.jsx("p", { className: "favorites-subtitle", children: "Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!" })
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "favorites-grid", children: O.map((t, a) => /* @__PURE__ */ e.jsxs("div", { className: "favorite-card", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "favorite-content", children: [
            /* @__PURE__ */ e.jsx("h4", { className: "favorite-name", children: t.name }),
            /* @__PURE__ */ e.jsx("p", { className: "favorite-description", children: t.description }),
            t.suggestedDomain && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
              /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
              /* @__PURE__ */ e.jsx("span", { className: "domain-name", children: t.suggestedDomain }),
              /* @__PURE__ */ e.jsx("span", { className: `domain-status ${t.domainStatus}`, children: t.domainStatus === "available" ? "✅ Available" : t.domainStatus === "taken" ? "❌ Taken" : t.domainStatus === "error" ? "⚠️ Check manually" : "🔍 Checking..." })
            ] })
          ] }),
          /* @__PURE__ */ e.jsx("div", { className: "favorite-actions", children: /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: () => ye(t.name, -1),
              className: "copy-button small",
              title: "Copy to clipboard",
              children: "📋 Copy"
            }
          ) })
        ] }, `fav-${a}`)) })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "input-section-simple", children: [
        /* @__PURE__ */ e.jsx("div", { className: "input-help-message-simple", children: /* @__PURE__ */ e.jsxs("p", { className: "input-sub-description", children: [
          "💡 Want different suggestions? Update your description below - ",
          /* @__PURE__ */ e.jsx("strong", { children: "your favorites will stay safe!" })
        ] }) }),
        /* @__PURE__ */ e.jsx("form", { onSubmit: Se, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
          /* @__PURE__ */ e.jsx(
            "textarea",
            {
              value: j,
              onChange: (t) => M(t.target.value),
              onKeyPress: Re,
              placeholder: "Describe what your podcast is about",
              className: "input-field",
              rows: 3,
              disabled: T
            }
          ),
          /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
            /* @__PURE__ */ e.jsx(
              "button",
              {
                type: "submit",
                disabled: T || !j.trim() || P,
                className: `generate-button ${P ? "disabled" : ""}`,
                children: T ? "Generating..." : P ? "Daily Limit Reached" : "Generate Names"
              }
            ),
            /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
              /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
              ] }),
              /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
                /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star star-partial", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
                ] }),
                /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
              ] })
            ] })
          ] })
        ] }) })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "suggestions-section", children: [
        /* @__PURE__ */ e.jsx("div", { className: "suggestions-header", children: /* @__PURE__ */ e.jsx("h3", { children: "🎯 Current Suggestions" }) }),
        /* @__PURE__ */ e.jsx("div", { className: "onboarding-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "onboarding-content", children: [
          /* @__PURE__ */ e.jsx("span", { className: "onboarding-icon", children: "💡" }),
          /* @__PURE__ */ e.jsxs("div", { className: "onboarding-text", children: [
            /* @__PURE__ */ e.jsx("strong", { children: "Smart AI Learning:" }),
            " The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."
          ] })
        ] }) }),
        /* @__PURE__ */ e.jsx("div", { className: "results-grid", children: f.map((t, a) => {
          const r = ee.find((c) => c.index === a), i = (r == null ? void 0 : r.liked) === !0, l = (r == null ? void 0 : r.liked) === !1, o = Ne.has(a), n = te.has(a);
          return /* @__PURE__ */ e.jsxs(
            "div",
            {
              className: `result-card ${i ? "liked" : ""} ${l ? "disliked" : ""} ${o ? "pending" : ""} ${ue.has(a) ? "flying-to-favorites" : ""}`,
              style: {
                opacity: o ? 0.6 : 1,
                pointerEvents: o ? "none" : "auto"
              },
              children: [
                /* @__PURE__ */ e.jsxs("div", { className: "result-header", children: [
                  /* @__PURE__ */ e.jsx("h4", { className: "result-name", children: o ? i ? "Generating new suggestion..." : "Generating better suggestion..." : t.name }),
                  /* @__PURE__ */ e.jsxs("div", { className: "result-actions", children: [
                    /* @__PURE__ */ e.jsxs("div", { className: "feedback-buttons", children: [
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => ke(a, !0),
                          className: `feedback-button like-button ${i ? "active" : ""}`,
                          title: "I like this name",
                          disabled: o,
                          children: "👍"
                        }
                      ),
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => ke(a, !1),
                          className: `feedback-button dislike-button ${l ? "active" : ""} ${o ? "loading" : ""}`,
                          title: o ? "Generating replacement..." : "I don't like this name",
                          disabled: o,
                          children: o ? "🔄" : "👎"
                        }
                      )
                    ] }),
                    /* @__PURE__ */ e.jsx(
                      "button",
                      {
                        onClick: () => ye(t.name, a),
                        className: "copy-button",
                        title: "Copy podcast name",
                        disabled: o,
                        children: J === a ? "✓ Copied!" : "📋 Copy"
                      }
                    )
                  ] })
                ] }),
                /* @__PURE__ */ e.jsx("p", { className: "result-description", children: o ? i ? "Added to favorites! Generating a new suggestion..." : "Creating a better suggestion based on your preferences..." : t.description }),
                t.suggestedDomain && !n && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
                  /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
                  /* @__PURE__ */ e.jsxs("code", { className: "domain-text", children: [
                    t.suggestedDomain,
                    ".com"
                  ] }),
                  /* @__PURE__ */ e.jsxs("span", { className: `domain-status ${t.domainStatus}`, children: [
                    (t.domainStatus === "checking" || te.has(a)) && "⏳ Checking...",
                    t.domainStatus === "available" && "✅ Available",
                    t.domainStatus === "taken" && "❌ Taken",
                    t.domainStatus === "error" && "⚠️ Check manually"
                  ] })
                ] })
              ]
            },
            a
          );
        }) })
      ] }),
      pe && !T && /* @__PURE__ */ e.jsxs("div", { className: "refinement-section", children: [
        /* @__PURE__ */ e.jsx("div", { className: "refinement-info", children: /* @__PURE__ */ e.jsx("p", { children: "💡 I'll keep your liked names and replace the disliked ones with better suggestions!" }) }),
        /* @__PURE__ */ e.jsx(
          "button",
          {
            onClick: _e,
            className: "refinement-button",
            disabled: T,
            children: de ? "🔄 Refining..." : "🎯 Replace Disliked Names"
          }
        )
      ] })
    ] })
  ] }) });
};
export {
  Ie as default
};

import ve, { useState as x } from "react";
var me = { exports: {} }, H = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var he;
function we() {
  if (he) return H;
  he = 1;
  var j = Symbol.for("react.transitional.element"), F = Symbol.for("react.fragment");
  function P(k, v, f) {
    var w = null;
    if (f !== void 0 && (w = "" + f), v.key !== void 0 && (w = "" + v.key), "key" in v) {
      f = {};
      for (var h in v)
        h !== "key" && (f[h] = v[h]);
    } else f = v;
    return v = f.ref, {
      $$typeof: j,
      type: k,
      key: w,
      ref: v !== void 0 ? v : null,
      props: f
    };
  }
  return H.Fragment = F, H.jsx = P, H.jsxs = P, H;
}
var X = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ge;
function Ne() {
  return ge || (ge = 1, process.env.NODE_ENV !== "production" && function() {
    function j(e) {
      if (e == null) return null;
      if (typeof e == "function")
        return e.$$typeof === se ? null : e.displayName || e.name || null;
      if (typeof e == "string") return e;
      switch (e) {
        case N:
          return "Fragment";
        case le:
          return "Profiler";
        case M:
          return "StrictMode";
        case J:
          return "Suspense";
        case ne:
          return "SuspenseList";
        case re:
          return "Activity";
      }
      if (typeof e == "object")
        switch (typeof e.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), e.$$typeof) {
          case C:
            return "Portal";
          case te:
            return (e.displayName || "Context") + ".Provider";
          case ee:
            return (e._context.displayName || "Context") + ".Consumer";
          case I:
            var o = e.render;
            return e = e.displayName, e || (e = o.displayName || o.name || "", e = e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef"), e;
          case de:
            return o = e.displayName || null, o !== null ? o : j(e.type) || "Memo";
          case G:
            o = e._payload, e = e._init;
            try {
              return j(e(o));
            } catch {
            }
        }
      return null;
    }
    function F(e) {
      return "" + e;
    }
    function P(e) {
      try {
        F(e);
        var o = !1;
      } catch {
        o = !0;
      }
      if (o) {
        o = console;
        var l = o.error, u = typeof Symbol == "function" && Symbol.toStringTag && e[Symbol.toStringTag] || e.constructor.name || "Object";
        return l.call(
          o,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          u
        ), F(e);
      }
    }
    function k(e) {
      if (e === N) return "<>";
      if (typeof e == "object" && e !== null && e.$$typeof === G)
        return "<...>";
      try {
        var o = j(e);
        return o ? "<" + o + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function v() {
      var e = z.A;
      return e === null ? null : e.getOwner();
    }
    function f() {
      return Error("react-stack-top-frame");
    }
    function w(e) {
      if (U.call(e, "key")) {
        var o = Object.getOwnPropertyDescriptor(e, "key").get;
        if (o && o.isReactWarning) return !1;
      }
      return e.key !== void 0;
    }
    function h(e, o) {
      function l() {
        Y || (Y = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          o
        ));
      }
      l.isReactWarning = !0, Object.defineProperty(e, "key", {
        get: l,
        configurable: !0
      });
    }
    function L() {
      var e = j(this.type);
      return ae[e] || (ae[e] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), e = this.props.ref, e !== void 0 ? e : null;
    }
    function Z(e, o, l, u, E, y, K, B) {
      return l = y.ref, e = {
        $$typeof: O,
        type: e,
        key: o,
        props: y,
        _owner: E
      }, (l !== void 0 ? l : null) !== null ? Object.defineProperty(e, "ref", {
        enumerable: !1,
        get: L
      }) : Object.defineProperty(e, "ref", { enumerable: !1, value: null }), e._store = {}, Object.defineProperty(e._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(e, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(e, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: K
      }), Object.defineProperty(e, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: B
      }), Object.freeze && (Object.freeze(e.props), Object.freeze(e)), e;
    }
    function T(e, o, l, u, E, y, K, B) {
      var t = o.children;
      if (t !== void 0)
        if (u)
          if (ue(t)) {
            for (u = 0; u < t.length; u++)
              Q(t[u]);
            Object.freeze && Object.freeze(t);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else Q(t);
      if (U.call(o, "key")) {
        t = j(e);
        var n = Object.keys(o).filter(function(s) {
          return s !== "key";
        });
        u = 0 < n.length ? "{key: someKey, " + n.join(": ..., ") + ": ...}" : "{key: someKey}", ie[t + u] || (n = 0 < n.length ? "{" + n.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          u,
          t,
          n,
          t
        ), ie[t + u] = !0);
      }
      if (t = null, l !== void 0 && (P(l), t = "" + l), w(o) && (P(o.key), t = "" + o.key), "key" in o) {
        l = {};
        for (var r in o)
          r !== "key" && (l[r] = o[r]);
      } else l = o;
      return t && h(
        l,
        typeof e == "function" ? e.displayName || e.name || "Unknown" : e
      ), Z(
        e,
        t,
        y,
        E,
        v(),
        l,
        K,
        B
      );
    }
    function Q(e) {
      typeof e == "object" && e !== null && e.$$typeof === O && e._store && (e._store.validated = 1);
    }
    var A = ve, O = Symbol.for("react.transitional.element"), C = Symbol.for("react.portal"), N = Symbol.for("react.fragment"), M = Symbol.for("react.strict_mode"), le = Symbol.for("react.profiler"), ee = Symbol.for("react.consumer"), te = Symbol.for("react.context"), I = Symbol.for("react.forward_ref"), J = Symbol.for("react.suspense"), ne = Symbol.for("react.suspense_list"), de = Symbol.for("react.memo"), G = Symbol.for("react.lazy"), re = Symbol.for("react.activity"), se = Symbol.for("react.client.reference"), z = A.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, U = Object.prototype.hasOwnProperty, ue = Array.isArray, W = console.createTask ? console.createTask : function() {
      return null;
    };
    A = {
      "react-stack-bottom-frame": function(e) {
        return e();
      }
    };
    var Y, ae = {}, oe = A["react-stack-bottom-frame"].bind(
      A,
      f
    )(), q = W(k(f)), ie = {};
    X.Fragment = N, X.jsx = function(e, o, l, u, E) {
      var y = 1e4 > z.recentlyCreatedOwnerStacks++;
      return T(
        e,
        o,
        l,
        !1,
        u,
        E,
        y ? Error("react-stack-top-frame") : oe,
        y ? W(k(e)) : q
      );
    }, X.jsxs = function(e, o, l, u, E) {
      var y = 1e4 > z.recentlyCreatedOwnerStacks++;
      return T(
        e,
        o,
        l,
        !0,
        u,
        E,
        y ? Error("react-stack-top-frame") : oe,
        y ? W(k(e)) : q
      );
    };
  }()), X;
}
process.env.NODE_ENV === "production" ? me.exports = we() : me.exports = Ne();
var a = me.exports;
const je = ({
  apiKey: j = "AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",
  className: F = "",
  style: P = {}
}) => {
  const [k, v] = x(""), [f, w] = x([]), [h, L] = x(!1), [Z, T] = x(null), [Q, A] = x(null), [O, C] = x([]), [N, M] = x({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    },
    generationRound: 0
  }), [le, ee] = x(!1), [te, I] = x(!1), [J, ne] = x([]), [de, G] = x(!0), [re, se] = x(!1), [z, U] = x(/* @__PURE__ */ new Set()), ue = (t) => t.toLowerCase().replace(/[^a-z0-9\s]/g, "").replace(/\s+/g, "").replace(/^the/, "").substring(0, 50), W = async (t) => {
    try {
      const n = await fetch(`https://dns.google/resolve?name=${t}&type=A`, {
        method: "GET",
        headers: {
          Accept: "application/json"
        }
      });
      if (!n.ok)
        return "error";
      const r = await n.json();
      return r.Answer && r.Answer.length > 0 ? "taken" : "available";
    } catch (n) {
      return console.warn("Domain check failed:", n), "error";
    }
  }, Y = async (t) => {
    const n = [...t];
    for (let r = 0; r < n.length; r++) {
      const s = ue(n[r].name);
      n[r].suggestedDomain = s, n[r].domainStatus = "checking", U((i) => /* @__PURE__ */ new Set([...i, r]));
    }
    w(n);
    for (let r = 0; r < n.length; r++) {
      const s = `${n[r].suggestedDomain}.com`;
      try {
        const i = await W(s);
        w((d) => {
          const c = [...d];
          return c[r] && (c[r].domainStatus = i), c;
        });
      } catch {
        w((d) => {
          const c = [...d];
          return c[r] && (c[r].domainStatus = "error"), c;
        });
      } finally {
        U((i) => {
          const d = new Set(i);
          return d.delete(r), d;
        });
      }
    }
  }, ae = (t, n) => {
    if (t.length === 0) return null;
    const r = t.map((c) => c.name.split(" ").length), s = n.map((c) => c.name.split(" ").length), i = r.reduce((c, _) => c + _, 0) / r.length, d = s.length > 0 ? s.reduce((c, _) => c + _, 0) / s.length : 0;
    return i <= 2 && d > 2 ? "short" : i <= 4 && (d <= 2 || d > 4) ? "medium" : i > 4 && d <= 4 ? "long" : i <= 2 ? "short" : i <= 4 ? "medium" : "long";
  }, oe = (t, n) => {
    if (t.length === 0) return null;
    const r = t.map((i) => i.description.toLowerCase()).join(" "), s = n.map((i) => i.description.toLowerCase()).join(" ");
    return (r.includes("professional") || r.includes("business")) && !s.includes("professional") && !s.includes("business") ? "professional" : (r.includes("creative") || r.includes("unique")) && !s.includes("creative") && !s.includes("unique") ? "creative" : (r.includes("fun") || r.includes("playful")) && !s.includes("fun") && !s.includes("playful") ? "playful" : "descriptive";
  }, q = (t) => {
    const n = [];
    t.forEach((s) => {
      const i = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], d = s.name.toLowerCase().split(/\s+/).filter(
        (c) => c.length > 2 && !i.includes(c)
      );
      n.push(...d);
    });
    const r = {};
    return n.forEach((s) => r[s] = (r[s] || 0) + 1), Object.entries(r).sort(([, s], [, i]) => i - s).slice(0, 5).map(([s]) => s);
  }, ie = (t) => {
    const n = t.filter((s) => s.liked === !0), r = t.filter((s) => s.liked === !1);
    return {
      preferredLength: ae(n, r),
      preferredStyle: oe(n, r),
      likedKeywords: q(n),
      dislikedKeywords: q(r),
      preferredStructure: null
      // Simplified for now
    };
  }, e = (t, n) => {
    const r = `Create 5 high-converting and catchy podcast names based on the following user input: ${t}.`;
    let s = "";
    if (n.patterns.preferredLength) {
      const i = {
        short: "1-2 words",
        medium: "3-4 words",
        long: "5+ words"
      };
      s += `Focus on ${n.patterns.preferredLength} names (${i[n.patterns.preferredLength]}). `;
    }
    if (n.patterns.preferredStyle && (s += `Use a ${n.patterns.preferredStyle} style. `), n.patterns.likedKeywords.length > 0 && (s += `Incorporate concepts similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (s += `Avoid concepts like: ${n.patterns.dislikedKeywords.join(", ")}. `), n.likedNames.length > 0) {
      const i = n.likedNames.slice(-2).map((d) => d.name).join('", "');
      s += `Generate names with similar appeal to: "${i}". `;
    }
    return `${r} ${s} Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`;
  }, o = async (t = !1) => {
    if (!k.trim()) {
      T("Please describe what your podcast is about");
      return;
    }
    L(!0), T(null), w([]), t ? I(!0) : (C([]), ee(!1), I(!1), G(!0), se(!1));
    try {
      const n = t ? e(k, N) : `Create 5 high-converting and catchy podcast names based on the following user input: ${k}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`, r = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${j}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: n
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!r.ok)
        throw new Error(`API request failed: ${r.status} ${r.statusText}`);
      const s = await r.json();
      if (!s.candidates || !s.candidates[0] || !s.candidates[0].content)
        throw new Error("Invalid response format from API");
      const d = s.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!d)
        throw new Error("No valid JSON found in API response");
      const c = JSON.parse(d[0]);
      if (!c.podcast_names || !Array.isArray(c.podcast_names))
        throw new Error("Invalid response structure");
      w(c.podcast_names), Y(c.podcast_names), ne((R) => [...R, c.podcast_names]), M((R) => ({
        ...R,
        generationRound: R.generationRound + 1
      }));
      const _ = c.podcast_names.map((R, ce) => ({
        name: R.name,
        description: R.description,
        liked: null,
        timestamp: Date.now(),
        index: ce
      }));
      C(_);
    } catch (n) {
      console.error("Error generating podcast names:", n), T(n instanceof Error ? n.message : "An unexpected error occurred");
    } finally {
      L(!1), I(!1);
    }
  }, l = async (t, n) => {
    try {
      await navigator.clipboard.writeText(t), A(n), setTimeout(() => A(null), 2e3);
    } catch (r) {
      console.error("Failed to copy text:", r);
    }
  }, u = (t, n) => {
    const r = [...O];
    r[t] = {
      ...r[t],
      liked: n,
      timestamp: Date.now()
    }, C(r);
    const s = { ...N }, i = r[t];
    n ? (s.dislikedNames = s.dislikedNames.filter((c) => c.name !== i.name), s.likedNames.find((c) => c.name === i.name) || s.likedNames.push(i)) : (s.likedNames = s.likedNames.filter((c) => c.name !== i.name), s.dislikedNames.find((c) => c.name === i.name) || s.dislikedNames.push(i)), s.patterns = ie([...s.likedNames, ...s.dislikedNames]), M(s), s.likedNames.length + s.dislikedNames.length >= 2 && ee(!0), re || (se(!0), G(!1));
  }, E = () => {
    k.trim() && y(k);
  }, y = async (t) => {
    var n, r, s, i, d, c, _, R, ce, fe;
    L(!0), T(""), I(!0);
    try {
      const S = f.filter((g, m) => {
        const p = O.find((b) => b.index === m);
        return (p == null ? void 0 : p.liked) === !0;
      }), be = f.filter((g, m) => {
        const p = O.find((b) => b.index === m);
        return (p == null ? void 0 : p.liked) === !1;
      }).length, ke = Math.max(1, be), pe = Math.min(5, S.length + ke) - S.length;
      if (pe <= 0) {
        const g = e(t, N), m = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=" + j, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: g.replace("Create 5", "Create 1")
              }]
            }]
          })
        });
        if (!m.ok)
          throw new Error(`API request failed: ${m.status}`);
        const b = (d = (i = (s = (r = (n = (await m.json()).candidates) == null ? void 0 : n[0]) == null ? void 0 : r.content) == null ? void 0 : s.parts) == null ? void 0 : i[0]) == null ? void 0 : d.text;
        if (!b)
          throw new Error("No content received from API");
        const $ = b.match(/\{[\s\S]*\}/);
        if (!$)
          throw new Error("No valid JSON found in response");
        const D = JSON.parse($[0]);
        if (!D.podcast_names || !Array.isArray(D.podcast_names))
          throw new Error("Invalid response format");
        const V = [...S, ...D.podcast_names].slice(0, 5);
        w(V), Y(V);
      } else {
        const g = e(t, N), m = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=" + j, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: g.replace("Create 5", `Create ${pe}`)
              }]
            }]
          })
        });
        if (!m.ok)
          throw new Error(`API request failed: ${m.status}`);
        const b = (fe = (ce = (R = (_ = (c = (await m.json()).candidates) == null ? void 0 : c[0]) == null ? void 0 : _.content) == null ? void 0 : R.parts) == null ? void 0 : ce[0]) == null ? void 0 : fe.text;
        if (!b)
          throw new Error("No content received from API");
        const $ = b.match(/\{[\s\S]*\}/);
        if (!$)
          throw new Error("No valid JSON found in response");
        const D = JSON.parse($[0]);
        if (!D.podcast_names || !Array.isArray(D.podcast_names))
          throw new Error("Invalid response format");
        const V = [...S, ...D.podcast_names];
        w(V), Y(V);
      }
      ne((g) => [...g, f]), M((g) => ({
        ...g,
        generationRound: g.generationRound + 1
      })), C((g) => g.filter((m) => {
        const p = f[m.index];
        return S.some((b) => b.name === (p == null ? void 0 : p.name));
      })), C((g) => g.map((m) => {
        const p = f[m.index], b = S.findIndex(($) => $.name === (p == null ? void 0 : p.name));
        return b >= 0 ? { ...m, index: b } : m;
      }).filter((m) => m.index >= 0));
    } catch (S) {
      console.error("Error generating refined names:", S), T(S instanceof Error ? S.message : "Failed to generate refined names. Please try again.");
    } finally {
      L(!1), I(!1);
    }
  }, K = (t) => {
    t.preventDefault(), o();
  }, B = (t) => {
    t.key === "Enter" && !t.shiftKey && (t.preventDefault(), o());
  };
  return /* @__PURE__ */ a.jsx("div", { className: `podcast-name-generator ${F}`, style: P, children: /* @__PURE__ */ a.jsxs("div", { className: "generator-container", children: [
    /* @__PURE__ */ a.jsx("h2", { className: "generator-title", children: "Podcast Name Generator" }),
    /* @__PURE__ */ a.jsx("p", { className: "generator-subtitle", children: "Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI" }),
    de && /* @__PURE__ */ a.jsx("div", { className: "onboarding-banner", children: /* @__PURE__ */ a.jsxs("div", { className: "onboarding-content", children: [
      /* @__PURE__ */ a.jsx("span", { className: "onboarding-icon", children: "💡" }),
      /* @__PURE__ */ a.jsxs("div", { className: "onboarding-text", children: [
        /* @__PURE__ */ a.jsx("strong", { children: "Pro Tip:" }),
        " After generating names, use the 👍👎 buttons to teach the AI your preferences. It will then generate better, more personalized suggestions just for you!"
      ] })
    ] }) }),
    /* @__PURE__ */ a.jsx("form", { onSubmit: K, className: "input-form", children: /* @__PURE__ */ a.jsxs("div", { className: "input-container", children: [
      /* @__PURE__ */ a.jsx(
        "textarea",
        {
          value: k,
          onChange: (t) => v(t.target.value),
          onKeyPress: B,
          placeholder: "Describe what your podcast is about",
          className: "input-field",
          rows: 3,
          disabled: h
        }
      ),
      /* @__PURE__ */ a.jsx(
        "button",
        {
          type: "submit",
          disabled: h || !k.trim(),
          className: "generate-button",
          children: h ? "Generating..." : "Generate Names"
        }
      )
    ] }) }),
    Z && /* @__PURE__ */ a.jsxs("div", { className: "error-message", children: [
      /* @__PURE__ */ a.jsx("span", { className: "error-icon", children: "⚠️" }),
      Z
    ] }),
    h && /* @__PURE__ */ a.jsxs("div", { className: "loading-container", children: [
      /* @__PURE__ */ a.jsx("div", { className: "loading-spinner" }),
      /* @__PURE__ */ a.jsx("p", { children: te ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
    ] }),
    f.length > 0 && /* @__PURE__ */ a.jsxs("div", { className: "results-container", children: [
      /* @__PURE__ */ a.jsxs("h3", { className: "results-title", children: [
        N.generationRound > 1 ? "Refined Podcast Name Suggestions" : "Your Podcast Name Suggestions",
        J.length > 1 && /* @__PURE__ */ a.jsxs("span", { className: "generation-counter", children: [
          " (Round ",
          N.generationRound,
          ")"
        ] })
      ] }),
      N.generationRound > 1 && /* @__PURE__ */ a.jsxs("p", { className: "refinement-info", children: [
        "✨ These names are tailored based on your preferences from ",
        J.length - 1,
        " previous round",
        J.length > 2 ? "s" : ""
      ] }),
      N.generationRound === 1 && !re && /* @__PURE__ */ a.jsx("div", { className: "feedback-hint", children: /* @__PURE__ */ a.jsxs("div", { className: "feedback-hint-content", children: [
        /* @__PURE__ */ a.jsx("span", { className: "feedback-hint-icon", children: "👆" }),
        /* @__PURE__ */ a.jsxs("p", { children: [
          /* @__PURE__ */ a.jsx("strong", { children: "Like what you see?" }),
          " Use the 👍👎 buttons below to rate each name. The AI will learn your style and generate even better suggestions!"
        ] })
      ] }) }),
      /* @__PURE__ */ a.jsx("div", { className: "results-grid", children: f.map((t, n) => {
        const r = O.find((d) => d.index === n), s = (r == null ? void 0 : r.liked) === !0, i = (r == null ? void 0 : r.liked) === !1;
        return /* @__PURE__ */ a.jsxs(
          "div",
          {
            className: `result-card ${s ? "liked" : ""} ${i ? "disliked" : ""}`,
            children: [
              /* @__PURE__ */ a.jsxs("div", { className: "result-header", children: [
                /* @__PURE__ */ a.jsxs("h4", { className: "result-name", children: [
                  t.name,
                  s && N.generationRound > 1 && /* @__PURE__ */ a.jsx("span", { className: "kept-indicator", title: "This name was kept from your previous selection", children: "⭐" })
                ] }),
                /* @__PURE__ */ a.jsxs("div", { className: "result-actions", children: [
                  /* @__PURE__ */ a.jsxs("div", { className: "feedback-buttons", children: [
                    /* @__PURE__ */ a.jsx(
                      "button",
                      {
                        onClick: () => u(n, !0),
                        className: `feedback-button like-button ${s ? "active" : ""}`,
                        title: "I like this name",
                        disabled: h,
                        children: "👍"
                      }
                    ),
                    /* @__PURE__ */ a.jsx(
                      "button",
                      {
                        onClick: () => u(n, !1),
                        className: `feedback-button dislike-button ${i ? "active" : ""}`,
                        title: "I don't like this name",
                        disabled: h,
                        children: "👎"
                      }
                    )
                  ] }),
                  /* @__PURE__ */ a.jsx(
                    "button",
                    {
                      onClick: () => l(t.name, n),
                      className: "copy-button",
                      title: "Copy podcast name",
                      children: Q === n ? "✓ Copied!" : "📋 Copy"
                    }
                  )
                ] })
              ] }),
              /* @__PURE__ */ a.jsx("p", { className: "result-description", children: t.description }),
              t.suggestedDomain && /* @__PURE__ */ a.jsxs("div", { className: "domain-info", children: [
                /* @__PURE__ */ a.jsxs("div", { className: "domain-name", children: [
                  /* @__PURE__ */ a.jsx("span", { className: "domain-label", children: "Domain:" }),
                  /* @__PURE__ */ a.jsxs("code", { className: "domain-text", children: [
                    t.suggestedDomain,
                    ".com"
                  ] })
                ] }),
                /* @__PURE__ */ a.jsxs("div", { className: `domain-status ${t.domainStatus}`, children: [
                  (t.domainStatus === "checking" || z.has(n)) && /* @__PURE__ */ a.jsxs(a.Fragment, { children: [
                    /* @__PURE__ */ a.jsx("span", { className: "domain-spinner", children: "⏳" }),
                    /* @__PURE__ */ a.jsx("span", { children: "Checking..." })
                  ] }),
                  t.domainStatus === "available" && /* @__PURE__ */ a.jsxs(a.Fragment, { children: [
                    /* @__PURE__ */ a.jsx("span", { className: "domain-icon available", children: "✅" }),
                    /* @__PURE__ */ a.jsx("span", { children: "Available" })
                  ] }),
                  t.domainStatus === "taken" && /* @__PURE__ */ a.jsxs(a.Fragment, { children: [
                    /* @__PURE__ */ a.jsx("span", { className: "domain-icon taken", children: "❌" }),
                    /* @__PURE__ */ a.jsx("span", { children: "Taken" })
                  ] }),
                  t.domainStatus === "error" && /* @__PURE__ */ a.jsxs(a.Fragment, { children: [
                    /* @__PURE__ */ a.jsx("span", { className: "domain-icon error", children: "⚠️" }),
                    /* @__PURE__ */ a.jsx("span", { children: "Check manually" })
                  ] })
                ] })
              ] })
            ]
          },
          n
        );
      }) }),
      le && !h && /* @__PURE__ */ a.jsxs("div", { className: "refinement-section", children: [
        /* @__PURE__ */ a.jsx("div", { className: "refinement-info", children: /* @__PURE__ */ a.jsx("p", { children: "💡 I'll keep your liked names and replace the disliked ones with better suggestions!" }) }),
        /* @__PURE__ */ a.jsx(
          "button",
          {
            onClick: E,
            className: "refinement-button",
            disabled: h,
            children: te ? "🔄 Refining..." : "🎯 Replace Disliked Names"
          }
        )
      ] })
    ] })
  ] }) });
};
export {
  je as default
};

import Re, { useState as w } from "react";
var be = { exports: {} }, ie = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ke;
function Ae() {
  if (ke) return ie;
  ke = 1;
  var P = Symbol.for("react.transitional.element"), G = Symbol.for("react.fragment");
  function x(L, h, f) {
    var O = null;
    if (f !== void 0 && (O = "" + f), h.key !== void 0 && (O = "" + h.key), "key" in h) {
      f = {};
      for (var W in h)
        W !== "key" && (f[W] = h[W]);
    } else f = h;
    return h = f.ref, {
      $$typeof: P,
      type: L,
      key: O,
      ref: h !== void 0 ? h : null,
      props: f
    };
  }
  return ie.Fragment = G, ie.jsx = x, ie.jsxs = x, ie;
}
var oe = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Se;
function _e() {
  return Se || (Se = 1, process.env.NODE_ENV !== "production" && function() {
    function P(s) {
      if (s == null) return null;
      if (typeof s == "function")
        return s.$$typeof === ve ? null : s.displayName || s.name || null;
      if (typeof s == "string") return s;
      switch (s) {
        case C:
          return "Fragment";
        case le:
          return "Profiler";
        case J:
          return "StrictMode";
        case F:
          return "Suspense";
        case ge:
          return "SuspenseList";
        case K:
          return "Activity";
      }
      if (typeof s == "object")
        switch (typeof s.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), s.$$typeof) {
          case ee:
            return "Portal";
          case fe:
            return (s.displayName || "Context") + ".Provider";
          case pe:
            return (s._context.displayName || "Context") + ".Consumer";
          case ce:
            var c = s.render;
            return s = s.displayName, s || (s = c.displayName || c.name || "", s = s !== "" ? "ForwardRef(" + s + ")" : "ForwardRef"), s;
          case de:
            return c = s.displayName || null, c !== null ? c : P(s.type) || "Memo";
          case te:
            c = s._payload, s = s._init;
            try {
              return P(s(c));
            } catch {
            }
        }
      return null;
    }
    function G(s) {
      return "" + s;
    }
    function x(s) {
      try {
        G(s);
        var c = !1;
      } catch {
        c = !0;
      }
      if (c) {
        c = console;
        var m = c.error, g = typeof Symbol == "function" && Symbol.toStringTag && s[Symbol.toStringTag] || s.constructor.name || "Object";
        return m.call(
          c,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          g
        ), G(s);
      }
    }
    function L(s) {
      if (s === C) return "<>";
      if (typeof s == "object" && s !== null && s.$$typeof === te)
        return "<...>";
      try {
        var c = P(s);
        return c ? "<" + c + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function h() {
      var s = M.A;
      return s === null ? null : s.getOwner();
    }
    function f() {
      return Error("react-stack-top-frame");
    }
    function O(s) {
      if (Ne.call(s, "key")) {
        var c = Object.getOwnPropertyDescriptor(s, "key").get;
        if (c && c.isReactWarning) return !1;
      }
      return s.key !== void 0;
    }
    function W(s, c) {
      function m() {
        se || (se = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          c
        ));
      }
      m.isReactWarning = !0, Object.defineProperty(s, "key", {
        get: m,
        configurable: !0
      });
    }
    function E() {
      var s = P(this.type);
      return ne[s] || (ne[s] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), s = this.props.ref, s !== void 0 ? s : null;
    }
    function z(s, c, m, g, A, S, V, X) {
      return m = S.ref, s = {
        $$typeof: Z,
        type: s,
        key: c,
        props: S,
        _owner: A
      }, (m !== void 0 ? m : null) !== null ? Object.defineProperty(s, "ref", {
        enumerable: !1,
        get: E
      }) : Object.defineProperty(s, "ref", { enumerable: !1, value: null }), s._store = {}, Object.defineProperty(s._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(s, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(s, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: V
      }), Object.defineProperty(s, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: X
      }), Object.freeze && (Object.freeze(s.props), Object.freeze(s)), s;
    }
    function Q(s, c, m, g, A, S, V, X) {
      var v = c.children;
      if (v !== void 0)
        if (g)
          if (me(v)) {
            for (g = 0; g < v.length; g++)
              I(v[g]);
            Object.freeze && Object.freeze(v);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else I(v);
      if (Ne.call(c, "key")) {
        v = P(s);
        var $ = Object.keys(c).filter(function(he) {
          return he !== "key";
        });
        g = 0 < $.length ? "{key: someKey, " + $.join(": ..., ") + ": ...}" : "{key: someKey}", ue[v + g] || ($ = 0 < $.length ? "{" + $.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          g,
          v,
          $,
          v
        ), ue[v + g] = !0);
      }
      if (v = null, m !== void 0 && (x(m), v = "" + m), O(c) && (x(c.key), v = "" + c.key), "key" in c) {
        m = {};
        for (var H in c)
          H !== "key" && (m[H] = c[H]);
      } else m = c;
      return v && W(
        m,
        typeof s == "function" ? s.displayName || s.name || "Unknown" : s
      ), z(
        s,
        v,
        S,
        A,
        h(),
        m,
        V,
        X
      );
    }
    function I(s) {
      typeof s == "object" && s !== null && s.$$typeof === Z && s._store && (s._store.validated = 1);
    }
    var Y = Re, Z = Symbol.for("react.transitional.element"), ee = Symbol.for("react.portal"), C = Symbol.for("react.fragment"), J = Symbol.for("react.strict_mode"), le = Symbol.for("react.profiler"), pe = Symbol.for("react.consumer"), fe = Symbol.for("react.context"), ce = Symbol.for("react.forward_ref"), F = Symbol.for("react.suspense"), ge = Symbol.for("react.suspense_list"), de = Symbol.for("react.memo"), te = Symbol.for("react.lazy"), K = Symbol.for("react.activity"), ve = Symbol.for("react.client.reference"), M = Y.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, Ne = Object.prototype.hasOwnProperty, me = Array.isArray, T = console.createTask ? console.createTask : function() {
      return null;
    };
    Y = {
      "react-stack-bottom-frame": function(s) {
        return s();
      }
    };
    var se, ne = {}, ae = Y["react-stack-bottom-frame"].bind(
      Y,
      f
    )(), B = T(L(f)), ue = {};
    oe.Fragment = C, oe.jsx = function(s, c, m, g, A) {
      var S = 1e4 > M.recentlyCreatedOwnerStacks++;
      return Q(
        s,
        c,
        m,
        !1,
        g,
        A,
        S ? Error("react-stack-top-frame") : ae,
        S ? T(L(s)) : B
      );
    }, oe.jsxs = function(s, c, m, g, A) {
      var S = 1e4 > M.recentlyCreatedOwnerStacks++;
      return Q(
        s,
        c,
        m,
        !0,
        g,
        A,
        S ? Error("react-stack-top-frame") : ae,
        S ? T(L(s)) : B
      );
    };
  }()), oe;
}
process.env.NODE_ENV === "production" ? be.exports = Ae() : be.exports = _e();
var e = be.exports;
const Oe = ({
  className: P = "",
  style: G = {}
}) => {
  const [x, L] = w(""), [h, f] = w([]), [O, W] = w([]), [E, z] = w(!1), [Q, I] = w(null), [Y, Z] = w(null), [ee, C] = w([]), [J, le] = w({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    }
  }), [pe, fe] = w(!1), [ce, F] = w(!1), [ge, de] = w(!1), [te, K] = w(/* @__PURE__ */ new Set()), [ve, M] = w(/* @__PURE__ */ new Set()), [Ne, me] = w(0), [T, se] = w(!1), ne = 100, ae = () => {
    const t = document.createElement("canvas"), n = t.getContext("2d");
    n.textBaseline = "top", n.font = "14px Arial", n.fillText("Usage tracking", 2, 2);
    const a = t.toDataURL(), o = navigator.userAgent, i = navigator.language, l = Intl.DateTimeFormat().resolvedOptions().timeZone, r = a + o + i + l;
    let d = 0;
    for (let u = 0; u < r.length; u++) {
      const N = r.charCodeAt(u);
      d = (d << 5) - d + N, d = d & d;
    }
    return `podcast_usage_${Math.abs(d)}`;
  }, B = () => {
    const t = ae(), n = (/* @__PURE__ */ new Date()).toDateString(), a = `${t}_${n}`, o = localStorage.getItem(a), i = o ? parseInt(o, 10) : 0;
    return me(i), i >= ne ? (se(!0), !1) : !0;
  }, ue = (t = 1) => {
    const n = ae(), a = (/* @__PURE__ */ new Date()).toDateString(), o = `${n}_${a}`, i = localStorage.getItem(o), r = (i ? parseInt(i, 10) : 0) + t;
    localStorage.setItem(o, r.toString()), me(r), r >= ne && se(!0);
  };
  Re.useEffect(() => {
    B();
  }, []);
  const s = (t) => {
    let n = t.toLowerCase().replace(/[^a-z0-9\s]/g, "").replace(/\s+/g, " ").replace(/^the\s+/, "").trim();
    const a = ["the", "and", "for", "with", "from", "show", "podcast", "cast", "of", "in", "on", "at", "to", "a", "an"], o = n.split(" "), i = o.filter(
      (r) => r.length > 2 && !a.includes(r)
    );
    if (i.length === 1) {
      const r = i[0];
      return r.length >= 6 && r.length <= 15 ? r : r.length < 6 ? r + "pod" : c(r);
    }
    if (i.length >= 2) {
      const r = i[0], d = i[1], u = r + d;
      if (u.length >= 6 && u.length <= 15)
        return u;
      const N = c(r), U = c(d), b = N + U;
      if (b.length >= 6 && b.length <= 15)
        return b;
      if (b.length > 15)
        return N + "-" + U;
    }
    if (i.length > 0) {
      const r = c(i[0]), d = ["cast", "pod", "show", "talk"];
      for (const u of d) {
        const N = r + u;
        if (N.length >= 6 && N.length <= 15)
          return N;
      }
      if (r.length >= 6 && r.length <= 15)
        return r;
    }
    const l = o[0];
    return l && l.length >= 3 ? c(l) + "pod" : "podcast" + Math.random().toString(36).substring(2, 5);
  }, c = (t) => {
    if (t.length <= 8) return t;
    const n = {
      // Business & Professional
      business: "biz",
      entrepreneur: "entre",
      marketing: "market",
      finance: "fin",
      startup: "start",
      leadership: "lead",
      strategy: "strat",
      success: "win",
      growth: "grow",
      innovation: "innov",
      management: "manage",
      // Technology
      technology: "tech",
      development: "dev",
      digital: "digi",
      software: "soft",
      coding: "code",
      programming: "prog",
      // Content & Media
      stories: "story",
      journey: "path",
      adventure: "quest",
      creative: "create",
      entertainment: "fun",
      education: "learn",
      knowledge: "know",
      wisdom: "wise",
      // Lifestyle & Personal
      lifestyle: "life",
      wellness: "well",
      fitness: "fit",
      health: "heal",
      mindset: "mind",
      motivation: "motive",
      inspiration: "inspire",
      // Geographic & Cultural
      american: "usa",
      european: "euro",
      international: "global",
      community: "comm",
      culture: "cult",
      society: "social"
    };
    if (n[t])
      return n[t];
    const a = ["super", "mega", "ultra", "micro", "mini", "multi"];
    for (const i of a)
      if (t.startsWith(i) && t.length > i.length + 3) {
        const l = t.substring(i.length);
        if (l.length <= 8)
          return l;
      }
    const o = ["ing", "tion", "sion", "ness", "ment", "able", "ible"];
    for (const i of o)
      if (t.endsWith(i) && t.length > i.length + 4) {
        const l = t.substring(0, t.length - i.length);
        if (l.length >= 4 && l.length <= 8)
          return l;
      }
    return t.length > 8 ? t.substring(0, 8) : t;
  }, m = async (t) => {
    try {
      const n = await fetch(`https://dns.google/resolve?name=${t}&type=A`, {
        method: "GET",
        headers: {
          Accept: "application/json"
        }
      });
      if (!n.ok)
        return "error";
      const a = await n.json();
      return a.Answer && a.Answer.length > 0 ? "taken" : "available";
    } catch (n) {
      return console.warn("Domain check failed:", n), "error";
    }
  }, g = async (t) => {
    const n = [...t];
    for (let a = 0; a < n.length; a++) {
      const o = s(n[a].name);
      n[a].suggestedDomain = o, n[a].domainStatus = "checking", K((i) => /* @__PURE__ */ new Set([...i, a]));
    }
    f(n);
    for (let a = 0; a < n.length; a++) {
      const o = `${n[a].suggestedDomain}.com`;
      try {
        const i = await m(o);
        f((l) => {
          const r = [...l];
          return r[a] && (r[a].domainStatus = i), r;
        });
      } catch {
        f((l) => {
          const r = [...l];
          return r[a] && (r[a].domainStatus = "error"), r;
        });
      } finally {
        K((i) => {
          const l = new Set(i);
          return l.delete(a), l;
        });
      }
    }
  }, A = (t, n) => {
    if (t.length === 0) return null;
    const a = t.map((r) => r.name.split(" ").length), o = n.map((r) => r.name.split(" ").length), i = a.reduce((r, d) => r + d, 0) / a.length, l = o.length > 0 ? o.reduce((r, d) => r + d, 0) / o.length : 0;
    return i <= 2 && l > 2 ? "short" : i <= 4 && (l <= 2 || l > 4) ? "medium" : i > 4 && l <= 4 ? "long" : i <= 2 ? "short" : i <= 4 ? "medium" : "long";
  }, S = (t, n) => {
    if (t.length === 0) return null;
    const a = t.map((i) => i.description.toLowerCase()).join(" "), o = n.map((i) => i.description.toLowerCase()).join(" ");
    return (a.includes("professional") || a.includes("business")) && !o.includes("professional") && !o.includes("business") ? "professional" : (a.includes("creative") || a.includes("unique")) && !o.includes("creative") && !o.includes("unique") ? "creative" : (a.includes("fun") || a.includes("playful")) && !o.includes("fun") && !o.includes("playful") ? "playful" : "descriptive";
  }, V = (t) => {
    const n = [];
    t.forEach((o) => {
      const i = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], l = o.name.toLowerCase().split(/\s+/).filter(
        (r) => r.length > 2 && !i.includes(r)
      );
      n.push(...l);
    });
    const a = {};
    return n.forEach((o) => a[o] = (a[o] || 0) + 1), Object.entries(a).sort(([, o], [, i]) => i - o).slice(0, 5).map(([o]) => o);
  }, X = (t) => {
    const n = t.filter((o) => o.liked === !0), a = t.filter((o) => o.liked === !1);
    return {
      preferredLength: A(n, a),
      preferredStyle: S(n, a),
      likedKeywords: V(n),
      dislikedKeywords: V(a),
      preferredStructure: null
      // Simplified for now
    };
  }, v = (t, n) => {
    const a = [
      ...n.likedNames.map((r) => r.name.toLowerCase()),
      ...n.dislikedNames.map((r) => r.name.toLowerCase()),
      ...h.map((r) => r.name.toLowerCase())
    ], o = `Create 4 unique, high-converting podcast names for: ${t}`;
    let i = `

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;
    a.length > 0 && (i += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${a.map((r) => `- ${r}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);
    let l = "";
    if (n.patterns.preferredLength && (l += `
Focus on ${{
      short: "1-2 words (punchy and memorable)",
      medium: "2-3 words (balanced and brandable)",
      long: "3-4 words (descriptive but still catchy)"
    }[n.patterns.preferredLength]}. `), n.patterns.preferredStyle && (l += `Use ${{
      descriptive: "clear, straightforward names that explain the content",
      creative: "imaginative, metaphorical, or playful names",
      professional: "authoritative, business-focused names",
      playful: "fun, energetic, engaging names"
    }[n.patterns.preferredStyle] || n.patterns.preferredStyle}. `), n.patterns.likedKeywords.length > 0 && (l += `
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (l += `
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `), n.likedNames.length > 0) {
      const r = n.likedNames.slice(-2).map((d) => d.name).join('", "');
      l += `
Generate names with similar appeal to: "${r}" (but completely different concepts). `;
    }
    return `${o}${i}${l}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;
  }, $ = (t, n, a = 1) => {
    const o = [
      ...n.likedNames.map((d) => d.name.toLowerCase()),
      ...n.dislikedNames.map((d) => d.name.toLowerCase()),
      ...h.map((d) => d.name.toLowerCase())
    ], i = `Create ${a} unique, high-converting podcast name${a > 1 ? "s" : ""} for: ${t}`;
    let l = `

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;
    o.length > 0 && (l += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${o.map((d) => `- ${d}`).join(`
`)}
Do not create names similar to any of the above.`);
    let r = "";
    return n.patterns.likedKeywords.length > 0 && (r += `
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (r += `
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `), `${i}${l}${r}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works"}${a > 1 ? ', {"name": "Unique Name 2", "description": "Why this works"}' : ""}]}`;
  }, H = async (t = !1) => {
    if (!x.trim()) {
      I("Please describe what your podcast is about");
      return;
    }
    if (!B()) {
      I(null);
      return;
    }
    z(!0), I(null), f([]), t ? F(!0) : (C([]), fe(!1), F(!1), de(!1));
    try {
      const n = t ? v(x, J) : `Create 4 unique, high-converting podcast names for: ${x}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`, a = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: n
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!a.ok)
        throw new Error(`API request failed: ${a.status} ${a.statusText}`);
      const o = await a.json();
      if (!o.candidates || !o.candidates[0] || !o.candidates[0].content)
        throw new Error("Invalid response format from API");
      const l = o.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!l)
        throw new Error("No valid JSON found in API response");
      const r = JSON.parse(l[0]);
      if (!r.podcast_names || !Array.isArray(r.podcast_names))
        throw new Error("Invalid response structure");
      f(r.podcast_names), ue(4), g(r.podcast_names);
      const d = r.podcast_names.map((u, N) => ({
        name: u.name,
        description: u.description,
        liked: null,
        timestamp: Date.now(),
        index: N
      }));
      C(d);
    } catch (n) {
      console.error("Error generating podcast names:", n), I(n instanceof Error ? n.message : "An unexpected error occurred");
    } finally {
      z(!1), F(!1);
    }
  }, he = async (t, n) => {
    try {
      await navigator.clipboard.writeText(t), Z(n), setTimeout(() => Z(null), 2e3);
    } catch (a) {
      console.error("Failed to copy text:", a);
    }
  }, xe = async (t, n) => {
    const a = h[t];
    a && (n ? (W((o) => o.find((i) => i.name === a.name) ? o : [...o, a]), le((o) => {
      const i = { ...o };
      return i.dislikedNames = i.dislikedNames.filter((l) => l.name !== a.name), i.likedNames.find((l) => l.name === a.name) || i.likedNames.push({
        name: a.name,
        description: a.description,
        liked: !0,
        timestamp: Date.now(),
        index: t
      }), i.patterns = X([...i.likedNames, ...i.dislikedNames]), i;
    }), M((o) => /* @__PURE__ */ new Set([...o, t]))) : (M((o) => /* @__PURE__ */ new Set([...o, t])), le((o) => {
      const i = { ...o };
      return i.likedNames = i.likedNames.filter((l) => l.name !== a.name), i.dislikedNames.find((l) => l.name === a.name) || i.dislikedNames.push({
        name: a.name,
        description: a.description,
        liked: !1,
        timestamp: Date.now(),
        index: t
      }), i.patterns = X([...i.likedNames, ...i.dislikedNames]), i;
    })), x.trim() && Ee(t), ge || de(!0));
  }, Ee = async (t) => {
    var n, a, o, i, l, r;
    if (B())
      try {
        K((_) => /* @__PURE__ */ new Set([..._, t]));
        const d = v(x, J), u = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: d
              }]
            }]
          })
        });
        if (!u.ok)
          throw new Error("Failed to generate replacement suggestion");
        const U = (l = (i = (o = (a = (n = (await u.json()).candidates) == null ? void 0 : n[0]) == null ? void 0 : a.content) == null ? void 0 : o.parts) == null ? void 0 : i[0]) == null ? void 0 : l.text;
        if (!U)
          throw new Error("No content in API response");
        const b = U.match(/\{[\s\S]*\}/);
        if (!b)
          throw new Error("No valid JSON found in response");
        const q = (r = JSON.parse(b[0]).names) == null ? void 0 : r[0];
        if (q && (f((_) => {
          const R = [..._];
          return R[t] = {
            name: q.name,
            description: q.description,
            suggestedDomain: q.suggestedDomain,
            domainStatus: "checking"
          }, R;
        }), M((_) => {
          const R = new Set(_);
          return R.delete(t), R;
        }), C((_) => _.filter((R) => R.index !== t)), q.suggestedDomain)) {
          const _ = await m(q.suggestedDomain);
          f((R) => {
            const j = [...R];
            return j[t] && (j[t].domainStatus = _), j;
          });
        }
      } catch (d) {
        console.error("Error generating replacement suggestion:", d), M((u) => {
          const N = new Set(u);
          return N.delete(t), N;
        }), C((u) => u.filter((N) => N.index !== t));
      } finally {
        K((d) => {
          const u = new Set(d);
          return u.delete(t), u;
        });
      }
  }, Te = () => {
    x.trim() && Ce(x);
  }, Ce = async (t) => {
    var n, a, o, i, l, r, d, u, N, U;
    z(!0), I(""), F(!0);
    try {
      const b = h.filter((j, k) => {
        const p = ee.find((y) => y.index === k);
        return (p == null ? void 0 : p.liked) === !0;
      }), ye = h.filter((j, k) => {
        const p = ee.find((y) => y.index === k);
        return (p == null ? void 0 : p.liked) === !1;
      }).length, q = Math.max(1, ye), R = Math.min(5, b.length + q) - b.length;
      if (R <= 0) {
        const j = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: $(t, J, 1)
              }]
            }]
          })
        });
        if (!j.ok)
          throw new Error(`API request failed: ${j.status}`);
        const p = (l = (i = (o = (a = (n = (await j.json()).candidates) == null ? void 0 : n[0]) == null ? void 0 : a.content) == null ? void 0 : o.parts) == null ? void 0 : i[0]) == null ? void 0 : l.text;
        if (!p)
          throw new Error("No content received from API");
        const y = p.match(/\{[\s\S]*\}/);
        if (!y)
          throw new Error("No valid JSON found in response");
        const D = JSON.parse(y[0]);
        if (!D.podcast_names || !Array.isArray(D.podcast_names))
          throw new Error("Invalid response format");
        const re = [...b, ...D.podcast_names].slice(0, 5);
        f(re), g(re);
      } else {
        const j = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: $(t, J, R)
              }]
            }]
          })
        });
        if (!j.ok)
          throw new Error(`API request failed: ${j.status}`);
        const p = (U = (N = (u = (d = (r = (await j.json()).candidates) == null ? void 0 : r[0]) == null ? void 0 : d.content) == null ? void 0 : u.parts) == null ? void 0 : N[0]) == null ? void 0 : U.text;
        if (!p)
          throw new Error("No content received from API");
        const y = p.match(/\{[\s\S]*\}/);
        if (!y)
          throw new Error("No valid JSON found in response");
        const D = JSON.parse(y[0]);
        if (!D.podcast_names || !Array.isArray(D.podcast_names))
          throw new Error("Invalid response format");
        const re = [...b, ...D.podcast_names];
        f(re), g(re);
      }
      C((j) => j.filter((k) => {
        const p = h[k.index];
        return b.some((y) => y.name === (p == null ? void 0 : p.name));
      })), C((j) => j.map((k) => {
        const p = h[k.index], y = b.findIndex((D) => D.name === (p == null ? void 0 : p.name));
        return y >= 0 ? { ...k, index: y } : k;
      }).filter((k) => k.index >= 0));
    } catch (b) {
      console.error("Error generating refined names:", b), I(b instanceof Error ? b.message : "Failed to generate refined names. Please try again.");
    } finally {
      z(!1), F(!1);
    }
  }, je = (t) => {
    t.preventDefault(), H();
  }, we = (t) => {
    t.key === "Enter" && !t.shiftKey && (t.preventDefault(), H());
  };
  return /* @__PURE__ */ e.jsx("div", { className: `podcast-name-generator ${P}`, style: G, children: /* @__PURE__ */ e.jsxs("div", { className: "generator-container", children: [
    /* @__PURE__ */ e.jsxs("div", { className: "header-section", children: [
      /* @__PURE__ */ e.jsx("h1", { className: "main-title", children: "Free Podcast Name Generator" }),
      /* @__PURE__ */ e.jsx("h2", { className: "main-subtitle", children: "Create the Perfect Name for Your Podcast in Seconds" })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: "benefits-section", children: [
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "100% Free Forever" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "No Sign-up Required" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
        /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
        /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "Instant Results" })
      ] })
    ] }),
    T && /* @__PURE__ */ e.jsx("div", { className: "limit-reached-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "limit-content", children: [
      /* @__PURE__ */ e.jsx("span", { className: "limit-icon", children: "⚠️" }),
      /* @__PURE__ */ e.jsx("div", { className: "limit-text", children: /* @__PURE__ */ e.jsx("p", { children: "You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below." }) })
    ] }) }),
    h.length === 0 && /* @__PURE__ */ e.jsx("div", { className: "initial-input-section", children: /* @__PURE__ */ e.jsx("form", { onSubmit: je, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
      /* @__PURE__ */ e.jsx(
        "textarea",
        {
          value: x,
          onChange: (t) => L(t.target.value),
          onKeyPress: we,
          placeholder: "Describe what your podcast is about",
          className: "input-field",
          rows: 3,
          disabled: E
        }
      ),
      /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
        /* @__PURE__ */ e.jsx(
          "button",
          {
            type: "submit",
            disabled: E || !x.trim() || T,
            className: `generate-button ${T ? "disabled" : ""}`,
            children: E ? "Generating..." : T ? "Daily Limit Reached" : "Generate Names"
          }
        ),
        /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
            /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
          ] }),
          /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
            /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ e.jsx("svg", { className: "star star-partial", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
            ] }),
            /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
          ] })
        ] })
      ] })
    ] }) }) }),
    Q && /* @__PURE__ */ e.jsxs("div", { className: "error-message", children: [
      /* @__PURE__ */ e.jsx("span", { className: "error-icon", children: "⚠️" }),
      Q
    ] }),
    E && /* @__PURE__ */ e.jsxs("div", { className: "loading-container", children: [
      /* @__PURE__ */ e.jsx("div", { className: "loading-spinner" }),
      /* @__PURE__ */ e.jsx("p", { children: ce ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
    ] }),
    h.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "results-container", children: [
      O.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "favorites-section", children: [
        /* @__PURE__ */ e.jsxs("div", { className: "favorites-header", children: [
          /* @__PURE__ */ e.jsxs("h3", { children: [
            "🏆 Your Winning Podcast Names (",
            O.length,
            ")"
          ] }),
          /* @__PURE__ */ e.jsx("p", { className: "favorites-subtitle", children: "Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!" })
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "favorites-grid", children: O.map((t, n) => /* @__PURE__ */ e.jsxs("div", { className: "favorite-card", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "favorite-content", children: [
            /* @__PURE__ */ e.jsx("h4", { className: "favorite-name", children: t.name }),
            /* @__PURE__ */ e.jsx("p", { className: "favorite-description", children: t.description }),
            t.suggestedDomain && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
              /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
              /* @__PURE__ */ e.jsx("span", { className: "domain-name", children: t.suggestedDomain }),
              /* @__PURE__ */ e.jsx("span", { className: `domain-status ${t.domainStatus}`, children: t.domainStatus === "available" ? "✅ Available" : t.domainStatus === "taken" ? "❌ Taken" : t.domainStatus === "error" ? "⚠️ Check manually" : "🔍 Checking..." })
            ] })
          ] }),
          /* @__PURE__ */ e.jsx("div", { className: "favorite-actions", children: /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: () => he(t.name, -1),
              className: "copy-button small",
              title: "Copy to clipboard",
              children: "📋 Copy"
            }
          ) })
        ] }, `fav-${n}`)) })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "input-section-simple", children: [
        /* @__PURE__ */ e.jsx("div", { className: "input-help-message-simple", children: /* @__PURE__ */ e.jsxs("p", { className: "input-sub-description", children: [
          "💡 Want different suggestions? Update your description below - ",
          /* @__PURE__ */ e.jsx("strong", { children: "your favorites will stay safe!" })
        ] }) }),
        /* @__PURE__ */ e.jsx("form", { onSubmit: je, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
          /* @__PURE__ */ e.jsx(
            "textarea",
            {
              value: x,
              onChange: (t) => L(t.target.value),
              onKeyPress: we,
              placeholder: "Describe what your podcast is about",
              className: "input-field",
              rows: 3,
              disabled: E
            }
          ),
          /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
            /* @__PURE__ */ e.jsx(
              "button",
              {
                type: "submit",
                disabled: E || !x.trim() || T,
                className: `generate-button ${T ? "disabled" : ""}`,
                children: E ? "Generating..." : T ? "Daily Limit Reached" : "Generate Names"
              }
            ),
            /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
              /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
                /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
              ] }),
              /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
                /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                  /* @__PURE__ */ e.jsx("svg", { className: "star star-partial", viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
                ] }),
                /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
              ] })
            ] })
          ] })
        ] }) })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "suggestions-section", children: [
        /* @__PURE__ */ e.jsx("div", { className: "suggestions-header", children: /* @__PURE__ */ e.jsx("h3", { children: "🎯 Current Suggestions" }) }),
        /* @__PURE__ */ e.jsx("div", { className: "onboarding-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "onboarding-content", children: [
          /* @__PURE__ */ e.jsx("span", { className: "onboarding-icon", children: "💡" }),
          /* @__PURE__ */ e.jsxs("div", { className: "onboarding-text", children: [
            /* @__PURE__ */ e.jsx("strong", { children: "Smart AI Learning:" }),
            " The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."
          ] })
        ] }) }),
        /* @__PURE__ */ e.jsx("div", { className: "results-grid", children: h.map((t, n) => {
          const a = ee.find((d) => d.index === n), o = (a == null ? void 0 : a.liked) === !0, i = (a == null ? void 0 : a.liked) === !1, l = ve.has(n), r = te.has(n);
          return /* @__PURE__ */ e.jsxs(
            "div",
            {
              className: `result-card ${o ? "liked" : ""} ${i ? "disliked" : ""} ${l ? "pending" : ""}`,
              style: {
                opacity: l ? 0.6 : 1,
                pointerEvents: l || r ? "none" : "auto"
              },
              children: [
                /* @__PURE__ */ e.jsxs("div", { className: "result-header", children: [
                  /* @__PURE__ */ e.jsx("h4", { className: "result-name", children: r ? o ? "Generating new suggestion..." : "Generating better suggestion..." : t.name }),
                  /* @__PURE__ */ e.jsxs("div", { className: "result-actions", children: [
                    /* @__PURE__ */ e.jsxs("div", { className: "feedback-buttons", children: [
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => xe(n, !0),
                          className: `feedback-button like-button ${o ? "active" : ""}`,
                          title: "I like this name",
                          disabled: l || r,
                          children: "👍"
                        }
                      ),
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => xe(n, !1),
                          className: `feedback-button dislike-button ${i ? "active" : ""} ${r ? "loading" : ""}`,
                          title: r ? "Generating replacement..." : "I don't like this name",
                          disabled: l || r,
                          children: r ? "🔄" : "👎"
                        }
                      )
                    ] }),
                    /* @__PURE__ */ e.jsx(
                      "button",
                      {
                        onClick: () => he(t.name, n),
                        className: "copy-button",
                        title: "Copy podcast name",
                        disabled: l || r,
                        children: Y === n ? "✓ Copied!" : "📋 Copy"
                      }
                    )
                  ] })
                ] }),
                /* @__PURE__ */ e.jsx("p", { className: "result-description", children: r ? o ? "Added to favorites! Generating a new suggestion..." : "Creating a better suggestion based on your preferences..." : t.description }),
                t.suggestedDomain && !r && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
                  /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
                  /* @__PURE__ */ e.jsxs("code", { className: "domain-text", children: [
                    t.suggestedDomain,
                    ".com"
                  ] }),
                  /* @__PURE__ */ e.jsxs("span", { className: `domain-status ${t.domainStatus}`, children: [
                    (t.domainStatus === "checking" || te.has(n)) && "⏳ Checking...",
                    t.domainStatus === "available" && "✅ Available",
                    t.domainStatus === "taken" && "❌ Taken",
                    t.domainStatus === "error" && "⚠️ Check manually"
                  ] })
                ] })
              ]
            },
            n
          );
        }) })
      ] }),
      pe && !E && /* @__PURE__ */ e.jsxs("div", { className: "refinement-section", children: [
        /* @__PURE__ */ e.jsx("div", { className: "refinement-info", children: /* @__PURE__ */ e.jsx("p", { children: "💡 I'll keep your liked names and replace the disliked ones with better suggestions!" }) }),
        /* @__PURE__ */ e.jsx(
          "button",
          {
            onClick: Te,
            className: "refinement-button",
            disabled: E,
            children: ce ? "🔄 Refining..." : "🎯 Replace Disliked Names"
          }
        )
      ] })
    ] })
  ] }) });
};
export {
  Oe as default
};

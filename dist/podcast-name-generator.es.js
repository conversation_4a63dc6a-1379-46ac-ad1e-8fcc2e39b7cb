import re, { useState as y } from "react";
var M = { exports: {} }, N = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var X;
function te() {
  if (X) return N;
  X = 1;
  var m = Symbol.for("react.transitional.element"), g = Symbol.for("react.fragment");
  function v(u, c, i) {
    var p = null;
    if (i !== void 0 && (p = "" + i), c.key !== void 0 && (p = "" + c.key), "key" in c) {
      i = {};
      for (var d in c)
        d !== "key" && (i[d] = c[d]);
    } else i = c;
    return c = i.ref, {
      $$typeof: m,
      type: u,
      key: p,
      ref: c !== void 0 ? c : null,
      props: i
    };
  }
  return N.Fragment = g, N.jsx = v, N.jsxs = v, N;
}
var w = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var B;
function ae() {
  return B || (B = 1, process.env.NODE_ENV !== "production" && function() {
    function m(e) {
      if (e == null) return null;
      if (typeof e == "function")
        return e.$$typeof === Z ? null : e.displayName || e.name || null;
      if (typeof e == "string") return e;
      switch (e) {
        case _:
          return "Fragment";
        case a:
          return "Profiler";
        case C:
          return "StrictMode";
        case O:
          return "Suspense";
        case j:
          return "SuspenseList";
        case K:
          return "Activity";
      }
      if (typeof e == "object")
        switch (typeof e.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), e.$$typeof) {
          case A:
            return "Portal";
          case h:
            return (e.displayName || "Context") + ".Provider";
          case l:
            return (e._context.displayName || "Context") + ".Consumer";
          case G:
            var r = e.render;
            return e = e.displayName, e || (e = r.displayName || r.name || "", e = e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef"), e;
          case H:
            return r = e.displayName || null, r !== null ? r : m(e.type) || "Memo";
          case L:
            r = e._payload, e = e._init;
            try {
              return m(e(r));
            } catch {
            }
        }
      return null;
    }
    function g(e) {
      return "" + e;
    }
    function v(e) {
      try {
        g(e);
        var r = !1;
      } catch {
        r = !0;
      }
      if (r) {
        r = console;
        var t = r.error, s = typeof Symbol == "function" && Symbol.toStringTag && e[Symbol.toStringTag] || e.constructor.name || "Object";
        return t.call(
          r,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          s
        ), g(e);
      }
    }
    function u(e) {
      if (e === _) return "<>";
      if (typeof e == "object" && e !== null && e.$$typeof === L)
        return "<...>";
      try {
        var r = m(e);
        return r ? "<" + r + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function c() {
      var e = I.A;
      return e === null ? null : e.getOwner();
    }
    function i() {
      return Error("react-stack-top-frame");
    }
    function p(e) {
      if (J.call(e, "key")) {
        var r = Object.getOwnPropertyDescriptor(e, "key").get;
        if (r && r.isReactWarning) return !1;
      }
      return e.key !== void 0;
    }
    function d(e, r) {
      function t() {
        U || (U = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          r
        ));
      }
      t.isReactWarning = !0, Object.defineProperty(e, "key", {
        get: t,
        configurable: !0
      });
    }
    function k() {
      var e = m(this.type);
      return W[e] || (W[e] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), e = this.props.ref, e !== void 0 ? e : null;
    }
    function P(e, r, t, s, b, f, $, D) {
      return t = f.ref, e = {
        $$typeof: R,
        type: e,
        key: r,
        props: f,
        _owner: b
      }, (t !== void 0 ? t : null) !== null ? Object.defineProperty(e, "ref", {
        enumerable: !1,
        get: k
      }) : Object.defineProperty(e, "ref", { enumerable: !1, value: null }), e._store = {}, Object.defineProperty(e._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(e, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(e, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: $
      }), Object.defineProperty(e, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: D
      }), Object.freeze && (Object.freeze(e.props), Object.freeze(e)), e;
    }
    function T(e, r, t, s, b, f, $, D) {
      var o = r.children;
      if (o !== void 0)
        if (s)
          if (Q(o)) {
            for (s = 0; s < o.length; s++)
              S(o[s]);
            Object.freeze && Object.freeze(o);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else S(o);
      if (J.call(r, "key")) {
        o = m(e);
        var x = Object.keys(r).filter(function(ee) {
          return ee !== "key";
        });
        s = 0 < x.length ? "{key: someKey, " + x.join(": ..., ") + ": ...}" : "{key: someKey}", V[o + s] || (x = 0 < x.length ? "{" + x.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          s,
          o,
          x,
          o
        ), V[o + s] = !0);
      }
      if (o = null, t !== void 0 && (v(t), o = "" + t), p(r) && (v(r.key), o = "" + r.key), "key" in r) {
        t = {};
        for (var F in r)
          F !== "key" && (t[F] = r[F]);
      } else t = r;
      return o && d(
        t,
        typeof e == "function" ? e.displayName || e.name || "Unknown" : e
      ), P(
        e,
        o,
        f,
        b,
        c(),
        t,
        $,
        D
      );
    }
    function S(e) {
      typeof e == "object" && e !== null && e.$$typeof === R && e._store && (e._store.validated = 1);
    }
    var E = re, R = Symbol.for("react.transitional.element"), A = Symbol.for("react.portal"), _ = Symbol.for("react.fragment"), C = Symbol.for("react.strict_mode"), a = Symbol.for("react.profiler"), l = Symbol.for("react.consumer"), h = Symbol.for("react.context"), G = Symbol.for("react.forward_ref"), O = Symbol.for("react.suspense"), j = Symbol.for("react.suspense_list"), H = Symbol.for("react.memo"), L = Symbol.for("react.lazy"), K = Symbol.for("react.activity"), Z = Symbol.for("react.client.reference"), I = E.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, J = Object.prototype.hasOwnProperty, Q = Array.isArray, Y = console.createTask ? console.createTask : function() {
      return null;
    };
    E = {
      "react-stack-bottom-frame": function(e) {
        return e();
      }
    };
    var U, W = {}, z = E["react-stack-bottom-frame"].bind(
      E,
      i
    )(), q = Y(u(i)), V = {};
    w.Fragment = _, w.jsx = function(e, r, t, s, b) {
      var f = 1e4 > I.recentlyCreatedOwnerStacks++;
      return T(
        e,
        r,
        t,
        !1,
        s,
        b,
        f ? Error("react-stack-top-frame") : z,
        f ? Y(u(e)) : q
      );
    }, w.jsxs = function(e, r, t, s, b) {
      var f = 1e4 > I.recentlyCreatedOwnerStacks++;
      return T(
        e,
        r,
        t,
        !0,
        s,
        b,
        f ? Error("react-stack-top-frame") : z,
        f ? Y(u(e)) : q
      );
    };
  }()), w;
}
process.env.NODE_ENV === "production" ? M.exports = te() : M.exports = ae();
var n = M.exports;
const se = ({
  apiKey: m = "AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",
  className: g = "",
  style: v = {}
}) => {
  const [u, c] = y(""), [i, p] = y([]), [d, k] = y(!1), [P, T] = y(null), [S, E] = y(null), R = async () => {
    if (!u.trim()) {
      T("Please describe what your podcast is about");
      return;
    }
    k(!0), T(null), p([]);
    try {
      const a = `Create 5 high-converting and catchy podcast names based on the following user input: ${u}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`, l = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${m}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: a
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!l.ok)
        throw new Error(`API request failed: ${l.status} ${l.statusText}`);
      const h = await l.json();
      if (!h.candidates || !h.candidates[0] || !h.candidates[0].content)
        throw new Error("Invalid response format from API");
      const O = h.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!O)
        throw new Error("No valid JSON found in API response");
      const j = JSON.parse(O[0]);
      if (!j.podcast_names || !Array.isArray(j.podcast_names))
        throw new Error("Invalid response structure");
      p(j.podcast_names);
    } catch (a) {
      console.error("Error generating podcast names:", a), T(a instanceof Error ? a.message : "An unexpected error occurred");
    } finally {
      k(!1);
    }
  }, A = async (a, l) => {
    try {
      await navigator.clipboard.writeText(a), E(l), setTimeout(() => E(null), 2e3);
    } catch (h) {
      console.error("Failed to copy text:", h);
    }
  }, _ = (a) => {
    a.preventDefault(), R();
  }, C = (a) => {
    a.key === "Enter" && !a.shiftKey && (a.preventDefault(), R());
  };
  return /* @__PURE__ */ n.jsx("div", { className: `podcast-name-generator ${g}`, style: v, children: /* @__PURE__ */ n.jsxs("div", { className: "generator-container", children: [
    /* @__PURE__ */ n.jsx("h2", { className: "generator-title", children: "Podcast Name Generator" }),
    /* @__PURE__ */ n.jsx("p", { className: "generator-subtitle", children: "Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI" }),
    /* @__PURE__ */ n.jsx("form", { onSubmit: _, className: "input-form", children: /* @__PURE__ */ n.jsxs("div", { className: "input-container", children: [
      /* @__PURE__ */ n.jsx(
        "textarea",
        {
          value: u,
          onChange: (a) => c(a.target.value),
          onKeyPress: C,
          placeholder: "Describe what your podcast is about",
          className: "input-field",
          rows: 3,
          disabled: d
        }
      ),
      /* @__PURE__ */ n.jsx(
        "button",
        {
          type: "submit",
          disabled: d || !u.trim(),
          className: "generate-button",
          children: d ? "Generating..." : "Generate Names"
        }
      )
    ] }) }),
    P && /* @__PURE__ */ n.jsxs("div", { className: "error-message", children: [
      /* @__PURE__ */ n.jsx("span", { className: "error-icon", children: "⚠️" }),
      P
    ] }),
    d && /* @__PURE__ */ n.jsxs("div", { className: "loading-container", children: [
      /* @__PURE__ */ n.jsx("div", { className: "loading-spinner" }),
      /* @__PURE__ */ n.jsx("p", { children: "Generating creative podcast names..." })
    ] }),
    i.length > 0 && /* @__PURE__ */ n.jsxs("div", { className: "results-container", children: [
      /* @__PURE__ */ n.jsx("h3", { className: "results-title", children: "Your Podcast Name Suggestions" }),
      /* @__PURE__ */ n.jsx("div", { className: "results-grid", children: i.map((a, l) => /* @__PURE__ */ n.jsxs("div", { className: "result-card", children: [
        /* @__PURE__ */ n.jsxs("div", { className: "result-header", children: [
          /* @__PURE__ */ n.jsx("h4", { className: "result-name", children: a.name }),
          /* @__PURE__ */ n.jsx(
            "button",
            {
              onClick: () => A(a.name, l),
              className: "copy-button",
              title: "Copy podcast name",
              children: S === l ? "✓ Copied!" : "📋 Copy"
            }
          )
        ] }),
        /* @__PURE__ */ n.jsx("p", { className: "result-description", children: a.description })
      ] }, l)) })
    ] })
  ] }) });
};
export {
  se as default
};

(function(u,P){typeof exports=="object"&&typeof module<"u"?module.exports=P(require("react")):typeof define=="function"&&define.amd?define(["react"],P):(u=typeof globalThis<"u"?globalThis:u||self,u.PodcastNameGenerator=P(u.React))})(this,function(u){"use strict";var P={exports:{}},J={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fe;function be(){if(fe)return J;fe=1;var j=Symbol.for("react.transitional.element"),Y=Symbol.for("react.fragment");function A(N,v,p){var w=null;if(p!==void 0&&(w=""+p),v.key!==void 0&&(w=""+v.key),"key"in v){p={};for(var g in v)g!=="key"&&(p[g]=v[g])}else p=v;return v=p.ref,{$$typeof:j,type:N,key:w,ref:v!==void 0?v:null,props:p}}return J.Fragment=Y,J.jsx=A,J.jsxs=A,J}var z={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pe;function ke(){return pe||(pe=1,process.env.NODE_ENV!=="production"&&function(){function j(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ae?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case y:return"Fragment";case de:return"Profiler";case U:return"StrictMode";case W:return"Suspense";case se:return"SuspenseList";case re:return"Activity"}if(typeof e=="object")switch(typeof e.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),e.$$typeof){case I:return"Portal";case ne:return(e.displayName||"Context")+".Provider";case te:return(e._context.displayName||"Context")+".Consumer";case D:var o=e.render;return e=e.displayName,e||(e=o.displayName||o.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ue:return o=e.displayName||null,o!==null?o:j(e.type)||"Memo";case $:o=e._payload,e=e._init;try{return j(e(o))}catch{}}return null}function Y(e){return""+e}function A(e){try{Y(e);var o=!1}catch{o=!0}if(o){o=console;var l=o.error,m=typeof Symbol=="function"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object";return l.call(o,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",m),Y(e)}}function N(e){if(e===y)return"<>";if(typeof e=="object"&&e!==null&&e.$$typeof===$)return"<...>";try{var o=j(e);return o?"<"+o+">":"<...>"}catch{return"<...>"}}function v(){var e=K.A;return e===null?null:e.getOwner()}function p(){return Error("react-stack-top-frame")}function w(e){if(B.call(e,"key")){var o=Object.getOwnPropertyDescriptor(e,"key").get;if(o&&o.isReactWarning)return!1}return e.key!==void 0}function g(e,o){function l(){G||(G=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",o))}l.isReactWarning=!0,Object.defineProperty(e,"key",{get:l,configurable:!0})}function M(){var e=j(this.type);return oe[e]||(oe[e]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),e=this.props.ref,e!==void 0?e:null}function q(e,o,l,m,E,x,X,Z){return l=x.ref,e={$$typeof:C,type:e,key:o,props:x,_owner:E},(l!==void 0?l:null)!==null?Object.defineProperty(e,"ref",{enumerable:!1,get:M}):Object.defineProperty(e,"ref",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:X}),Object.defineProperty(e,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:Z}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function T(e,o,l,m,E,x,X,Z){var t=o.children;if(t!==void 0)if(m)if(me(t)){for(m=0;m<t.length;m++)ee(t[m]);Object.freeze&&Object.freeze(t)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else ee(t);if(B.call(o,"key")){t=j(e);var n=Object.keys(o).filter(function(a){return a!=="key"});m=0<n.length?"{key: someKey, "+n.join(": ..., ")+": ...}":"{key: someKey}",ce[t+m]||(n=0<n.length?"{"+n.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,m,t,n,t),ce[t+m]=!0)}if(t=null,l!==void 0&&(A(l),t=""+l),w(o)&&(A(o.key),t=""+o.key),"key"in o){l={};for(var s in o)s!=="key"&&(l[s]=o[s])}else l=o;return t&&g(l,typeof e=="function"?e.displayName||e.name||"Unknown":e),q(e,t,x,E,v(),l,X,Z)}function ee(e){typeof e=="object"&&e!==null&&e.$$typeof===C&&e._store&&(e._store.validated=1)}var O=u,C=Symbol.for("react.transitional.element"),I=Symbol.for("react.portal"),y=Symbol.for("react.fragment"),U=Symbol.for("react.strict_mode"),de=Symbol.for("react.profiler"),te=Symbol.for("react.consumer"),ne=Symbol.for("react.context"),D=Symbol.for("react.forward_ref"),W=Symbol.for("react.suspense"),se=Symbol.for("react.suspense_list"),ue=Symbol.for("react.memo"),$=Symbol.for("react.lazy"),re=Symbol.for("react.activity"),ae=Symbol.for("react.client.reference"),K=O.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,B=Object.prototype.hasOwnProperty,me=Array.isArray,V=console.createTask?console.createTask:function(){return null};O={"react-stack-bottom-frame":function(e){return e()}};var G,oe={},ie=O["react-stack-bottom-frame"].bind(O,p)(),H=V(N(p)),ce={};z.Fragment=y,z.jsx=function(e,o,l,m,E){var x=1e4>K.recentlyCreatedOwnerStacks++;return T(e,o,l,!1,m,E,x?Error("react-stack-top-frame"):ie,x?V(N(e)):H)},z.jsxs=function(e,o,l,m,E){var x=1e4>K.recentlyCreatedOwnerStacks++;return T(e,o,l,!0,m,E,x?Error("react-stack-top-frame"):ie,x?V(N(e)):H)}}()),z}process.env.NODE_ENV==="production"?P.exports=be():P.exports=ke();var r=P.exports;return({apiKey:j="AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",className:Y="",style:A={}})=>{const[N,v]=u.useState(""),[p,w]=u.useState([]),[g,M]=u.useState(!1),[q,T]=u.useState(null),[ee,O]=u.useState(null),[C,I]=u.useState([]),[y,U]=u.useState({likedNames:[],dislikedNames:[],patterns:{preferredLength:null,preferredStyle:null,likedKeywords:[],dislikedKeywords:[],preferredStructure:null},generationRound:0}),[de,te]=u.useState(!1),[ne,D]=u.useState(!1),[W,se]=u.useState([]),[ue,$]=u.useState(!0),[re,ae]=u.useState(!1),[K,B]=u.useState(new Set),me=t=>t.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"").replace(/^the/,"").substring(0,50),V=async t=>{try{const n=await fetch(`https://dns.google/resolve?name=${t}&type=A`,{method:"GET",headers:{Accept:"application/json"}});if(!n.ok)return"error";const s=await n.json();return s.Answer&&s.Answer.length>0?"taken":"available"}catch(n){return console.warn("Domain check failed:",n),"error"}},G=async t=>{const n=[...t];for(let s=0;s<n.length;s++){const a=me(n[s].name);n[s].suggestedDomain=a,n[s].domainStatus="checking",B(i=>new Set([...i,s]))}w(n);for(let s=0;s<n.length;s++){const a=`${n[s].suggestedDomain}.com`;try{const i=await V(a);w(d=>{const c=[...d];return c[s]&&(c[s].domainStatus=i),c})}catch{w(d=>{const c=[...d];return c[s]&&(c[s].domainStatus="error"),c})}finally{B(i=>{const d=new Set(i);return d.delete(s),d})}}},oe=(t,n)=>{if(t.length===0)return null;const s=t.map(c=>c.name.split(" ").length),a=n.map(c=>c.name.split(" ").length),i=s.reduce((c,_)=>c+_,0)/s.length,d=a.length>0?a.reduce((c,_)=>c+_,0)/a.length:0;return i<=2&&d>2?"short":i<=4&&(d<=2||d>4)?"medium":i>4&&d<=4?"long":i<=2?"short":i<=4?"medium":"long"},ie=(t,n)=>{if(t.length===0)return null;const s=t.map(i=>i.description.toLowerCase()).join(" "),a=n.map(i=>i.description.toLowerCase()).join(" ");return(s.includes("professional")||s.includes("business"))&&!a.includes("professional")&&!a.includes("business")?"professional":(s.includes("creative")||s.includes("unique"))&&!a.includes("creative")&&!a.includes("unique")?"creative":(s.includes("fun")||s.includes("playful"))&&!a.includes("fun")&&!a.includes("playful")?"playful":"descriptive"},H=t=>{const n=[];t.forEach(a=>{const i=["the","a","an","and","or","but","in","on","at","to","for","of","with","by"],d=a.name.toLowerCase().split(/\s+/).filter(c=>c.length>2&&!i.includes(c));n.push(...d)});const s={};return n.forEach(a=>s[a]=(s[a]||0)+1),Object.entries(s).sort(([,a],[,i])=>i-a).slice(0,5).map(([a])=>a)},ce=t=>{const n=t.filter(a=>a.liked===!0),s=t.filter(a=>a.liked===!1);return{preferredLength:oe(n,s),preferredStyle:ie(n,s),likedKeywords:H(n),dislikedKeywords:H(s),preferredStructure:null}},e=(t,n)=>{const s=`Create 5 high-converting and catchy podcast names based on the following user input: ${t}.`;let a="";if(n.patterns.preferredLength){const i={short:"1-2 words",medium:"3-4 words",long:"5+ words"};a+=`Focus on ${n.patterns.preferredLength} names (${i[n.patterns.preferredLength]}). `}if(n.patterns.preferredStyle&&(a+=`Use a ${n.patterns.preferredStyle} style. `),n.patterns.likedKeywords.length>0&&(a+=`Incorporate concepts similar to: ${n.patterns.likedKeywords.join(", ")}. `),n.patterns.dislikedKeywords.length>0&&(a+=`Avoid concepts like: ${n.patterns.dislikedKeywords.join(", ")}. `),n.likedNames.length>0){const i=n.likedNames.slice(-2).map(d=>d.name).join('", "');a+=`Generate names with similar appeal to: "${i}". `}return`${s} ${a} Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`},o=async(t=!1)=>{if(!N.trim()){T("Please describe what your podcast is about");return}M(!0),T(null),w([]),t?D(!0):(I([]),te(!1),D(!1),$(!0),ae(!1));try{const n=t?e(N,y):`Create 5 high-converting and catchy podcast names based on the following user input: ${N}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`,s=await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${j}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:n}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!s.ok)throw new Error(`API request failed: ${s.status} ${s.statusText}`);const a=await s.json();if(!a.candidates||!a.candidates[0]||!a.candidates[0].content)throw new Error("Invalid response format from API");const d=a.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!d)throw new Error("No valid JSON found in API response");const c=JSON.parse(d[0]);if(!c.podcast_names||!Array.isArray(c.podcast_names))throw new Error("Invalid response structure");w(c.podcast_names),G(c.podcast_names),se(R=>[...R,c.podcast_names]),U(R=>({...R,generationRound:R.generationRound+1}));const _=c.podcast_names.map((R,le)=>({name:R.name,description:R.description,liked:null,timestamp:Date.now(),index:le}));I(_)}catch(n){console.error("Error generating podcast names:",n),T(n instanceof Error?n.message:"An unexpected error occurred")}finally{M(!1),D(!1)}},l=async(t,n)=>{try{await navigator.clipboard.writeText(t),O(n),setTimeout(()=>O(null),2e3)}catch(s){console.error("Failed to copy text:",s)}},m=(t,n)=>{const s=[...C];s[t]={...s[t],liked:n,timestamp:Date.now()},I(s);const a={...y},i=s[t];n?(a.dislikedNames=a.dislikedNames.filter(c=>c.name!==i.name),a.likedNames.find(c=>c.name===i.name)||a.likedNames.push(i)):(a.likedNames=a.likedNames.filter(c=>c.name!==i.name),a.dislikedNames.find(c=>c.name===i.name)||a.dislikedNames.push(i)),a.patterns=ce([...a.likedNames,...a.dislikedNames]),U(a),a.likedNames.length+a.dislikedNames.length>=2&&te(!0),re||(ae(!0),$(!1))},E=()=>{N.trim()&&x(N)},x=async t=>{var n,s,a,i,d,c,_,R,le,he;M(!0),T(""),D(!0);try{const S=p.filter((b,f)=>{const h=C.find(k=>k.index===f);return(h==null?void 0:h.liked)===!0}),Ne=p.filter((b,f)=>{const h=C.find(k=>k.index===f);return(h==null?void 0:h.liked)===!1}).length,ve=Math.max(1,Ne),ge=Math.min(5,S.length+ve)-S.length;if(ge<=0){const b=e(t,y),f=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key="+j,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:b.replace("Create 5","Create 1")}]}]})});if(!f.ok)throw new Error(`API request failed: ${f.status}`);const k=(d=(i=(a=(s=(n=(await f.json()).candidates)==null?void 0:n[0])==null?void 0:s.content)==null?void 0:a.parts)==null?void 0:i[0])==null?void 0:d.text;if(!k)throw new Error("No content received from API");const F=k.match(/\{[\s\S]*\}/);if(!F)throw new Error("No valid JSON found in response");const L=JSON.parse(F[0]);if(!L.podcast_names||!Array.isArray(L.podcast_names))throw new Error("Invalid response format");const Q=[...S,...L.podcast_names].slice(0,5);w(Q),G(Q)}else{const b=e(t,y),f=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key="+j,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:b.replace("Create 5",`Create ${ge}`)}]}]})});if(!f.ok)throw new Error(`API request failed: ${f.status}`);const k=(he=(le=(R=(_=(c=(await f.json()).candidates)==null?void 0:c[0])==null?void 0:_.content)==null?void 0:R.parts)==null?void 0:le[0])==null?void 0:he.text;if(!k)throw new Error("No content received from API");const F=k.match(/\{[\s\S]*\}/);if(!F)throw new Error("No valid JSON found in response");const L=JSON.parse(F[0]);if(!L.podcast_names||!Array.isArray(L.podcast_names))throw new Error("Invalid response format");const Q=[...S,...L.podcast_names];w(Q),G(Q)}se(b=>[...b,p]),U(b=>({...b,generationRound:b.generationRound+1})),I(b=>b.filter(f=>{const h=p[f.index];return S.some(k=>k.name===(h==null?void 0:h.name))})),I(b=>b.map(f=>{const h=p[f.index],k=S.findIndex(F=>F.name===(h==null?void 0:h.name));return k>=0?{...f,index:k}:f}).filter(f=>f.index>=0))}catch(S){console.error("Error generating refined names:",S),T(S instanceof Error?S.message:"Failed to generate refined names. Please try again.")}finally{M(!1),D(!1)}},X=t=>{t.preventDefault(),o()},Z=t=>{t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),o())};return r.jsx("div",{className:`podcast-name-generator ${Y}`,style:A,children:r.jsxs("div",{className:"generator-container",children:[r.jsx("h2",{className:"generator-title",children:"Podcast Name Generator"}),r.jsx("p",{className:"generator-subtitle",children:"Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI"}),ue&&r.jsx("div",{className:"onboarding-banner",children:r.jsxs("div",{className:"onboarding-content",children:[r.jsx("span",{className:"onboarding-icon",children:"💡"}),r.jsxs("div",{className:"onboarding-text",children:[r.jsx("strong",{children:"Pro Tip:"})," After generating names, use the 👍👎 buttons to teach the AI your preferences. It will then generate better, more personalized suggestions just for you!"]})]})}),r.jsx("form",{onSubmit:X,className:"input-form",children:r.jsxs("div",{className:"input-container",children:[r.jsx("textarea",{value:N,onChange:t=>v(t.target.value),onKeyPress:Z,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:g}),r.jsx("button",{type:"submit",disabled:g||!N.trim(),className:"generate-button",children:g?"Generating...":"Generate Names"})]})}),q&&r.jsxs("div",{className:"error-message",children:[r.jsx("span",{className:"error-icon",children:"⚠️"}),q]}),g&&r.jsxs("div",{className:"loading-container",children:[r.jsx("div",{className:"loading-spinner"}),r.jsx("p",{children:ne?"Generating better names based on your preferences...":"Generating creative podcast names..."})]}),p.length>0&&r.jsxs("div",{className:"results-container",children:[r.jsxs("h3",{className:"results-title",children:[y.generationRound>1?"Refined Podcast Name Suggestions":"Your Podcast Name Suggestions",W.length>1&&r.jsxs("span",{className:"generation-counter",children:[" (Round ",y.generationRound,")"]})]}),y.generationRound>1&&r.jsxs("p",{className:"refinement-info",children:["✨ These names are tailored based on your preferences from ",W.length-1," previous round",W.length>2?"s":""]}),y.generationRound===1&&!re&&r.jsx("div",{className:"feedback-hint",children:r.jsxs("div",{className:"feedback-hint-content",children:[r.jsx("span",{className:"feedback-hint-icon",children:"👆"}),r.jsxs("p",{children:[r.jsx("strong",{children:"Like what you see?"})," Use the 👍👎 buttons below to rate each name. The AI will learn your style and generate even better suggestions!"]})]})}),r.jsx("div",{className:"results-grid",children:p.map((t,n)=>{const s=C.find(d=>d.index===n),a=(s==null?void 0:s.liked)===!0,i=(s==null?void 0:s.liked)===!1;return r.jsxs("div",{className:`result-card ${a?"liked":""} ${i?"disliked":""}`,children:[r.jsxs("div",{className:"result-header",children:[r.jsxs("h4",{className:"result-name",children:[t.name,a&&y.generationRound>1&&r.jsx("span",{className:"kept-indicator",title:"This name was kept from your previous selection",children:"⭐"})]}),r.jsxs("div",{className:"result-actions",children:[r.jsxs("div",{className:"feedback-buttons",children:[r.jsx("button",{onClick:()=>m(n,!0),className:`feedback-button like-button ${a?"active":""}`,title:"I like this name",disabled:g,children:"👍"}),r.jsx("button",{onClick:()=>m(n,!1),className:`feedback-button dislike-button ${i?"active":""}`,title:"I don't like this name",disabled:g,children:"👎"})]}),r.jsx("button",{onClick:()=>l(t.name,n),className:"copy-button",title:"Copy podcast name",children:ee===n?"✓ Copied!":"📋 Copy"})]})]}),r.jsx("p",{className:"result-description",children:t.description}),t.suggestedDomain&&r.jsxs("div",{className:"domain-info",children:[r.jsxs("div",{className:"domain-name",children:[r.jsx("span",{className:"domain-label",children:"Domain:"}),r.jsxs("code",{className:"domain-text",children:[t.suggestedDomain,".com"]})]}),r.jsxs("div",{className:`domain-status ${t.domainStatus}`,children:[(t.domainStatus==="checking"||K.has(n))&&r.jsxs(r.Fragment,{children:[r.jsx("span",{className:"domain-spinner",children:"⏳"}),r.jsx("span",{children:"Checking..."})]}),t.domainStatus==="available"&&r.jsxs(r.Fragment,{children:[r.jsx("span",{className:"domain-icon available",children:"✅"}),r.jsx("span",{children:"Available"})]}),t.domainStatus==="taken"&&r.jsxs(r.Fragment,{children:[r.jsx("span",{className:"domain-icon taken",children:"❌"}),r.jsx("span",{children:"Taken"})]}),t.domainStatus==="error"&&r.jsxs(r.Fragment,{children:[r.jsx("span",{className:"domain-icon error",children:"⚠️"}),r.jsx("span",{children:"Check manually"})]})]})]})]},n)})}),de&&!g&&r.jsxs("div",{className:"refinement-section",children:[r.jsx("div",{className:"refinement-info",children:r.jsx("p",{children:"💡 I'll keep your liked names and replace the disliked ones with better suggestions!"})}),r.jsx("button",{onClick:E,className:"refinement-button",disabled:g,children:ne?"🔄 Refining...":"🎯 Replace Disliked Names"})]})]})]})})}});

(function(u,I){typeof exports=="object"&&typeof module<"u"?module.exports=I(require("react")):typeof define=="function"&&define.amd?define(["react"],I):(u=typeof globalThis<"u"?globalThis:u||self,u.PodcastNameGenerator=I(u.React))})(this,function(u){"use strict";var I={exports:{}},Z={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var we;function je(){if(we)return Z;we=1;var E=Symbol.for("react.transitional.element"),W=Symbol.for("react.fragment");function D(b,y,m){var g=null;if(m!==void 0&&(g=""+m),y.key!==void 0&&(g=""+y.key),"key"in y){m={};for(var P in y)P!=="key"&&(m[P]=y[P])}else m=y;return y=m.ref,{$$typeof:E,type:b,key:g,ref:y!==void 0?y:null,props:m}}return Z.Fragment=W,Z.jsx=D,Z.jsxs=D,Z}var Q={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ye;function xe(){return ye||(ye=1,process.env.NODE_ENV!=="production"&&function(){function E(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ue?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case L:return"Fragment";case v:return"Profiler";case z:return"StrictMode";case de:return"Suspense";case F:return"SuspenseList";case be:return"Activity"}if(typeof e=="object")switch(typeof e.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),e.$$typeof){case le:return"Portal";case Ne:return(e.displayName||"Context")+".Provider";case B:return(e._context.displayName||"Context")+".Consumer";case ke:var i=e.render;return e=e.displayName,e||(e=i.displayName||i.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ee:return i=e.displayName||null,i!==null?i:E(e.type)||"Memo";case te:i=e._payload,e=e._init;try{return E(e(i))}catch{}}return null}function W(e){return""+e}function D(e){try{W(e);var i=!1}catch{i=!0}if(i){i=console;var d=i.error,f=typeof Symbol=="function"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object";return d.call(i,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",f),W(e)}}function b(e){if(e===L)return"<>";if(typeof e=="object"&&e!==null&&e.$$typeof===te)return"<...>";try{var i=E(e);return i?"<"+i+">":"<...>"}catch{return"<...>"}}function y(){var e=K.A;return e===null?null:e.getOwner()}function m(){return Error("react-stack-top-frame")}function g(e){if(ne.call(e,"key")){var i=Object.getOwnPropertyDescriptor(e,"key").get;if(i&&i.isReactWarning)return!1}return e.key!==void 0}function P(e,i){function d(){se||(se=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",i))}d.isReactWarning=!0,Object.defineProperty(e,"key",{get:d,configurable:!0})}function ge(){var e=E(this.type);return ae[e]||(ae[e]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),e=this.props.ref,e!==void 0?e:null}function T(e,i,d,f,_,j,X,re){return d=j.ref,e={$$typeof:ce,type:e,key:i,props:j,_owner:_},(d!==void 0?d:null)!==null?Object.defineProperty(e,"ref",{enumerable:!1,get:ge}):Object.defineProperty(e,"ref",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:X}),Object.defineProperty(e,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:re}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function A(e,i,d,f,_,j,X,re){var h=i.children;if(h!==void 0)if(f)if(J(h)){for(f=0;f<h.length;f++)q(h[f]);Object.freeze&&Object.freeze(h)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else q(h);if(ne.call(i,"key")){h=E(e);var O=Object.keys(i).filter(function(n){return n!=="key"});f=0<O.length?"{key: someKey, "+O.join(": ..., ")+": ...}":"{key: someKey}",fe[h+f]||(O=0<O.length?"{"+O.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,f,h,O,h),fe[h+f]=!0)}if(h=null,d!==void 0&&(D(d),h=""+d),g(i)&&(D(i.key),h=""+i.key),"key"in i){d={};for(var oe in i)oe!=="key"&&(d[oe]=i[oe])}else d=i;return h&&P(d,typeof e=="function"?e.displayName||e.name||"Unknown":e),T(e,h,j,_,y(),d,X,re)}function q(e){typeof e=="object"&&e!==null&&e.$$typeof===ce&&e._store&&(e._store.validated=1)}var R=u,ce=Symbol.for("react.transitional.element"),le=Symbol.for("react.portal"),L=Symbol.for("react.fragment"),z=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),B=Symbol.for("react.consumer"),Ne=Symbol.for("react.context"),ke=Symbol.for("react.forward_ref"),de=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),ee=Symbol.for("react.memo"),te=Symbol.for("react.lazy"),be=Symbol.for("react.activity"),ue=Symbol.for("react.client.reference"),K=R.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ne=Object.prototype.hasOwnProperty,J=Array.isArray,V=console.createTask?console.createTask:function(){return null};R={"react-stack-bottom-frame":function(e){return e()}};var se,ae={},H=R["react-stack-bottom-frame"].bind(R,m)(),me=V(b(m)),fe={};Q.Fragment=L,Q.jsx=function(e,i,d,f,_){var j=1e4>K.recentlyCreatedOwnerStacks++;return A(e,i,d,!1,f,_,j?Error("react-stack-top-frame"):H,j?V(b(e)):me)},Q.jsxs=function(e,i,d,f,_){var j=1e4>K.recentlyCreatedOwnerStacks++;return A(e,i,d,!0,f,_,j?Error("react-stack-top-frame"):H,j?V(b(e)):me)}}()),Q}process.env.NODE_ENV==="production"?I.exports=je():I.exports=xe();var s=I.exports;return({apiKey:E="AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",className:W="",style:D={}})=>{const[b,y]=u.useState(""),[m,g]=u.useState([]),[P,ge]=u.useState([]),[T,A]=u.useState(!1),[q,R]=u.useState(null),[ce,le]=u.useState(null),[L,z]=u.useState([]),[v,B]=u.useState({likedNames:[],dislikedNames:[],patterns:{preferredLength:null,preferredStyle:null,likedKeywords:[],dislikedKeywords:[],preferredStructure:null},generationRound:0}),[Ne,ke]=u.useState(!1),[de,F]=u.useState(!1),[ee,te]=u.useState([]),[be,ue]=u.useState(!0),[K,ne]=u.useState(!1),[J,V]=u.useState(new Set),se=n=>n.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"").replace(/^the/,"").substring(0,50),ae=async n=>{try{const a=await fetch(`https://dns.google/resolve?name=${n}&type=A`,{method:"GET",headers:{Accept:"application/json"}});if(!a.ok)return"error";const t=await a.json();return t.Answer&&t.Answer.length>0?"taken":"available"}catch(a){return console.warn("Domain check failed:",a),"error"}},H=async n=>{const a=[...n];for(let t=0;t<a.length;t++){const r=se(a[t].name);a[t].suggestedDomain=r,a[t].domainStatus="checking",V(o=>new Set([...o,t]))}g(a);for(let t=0;t<a.length;t++){const r=`${a[t].suggestedDomain}.com`;try{const o=await ae(r);g(c=>{const l=[...c];return l[t]&&(l[t].domainStatus=o),l})}catch{g(c=>{const l=[...c];return l[t]&&(l[t].domainStatus="error"),l})}finally{V(o=>{const c=new Set(o);return c.delete(t),c})}}},me=(n,a)=>{if(n.length===0)return null;const t=n.map(l=>l.name.split(" ").length),r=a.map(l=>l.name.split(" ").length),o=t.reduce((l,C)=>l+C,0)/t.length,c=r.length>0?r.reduce((l,C)=>l+C,0)/r.length:0;return o<=2&&c>2?"short":o<=4&&(c<=2||c>4)?"medium":o>4&&c<=4?"long":o<=2?"short":o<=4?"medium":"long"},fe=(n,a)=>{if(n.length===0)return null;const t=n.map(o=>o.description.toLowerCase()).join(" "),r=a.map(o=>o.description.toLowerCase()).join(" ");return(t.includes("professional")||t.includes("business"))&&!r.includes("professional")&&!r.includes("business")?"professional":(t.includes("creative")||t.includes("unique"))&&!r.includes("creative")&&!r.includes("unique")?"creative":(t.includes("fun")||t.includes("playful"))&&!r.includes("fun")&&!r.includes("playful")?"playful":"descriptive"},e=n=>{const a=[];n.forEach(r=>{const o=["the","a","an","and","or","but","in","on","at","to","for","of","with","by"],c=r.name.toLowerCase().split(/\s+/).filter(l=>l.length>2&&!o.includes(l));a.push(...c)});const t={};return a.forEach(r=>t[r]=(t[r]||0)+1),Object.entries(t).sort(([,r],[,o])=>o-r).slice(0,5).map(([r])=>r)},i=n=>{const a=n.filter(r=>r.liked===!0),t=n.filter(r=>r.liked===!1);return{preferredLength:me(a,t),preferredStyle:fe(a,t),likedKeywords:e(a),dislikedKeywords:e(t),preferredStructure:null}},d=(n,a)=>{const t=`Create 4 high-converting and catchy podcast names based on the following user input: ${n}.`;let r="";if(a.patterns.preferredLength){const o={short:"1-2 words",medium:"3-4 words",long:"5+ words"};r+=`Focus on ${a.patterns.preferredLength} names (${o[a.patterns.preferredLength]}). `}if(a.patterns.preferredStyle&&(r+=`Use a ${a.patterns.preferredStyle} style. `),a.patterns.likedKeywords.length>0&&(r+=`Incorporate concepts similar to: ${a.patterns.likedKeywords.join(", ")}. `),a.patterns.dislikedKeywords.length>0&&(r+=`Avoid concepts like: ${a.patterns.dislikedKeywords.join(", ")}. `),a.likedNames.length>0){const o=a.likedNames.slice(-2).map(c=>c.name).join('", "');r+=`Generate names with similar appeal to: "${o}". `}return`${t} ${r} Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`},f=async(n=!1)=>{if(!b.trim()){R("Please describe what your podcast is about");return}A(!0),R(null),g([]),n?F(!0):(z([]),ke(!1),F(!1),ue(!0),ne(!1));try{const a=n?d(b,v):`Create 4 high-converting and catchy podcast names based on the following user input: ${b}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 4 names are creative, memorable, and relevant to the input topic.`,t=await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${E}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:a}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!t.ok)throw new Error(`API request failed: ${t.status} ${t.statusText}`);const r=await t.json();if(!r.candidates||!r.candidates[0]||!r.candidates[0].content)throw new Error("Invalid response format from API");const c=r.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!c)throw new Error("No valid JSON found in API response");const l=JSON.parse(c[0]);if(!l.podcast_names||!Array.isArray(l.podcast_names))throw new Error("Invalid response structure");g(l.podcast_names),H(l.podcast_names),te(x=>[...x,l.podcast_names]),B(x=>({...x,generationRound:x.generationRound+1}));const C=l.podcast_names.map((x,Y)=>({name:x.name,description:x.description,liked:null,timestamp:Date.now(),index:Y}));z(C)}catch(a){console.error("Error generating podcast names:",a),R(a instanceof Error?a.message:"An unexpected error occurred")}finally{A(!1),F(!1)}},_=async(n,a)=>{try{await navigator.clipboard.writeText(n),le(a),setTimeout(()=>le(null),2e3)}catch(t){console.error("Failed to copy text:",t)}},j=async(n,a)=>{const t=m[n];t&&(a?(ge(r=>r.find(o=>o.name===t.name)?r:[...r,t]),g(r=>r.filter((o,c)=>c!==n)),B(r=>{const o={...r};return o.dislikedNames=o.dislikedNames.filter(c=>c.name!==t.name),o.likedNames.find(c=>c.name===t.name)||o.likedNames.push({name:t.name,description:t.description,liked:!0,timestamp:Date.now(),index:n}),o.patterns=i([...o.likedNames,...o.dislikedNames]),o}),b.trim()&&X()):(g(r=>r.filter((o,c)=>c!==n)),B(r=>{const o={...r};return o.likedNames=o.likedNames.filter(c=>c.name!==t.name),o.dislikedNames.find(c=>c.name===t.name)||o.dislikedNames.push({name:t.name,description:t.description,liked:!1,timestamp:Date.now(),index:n}),o.patterns=i([...o.likedNames,...o.dislikedNames]),o}),b.trim()&&X()),K||(ne(!0),ue(!1)))},X=async()=>{var n,a,t,r,o;try{A(!0);const c=d(b,v),l=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key="+E,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:c.replace("Create 4","Create 1")}]}]})});if(!l.ok)throw new Error(`API request failed: ${l.status}`);const x=(o=(r=(t=(a=(n=(await l.json()).candidates)==null?void 0:n[0])==null?void 0:a.content)==null?void 0:t.parts)==null?void 0:r[0])==null?void 0:o.text;if(!x)throw new Error("No content received from API");const Y=x.match(/\{[\s\S]*\}/);if(!Y)throw new Error("No valid JSON found in response");const M=JSON.parse(Y[0]);if(!M.podcast_names||!Array.isArray(M.podcast_names)||M.podcast_names.length===0)throw new Error("Invalid response format");const S=M.podcast_names[0];g(he=>[...he,S]);const pe=se(S.name),ve=await ae(pe);g(he=>{const G=[...he],N=G.length-1;return G[N]={...G[N],suggestedDomain:pe,domainStatus:ve},G})}catch(c){console.error("Error generating new suggestion:",c),R("Failed to generate new suggestion. Please try again.")}finally{A(!1)}},re=()=>{b.trim()&&h(b)},h=async n=>{var a,t,r,o,c,l,C,x,Y,M;A(!0),R(""),F(!0);try{const S=m.filter((N,p)=>{const k=L.find(w=>w.index===p);return(k==null?void 0:k.liked)===!0}),pe=m.filter((N,p)=>{const k=L.find(w=>w.index===p);return(k==null?void 0:k.liked)===!1}).length,ve=Math.max(1,pe),G=Math.min(5,S.length+ve)-S.length;if(G<=0){const N=d(n,v),p=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key="+E,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:N.replace("Create 5","Create 1")}]}]})});if(!p.ok)throw new Error(`API request failed: ${p.status}`);const w=(c=(o=(r=(t=(a=(await p.json()).candidates)==null?void 0:a[0])==null?void 0:t.content)==null?void 0:r.parts)==null?void 0:o[0])==null?void 0:c.text;if(!w)throw new Error("No content received from API");const $=w.match(/\{[\s\S]*\}/);if(!$)throw new Error("No valid JSON found in response");const U=JSON.parse($[0]);if(!U.podcast_names||!Array.isArray(U.podcast_names))throw new Error("Invalid response format");const ie=[...S,...U.podcast_names].slice(0,5);g(ie),H(ie)}else{const N=d(n,v),p=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key="+E,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:N.replace("Create 5",`Create ${G}`)}]}]})});if(!p.ok)throw new Error(`API request failed: ${p.status}`);const w=(M=(Y=(x=(C=(l=(await p.json()).candidates)==null?void 0:l[0])==null?void 0:C.content)==null?void 0:x.parts)==null?void 0:Y[0])==null?void 0:M.text;if(!w)throw new Error("No content received from API");const $=w.match(/\{[\s\S]*\}/);if(!$)throw new Error("No valid JSON found in response");const U=JSON.parse($[0]);if(!U.podcast_names||!Array.isArray(U.podcast_names))throw new Error("Invalid response format");const ie=[...S,...U.podcast_names];g(ie),H(ie)}te(N=>[...N,m]),B(N=>({...N,generationRound:N.generationRound+1})),z(N=>N.filter(p=>{const k=m[p.index];return S.some(w=>w.name===(k==null?void 0:k.name))})),z(N=>N.map(p=>{const k=m[p.index],w=S.findIndex($=>$.name===(k==null?void 0:k.name));return w>=0?{...p,index:w}:p}).filter(p=>p.index>=0))}catch(S){console.error("Error generating refined names:",S),R(S instanceof Error?S.message:"Failed to generate refined names. Please try again.")}finally{A(!1),F(!1)}},O=n=>{n.preventDefault(),f()},oe=n=>{n.key==="Enter"&&!n.shiftKey&&(n.preventDefault(),f())};return s.jsx("div",{className:`podcast-name-generator ${W}`,style:D,children:s.jsxs("div",{className:"generator-container",children:[s.jsx("h2",{className:"generator-title",children:"Podcast Name Generator"}),s.jsx("p",{className:"generator-subtitle",children:"Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI"}),be&&s.jsx("div",{className:"onboarding-banner",children:s.jsxs("div",{className:"onboarding-content",children:[s.jsx("span",{className:"onboarding-icon",children:"💡"}),s.jsxs("div",{className:"onboarding-text",children:[s.jsx("strong",{children:"Smart AI Learning:"})," The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."]})]})}),s.jsx("form",{onSubmit:O,className:"input-form",children:s.jsxs("div",{className:"input-container",children:[s.jsx("textarea",{value:b,onChange:n=>y(n.target.value),onKeyPress:oe,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:T}),s.jsx("button",{type:"submit",disabled:T||!b.trim(),className:"generate-button",children:T?"Generating...":"Generate Names"})]})}),q&&s.jsxs("div",{className:"error-message",children:[s.jsx("span",{className:"error-icon",children:"⚠️"}),q]}),T&&s.jsxs("div",{className:"loading-container",children:[s.jsx("div",{className:"loading-spinner"}),s.jsx("p",{children:de?"Generating better names based on your preferences...":"Generating creative podcast names..."})]}),m.length>0&&s.jsxs("div",{className:"results-container",children:[s.jsxs("h3",{className:"results-title",children:[v.generationRound>1?"Refined Podcast Name Suggestions":"Your Podcast Name Suggestions",ee.length>1&&s.jsxs("span",{className:"generation-counter",children:[" (Round ",v.generationRound,")"]})]}),v.generationRound>1&&s.jsxs("p",{className:"refinement-info",children:["✨ These names are tailored based on your preferences from ",ee.length-1," previous round",ee.length>2?"s":""]}),v.generationRound===1&&!K&&s.jsx("div",{className:"feedback-hint",children:s.jsxs("div",{className:"feedback-hint-content",children:[s.jsx("span",{className:"feedback-hint-icon",children:"👆"}),s.jsxs("p",{children:[s.jsx("strong",{children:"Like what you see?"})," Use the 👍👎 buttons below! Liked names move to favorites, disliked names disappear and get replaced with better AI suggestions!"]})]})}),P.length>0&&s.jsxs("div",{className:"favorites-section",children:[s.jsxs("div",{className:"favorites-header",children:[s.jsxs("h3",{children:["⭐ Favoured Podcast Names (",P.length,")"]}),s.jsx("p",{className:"favorites-subtitle",children:"Names you loved - the AI is learning from these to suggest better options!"})]}),s.jsx("div",{className:"favorites-grid",children:P.map((n,a)=>s.jsxs("div",{className:"favorite-card",children:[s.jsxs("div",{className:"favorite-content",children:[s.jsx("h4",{className:"favorite-name",children:n.name}),s.jsx("p",{className:"favorite-description",children:n.description}),n.suggestedDomain&&s.jsxs("div",{className:"domain-info inline",children:[s.jsx("span",{className:"domain-label",children:"Domain:"}),s.jsx("span",{className:"domain-name",children:n.suggestedDomain}),s.jsx("span",{className:`domain-status ${n.domainStatus}`,children:n.domainStatus==="available"?"✅ Available":n.domainStatus==="taken"?"❌ Taken":n.domainStatus==="error"?"⚠️ Check manually":"🔍 Checking..."})]})]}),s.jsx("div",{className:"favorite-actions",children:s.jsx("button",{onClick:()=>_(n.name,-1),className:"copy-button small",title:"Copy to clipboard",children:"📋 Copy"})})]},`fav-${a}`))})]}),s.jsxs("div",{className:"suggestions-section",children:[s.jsxs("div",{className:"suggestions-header",children:[s.jsxs("h3",{children:["🎯 Current Suggestions (",m.length,"/4)"]}),s.jsx("p",{className:"suggestions-subtitle",children:v.likedNames.length>0||v.dislikedNames.length>0?`AI is learning from your ${v.likedNames.length} likes and ${v.dislikedNames.length} dislikes to improve these suggestions!`:"Rate these suggestions to help the AI learn your preferences and generate better names!"})]}),s.jsx("div",{className:"results-grid",children:m.map((n,a)=>{const t=L.find(c=>c.index===a),r=(t==null?void 0:t.liked)===!0,o=(t==null?void 0:t.liked)===!1;return s.jsxs("div",{className:`result-card ${r?"liked":""} ${o?"disliked":""}`,children:[s.jsxs("div",{className:"result-header",children:[s.jsxs("h4",{className:"result-name",children:[n.name,r&&v.generationRound>1&&s.jsx("span",{className:"kept-indicator",title:"This name was kept from your previous selection",children:"⭐"})]}),s.jsxs("div",{className:"result-actions",children:[s.jsxs("div",{className:"feedback-buttons",children:[s.jsx("button",{onClick:()=>j(a,!0),className:`feedback-button like-button ${r?"active":""}`,title:"I like this name",disabled:T,children:"👍"}),s.jsx("button",{onClick:()=>j(a,!1),className:`feedback-button dislike-button ${o?"active":""} ${J.has(a)?"loading":""}`,title:J.has(a)?"Generating replacement...":"I don't like this name",disabled:T||J.has(a),children:J.has(a)?"🔄":"👎"})]}),s.jsx("button",{onClick:()=>_(n.name,a),className:"copy-button",title:"Copy podcast name",children:ce===a?"✓ Copied!":"📋 Copy"})]})]}),s.jsx("p",{className:"result-description",children:n.description}),n.suggestedDomain&&s.jsxs("div",{className:"domain-info inline",children:[s.jsx("span",{className:"domain-label",children:"Domain:"}),s.jsxs("code",{className:"domain-text",children:[n.suggestedDomain,".com"]}),s.jsxs("span",{className:`domain-status ${n.domainStatus}`,children:[(n.domainStatus==="checking"||J.has(a))&&"⏳ Checking...",n.domainStatus==="available"&&"✅ Available",n.domainStatus==="taken"&&"❌ Taken",n.domainStatus==="error"&&"⚠️ Check manually"]})]})]},a)})})]}),Ne&&!T&&s.jsxs("div",{className:"refinement-section",children:[s.jsx("div",{className:"refinement-info",children:s.jsx("p",{children:"💡 I'll keep your liked names and replace the disliked ones with better suggestions!"})}),s.jsx("button",{onClick:re,className:"refinement-button",disabled:T,children:de?"🔄 Refining...":"🎯 Replace Disliked Names"})]})]})]})})}});

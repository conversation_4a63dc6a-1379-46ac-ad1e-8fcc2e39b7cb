(function(h,F){typeof exports=="object"&&typeof module<"u"?module.exports=F(require("react")):typeof define=="function"&&define.amd?define(["react"],F):(h=typeof globalThis<"u"?globalThis:h||self,h.PodcastNameGenerator=F(h.React))})(this,function(h){"use strict";var F={exports:{}},Z={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xe;function Re(){if(xe)return Z;xe=1;var P=Symbol.for("react.transitional.element"),Y=Symbol.for("react.fragment");function j(M,p,g){var O=null;if(g!==void 0&&(O=""+g),p.key!==void 0&&(O=""+p.key),"key"in p){g={};for(var $ in p)$!=="key"&&(g[$]=p[$])}else g=p;return p=g.ref,{$$typeof:P,type:M,key:O,ref:p!==void 0?p:null,props:g}}return Z.Fragment=Y,Z.jsx=j,Z.jsxs=j,Z}var ee={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var je;function Ee(){return je||(je=1,process.env.NODE_ENV!=="production"&&function(){function P(s){if(s==null)return null;if(typeof s=="function")return s.$$typeof===Ne?null:s.displayName||s.name||null;if(typeof s=="string")return s;switch(s){case C:return"Fragment";case ce:return"Profiler";case q:return"StrictMode";case z:return"Suspense";case ve:return"SuspenseList";case B:return"Activity"}if(typeof s=="object")switch(typeof s.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),s.$$typeof){case ne:return"Portal";case ge:return(s.displayName||"Context")+".Provider";case fe:return(s._context.displayName||"Context")+".Consumer";case de:var c=s.render;return s=s.displayName,s||(s=c.displayName||c.name||"",s=s!==""?"ForwardRef("+s+")":"ForwardRef"),s;case me:return c=s.displayName||null,c!==null?c:P(s.type)||"Memo";case ae:c=s._payload,s=s._init;try{return P(s(c))}catch{}}return null}function Y(s){return""+s}function j(s){try{Y(s);var c=!1}catch{c=!0}if(c){c=console;var m=c.error,v=typeof Symbol=="function"&&Symbol.toStringTag&&s[Symbol.toStringTag]||s.constructor.name||"Object";return m.call(c,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",v),Y(s)}}function M(s){if(s===C)return"<>";if(typeof s=="object"&&s!==null&&s.$$typeof===ae)return"<...>";try{var c=P(s);return c?"<"+c+">":"<...>"}catch{return"<...>"}}function p(){var s=U.A;return s===null?null:s.getOwner()}function g(){return Error("react-stack-top-frame")}function O(s){if(be.call(s,"key")){var c=Object.getOwnPropertyDescriptor(s,"key").get;if(c&&c.isReactWarning)return!1}return s.key!==void 0}function $(s,c){function m(){re||(re=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",c))}m.isReactWarning=!0,Object.defineProperty(s,"key",{get:m,configurable:!0})}function E(){var s=P(this.type);return ie[s]||(ie[s]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),s=this.props.ref,s!==void 0?s:null}function J(s,c,m,v,A,S,X,H){return m=S.ref,s={$$typeof:se,type:s,key:c,props:S,_owner:A},(m!==void 0?m:null)!==null?Object.defineProperty(s,"ref",{enumerable:!1,get:E}):Object.defineProperty(s,"ref",{enumerable:!1,value:null}),s._store={},Object.defineProperty(s._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(s,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(s,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:X}),Object.defineProperty(s,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:H}),Object.freeze&&(Object.freeze(s.props),Object.freeze(s)),s}function te(s,c,m,v,A,S,X,H){var N=c.children;if(N!==void 0)if(v)if(ue(N)){for(v=0;v<N.length;v++)I(N[v]);Object.freeze&&Object.freeze(N)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else I(N);if(be.call(c,"key")){N=P(s);var D=Object.keys(c).filter(function(pe){return pe!=="key"});v=0<D.length?"{key: someKey, "+D.join(": ..., ")+": ...}":"{key: someKey}",he[N+v]||(D=0<D.length?"{"+D.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,v,N,D,N),he[N+v]=!0)}if(N=null,m!==void 0&&(j(m),N=""+m),O(c)&&(j(c.key),N=""+c.key),"key"in c){m={};for(var Q in c)Q!=="key"&&(m[Q]=c[Q])}else m=c;return N&&$(m,typeof s=="function"?s.displayName||s.name||"Unknown":s),J(s,N,S,A,p(),m,X,H)}function I(s){typeof s=="object"&&s!==null&&s.$$typeof===se&&s._store&&(s._store.validated=1)}var K=h,se=Symbol.for("react.transitional.element"),ne=Symbol.for("react.portal"),C=Symbol.for("react.fragment"),q=Symbol.for("react.strict_mode"),ce=Symbol.for("react.profiler"),fe=Symbol.for("react.consumer"),ge=Symbol.for("react.context"),de=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),ve=Symbol.for("react.suspense_list"),me=Symbol.for("react.memo"),ae=Symbol.for("react.lazy"),B=Symbol.for("react.activity"),Ne=Symbol.for("react.client.reference"),U=K.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,be=Object.prototype.hasOwnProperty,ue=Array.isArray,T=console.createTask?console.createTask:function(){return null};K={"react-stack-bottom-frame":function(s){return s()}};var re,ie={},oe=K["react-stack-bottom-frame"].bind(K,g)(),V=T(M(g)),he={};ee.Fragment=C,ee.jsx=function(s,c,m,v,A){var S=1e4>U.recentlyCreatedOwnerStacks++;return te(s,c,m,!1,v,A,S?Error("react-stack-top-frame"):oe,S?T(M(s)):V)},ee.jsxs=function(s,c,m,v,A){var S=1e4>U.recentlyCreatedOwnerStacks++;return te(s,c,m,!0,v,A,S?Error("react-stack-top-frame"):oe,S?T(M(s)):V)}}()),ee}process.env.NODE_ENV==="production"?F.exports=Re():F.exports=Ee();var e=F.exports;return({className:P="",style:Y={}})=>{const[j,M]=h.useState(""),[p,g]=h.useState([]),[O,$]=h.useState([]),[E,J]=h.useState(!1),[te,I]=h.useState(null),[K,se]=h.useState(null),[ne,C]=h.useState([]),[q,ce]=h.useState({likedNames:[],dislikedNames:[],patterns:{preferredLength:null,preferredStyle:null,likedKeywords:[],dislikedKeywords:[],preferredStructure:null}}),[fe,ge]=h.useState(!1),[de,z]=h.useState(!1),[ve,me]=h.useState(!1),[ae,B]=h.useState(new Set),[Ne,U]=h.useState(new Set),[be,ue]=h.useState(0),[T,re]=h.useState(!1),ie=100,oe=()=>{const t=document.createElement("canvas"),n=t.getContext("2d");n.textBaseline="top",n.font="14px Arial",n.fillText("Usage tracking",2,2);const a=t.toDataURL(),o=navigator.userAgent,i=navigator.language,l=Intl.DateTimeFormat().resolvedOptions().timeZone,r=a+o+i+l;let d=0;for(let u=0;u<r.length;u++){const b=r.charCodeAt(u);d=(d<<5)-d+b,d=d&d}return`podcast_usage_${Math.abs(d)}`},V=()=>{const t=oe(),n=new Date().toDateString(),a=`${t}_${n}`,o=localStorage.getItem(a),i=o?parseInt(o,10):0;return ue(i),i>=ie?(re(!0),!1):!0},he=(t=1)=>{const n=oe(),a=new Date().toDateString(),o=`${n}_${a}`,i=localStorage.getItem(o),r=(i?parseInt(i,10):0)+t;localStorage.setItem(o,r.toString()),ue(r),r>=ie&&re(!0)};h.useEffect(()=>{V()},[]);const s=t=>{let n=t.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g," ").replace(/^the\s+/,"").trim();const a=["the","and","for","with","from","show","podcast","cast","of","in","on","at","to","a","an"],o=n.split(" "),i=o.filter(r=>r.length>2&&!a.includes(r));if(i.length===1){const r=i[0];return r.length>=6&&r.length<=15?r:r.length<6?r+"pod":c(r)}if(i.length>=2){const r=i[0],d=i[1],u=r+d;if(u.length>=6&&u.length<=15)return u;const b=c(r),W=c(d),x=b+W;if(x.length>=6&&x.length<=15)return x;if(x.length>15)return b+"-"+W}if(i.length>0){const r=c(i[0]),d=["cast","pod","show","talk"];for(const u of d){const b=r+u;if(b.length>=6&&b.length<=15)return b}if(r.length>=6&&r.length<=15)return r}const l=o[0];return l&&l.length>=3?c(l)+"pod":"podcast"+Math.random().toString(36).substring(2,5)},c=t=>{if(t.length<=8)return t;const n={business:"biz",entrepreneur:"entre",marketing:"market",finance:"fin",startup:"start",leadership:"lead",strategy:"strat",success:"win",growth:"grow",innovation:"innov",management:"manage",technology:"tech",development:"dev",digital:"digi",software:"soft",coding:"code",programming:"prog",stories:"story",journey:"path",adventure:"quest",creative:"create",entertainment:"fun",education:"learn",knowledge:"know",wisdom:"wise",lifestyle:"life",wellness:"well",fitness:"fit",health:"heal",mindset:"mind",motivation:"motive",inspiration:"inspire",american:"usa",european:"euro",international:"global",community:"comm",culture:"cult",society:"social"};if(n[t])return n[t];const a=["super","mega","ultra","micro","mini","multi"];for(const i of a)if(t.startsWith(i)&&t.length>i.length+3){const l=t.substring(i.length);if(l.length<=8)return l}const o=["ing","tion","sion","ness","ment","able","ible"];for(const i of o)if(t.endsWith(i)&&t.length>i.length+4){const l=t.substring(0,t.length-i.length);if(l.length>=4&&l.length<=8)return l}return t.length>8?t.substring(0,8):t},m=async t=>{try{const n=await fetch(`https://dns.google/resolve?name=${t}&type=A`,{method:"GET",headers:{Accept:"application/json"}});if(!n.ok)return"error";const a=await n.json();return a.Answer&&a.Answer.length>0?"taken":"available"}catch(n){return console.warn("Domain check failed:",n),"error"}},v=async t=>{const n=[...t];for(let a=0;a<n.length;a++){const o=s(n[a].name);n[a].suggestedDomain=o,n[a].domainStatus="checking",B(i=>new Set([...i,a]))}g(n);for(let a=0;a<n.length;a++){const o=`${n[a].suggestedDomain}.com`;try{const i=await m(o);g(l=>{const r=[...l];return r[a]&&(r[a].domainStatus=i),r})}catch{g(l=>{const r=[...l];return r[a]&&(r[a].domainStatus="error"),r})}finally{B(i=>{const l=new Set(i);return l.delete(a),l})}}},A=(t,n)=>{if(t.length===0)return null;const a=t.map(r=>r.name.split(" ").length),o=n.map(r=>r.name.split(" ").length),i=a.reduce((r,d)=>r+d,0)/a.length,l=o.length>0?o.reduce((r,d)=>r+d,0)/o.length:0;return i<=2&&l>2?"short":i<=4&&(l<=2||l>4)?"medium":i>4&&l<=4?"long":i<=2?"short":i<=4?"medium":"long"},S=(t,n)=>{if(t.length===0)return null;const a=t.map(i=>i.description.toLowerCase()).join(" "),o=n.map(i=>i.description.toLowerCase()).join(" ");return(a.includes("professional")||a.includes("business"))&&!o.includes("professional")&&!o.includes("business")?"professional":(a.includes("creative")||a.includes("unique"))&&!o.includes("creative")&&!o.includes("unique")?"creative":(a.includes("fun")||a.includes("playful"))&&!o.includes("fun")&&!o.includes("playful")?"playful":"descriptive"},X=t=>{const n=[];t.forEach(o=>{const i=["the","a","an","and","or","but","in","on","at","to","for","of","with","by"],l=o.name.toLowerCase().split(/\s+/).filter(r=>r.length>2&&!i.includes(r));n.push(...l)});const a={};return n.forEach(o=>a[o]=(a[o]||0)+1),Object.entries(a).sort(([,o],[,i])=>i-o).slice(0,5).map(([o])=>o)},H=t=>{const n=t.filter(o=>o.liked===!0),a=t.filter(o=>o.liked===!1);return{preferredLength:A(n,a),preferredStyle:S(n,a),likedKeywords:X(n),dislikedKeywords:X(a),preferredStructure:null}},N=(t,n)=>{const a=[...n.likedNames.map(r=>r.name.toLowerCase()),...n.dislikedNames.map(r=>r.name.toLowerCase()),...p.map(r=>r.name.toLowerCase())],o=`Create 4 unique, high-converting podcast names for: ${t}`;let i=`

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;a.length>0&&(i+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${a.map(r=>`- ${r}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);let l="";if(n.patterns.preferredLength&&(l+=`
Focus on ${{short:"1-2 words (punchy and memorable)",medium:"2-3 words (balanced and brandable)",long:"3-4 words (descriptive but still catchy)"}[n.patterns.preferredLength]}. `),n.patterns.preferredStyle&&(l+=`Use ${{descriptive:"clear, straightforward names that explain the content",creative:"imaginative, metaphorical, or playful names",professional:"authoritative, business-focused names",playful:"fun, energetic, engaging names"}[n.patterns.preferredStyle]||n.patterns.preferredStyle}. `),n.patterns.likedKeywords.length>0&&(l+=`
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `),n.patterns.dislikedKeywords.length>0&&(l+=`
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `),n.likedNames.length>0){const r=n.likedNames.slice(-2).map(d=>d.name).join('", "');l+=`
Generate names with similar appeal to: "${r}" (but completely different concepts). `}return`${o}${i}${l}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`},D=(t,n,a=1)=>{const o=[...n.likedNames.map(d=>d.name.toLowerCase()),...n.dislikedNames.map(d=>d.name.toLowerCase()),...p.map(d=>d.name.toLowerCase())],i=`Create ${a} unique, high-converting podcast name${a>1?"s":""} for: ${t}`;let l=`

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;o.length>0&&(l+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${o.map(d=>`- ${d}`).join(`
`)}
Do not create names similar to any of the above.`);let r="";return n.patterns.likedKeywords.length>0&&(r+=`
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `),n.patterns.dislikedKeywords.length>0&&(r+=`
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `),`${i}${l}${r}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works"}${a>1?', {"name": "Unique Name 2", "description": "Why this works"}':""}]}`},Q=async(t=!1)=>{if(!j.trim()){I("Please describe what your podcast is about");return}if(!V()){I(null);return}J(!0),I(null),g([]),t?z(!0):(C([]),ge(!1),z(!1),me(!1));try{const n=t?N(j,q):`Create 4 unique, high-converting podcast names for: ${j}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`,a=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:n}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!a.ok)throw new Error(`API request failed: ${a.status} ${a.statusText}`);const o=await a.json();if(!o.candidates||!o.candidates[0]||!o.candidates[0].content)throw new Error("Invalid response format from API");const l=o.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!l)throw new Error("No valid JSON found in API response");const r=JSON.parse(l[0]);if(!r.podcast_names||!Array.isArray(r.podcast_names))throw new Error("Invalid response structure");g(r.podcast_names),he(4),v(r.podcast_names);const d=r.podcast_names.map((u,b)=>({name:u.name,description:u.description,liked:null,timestamp:Date.now(),index:b}));C(d)}catch(n){console.error("Error generating podcast names:",n),I(n instanceof Error?n.message:"An unexpected error occurred")}finally{J(!1),z(!1)}},pe=async(t,n)=>{try{await navigator.clipboard.writeText(t),se(n),setTimeout(()=>se(null),2e3)}catch(a){console.error("Failed to copy text:",a)}},we=async(t,n)=>{const a=p[t];a&&(n?($(o=>o.find(i=>i.name===a.name)?o:[...o,a]),ce(o=>{const i={...o};return i.dislikedNames=i.dislikedNames.filter(l=>l.name!==a.name),i.likedNames.find(l=>l.name===a.name)||i.likedNames.push({name:a.name,description:a.description,liked:!0,timestamp:Date.now(),index:t}),i.patterns=H([...i.likedNames,...i.dislikedNames]),i}),U(o=>new Set([...o,t]))):(U(o=>new Set([...o,t])),ce(o=>{const i={...o};return i.likedNames=i.likedNames.filter(l=>l.name!==a.name),i.dislikedNames.find(l=>l.name===a.name)||i.dislikedNames.push({name:a.name,description:a.description,liked:!1,timestamp:Date.now(),index:t}),i.patterns=H([...i.likedNames,...i.dislikedNames]),i})),j.trim()&&Te(t),ve||me(!0))},Te=async t=>{var n,a,o,i,l,r;if(V())try{B(_=>new Set([..._,t]));const d=N(j,q),u=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:d}]}]})});if(!u.ok)throw new Error("Failed to generate replacement suggestion");const W=(l=(i=(o=(a=(n=(await u.json()).candidates)==null?void 0:n[0])==null?void 0:a.content)==null?void 0:o.parts)==null?void 0:i[0])==null?void 0:l.text;if(!W)throw new Error("No content in API response");const x=W.match(/\{[\s\S]*\}/);if(!x)throw new Error("No valid JSON found in response");const G=(r=JSON.parse(x[0]).names)==null?void 0:r[0];if(G&&(g(_=>{const R=[..._];return R[t]={name:G.name,description:G.description,suggestedDomain:G.suggestedDomain,domainStatus:"checking"},R}),U(_=>{const R=new Set(_);return R.delete(t),R}),C(_=>_.filter(R=>R.index!==t)),G.suggestedDomain)){const _=await m(G.suggestedDomain);g(R=>{const w=[...R];return w[t]&&(w[t].domainStatus=_),w})}}catch(d){console.error("Error generating replacement suggestion:",d),U(u=>{const b=new Set(u);return b.delete(t),b}),C(u=>u.filter(b=>b.index!==t))}finally{B(d=>{const u=new Set(d);return u.delete(t),u})}},Ce=()=>{j.trim()&&Ae(j)},Ae=async t=>{var n,a,o,i,l,r,d,u,b,W;J(!0),I(""),z(!0);try{const x=p.filter((w,k)=>{const f=ne.find(y=>y.index===k);return(f==null?void 0:f.liked)===!0}),Se=p.filter((w,k)=>{const f=ne.find(y=>y.index===k);return(f==null?void 0:f.liked)===!1}).length,G=Math.max(1,Se),R=Math.min(5,x.length+G)-x.length;if(R<=0){const w=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:D(t,q,1)}]}]})});if(!w.ok)throw new Error(`API request failed: ${w.status}`);const f=(l=(i=(o=(a=(n=(await w.json()).candidates)==null?void 0:n[0])==null?void 0:a.content)==null?void 0:o.parts)==null?void 0:i[0])==null?void 0:l.text;if(!f)throw new Error("No content received from API");const y=f.match(/\{[\s\S]*\}/);if(!y)throw new Error("No valid JSON found in response");const L=JSON.parse(y[0]);if(!L.podcast_names||!Array.isArray(L.podcast_names))throw new Error("Invalid response format");const le=[...x,...L.podcast_names].slice(0,5);g(le),v(le)}else{const w=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:D(t,q,R)}]}]})});if(!w.ok)throw new Error(`API request failed: ${w.status}`);const f=(W=(b=(u=(d=(r=(await w.json()).candidates)==null?void 0:r[0])==null?void 0:d.content)==null?void 0:u.parts)==null?void 0:b[0])==null?void 0:W.text;if(!f)throw new Error("No content received from API");const y=f.match(/\{[\s\S]*\}/);if(!y)throw new Error("No valid JSON found in response");const L=JSON.parse(y[0]);if(!L.podcast_names||!Array.isArray(L.podcast_names))throw new Error("Invalid response format");const le=[...x,...L.podcast_names];g(le),v(le)}C(w=>w.filter(k=>{const f=p[k.index];return x.some(y=>y.name===(f==null?void 0:f.name))})),C(w=>w.map(k=>{const f=p[k.index],y=x.findIndex(L=>L.name===(f==null?void 0:f.name));return y>=0?{...k,index:y}:k}).filter(k=>k.index>=0))}catch(x){console.error("Error generating refined names:",x),I(x instanceof Error?x.message:"Failed to generate refined names. Please try again.")}finally{J(!1),z(!1)}},ye=t=>{t.preventDefault(),Q()},ke=t=>{t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),Q())};return e.jsx("div",{className:`podcast-name-generator ${P}`,style:Y,children:e.jsxs("div",{className:"generator-container",children:[e.jsxs("div",{className:"header-section",children:[e.jsx("h1",{className:"main-title",children:"Free Podcast Name Generator"}),e.jsx("h2",{className:"main-subtitle",children:"Create the Perfect Name for Your Podcast in Seconds"})]}),e.jsxs("div",{className:"benefits-section",children:[e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"100% Free Forever"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"No Sign-up Required"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"Instant Results"})]})]}),T&&e.jsx("div",{className:"limit-reached-banner",children:e.jsxs("div",{className:"limit-content",children:[e.jsx("span",{className:"limit-icon",children:"⚠️"}),e.jsx("div",{className:"limit-text",children:e.jsx("p",{children:"You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below."})})]})}),p.length===0&&e.jsx("div",{className:"initial-input-section",children:e.jsx("form",{onSubmit:ye,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:j,onChange:t=>M(t.target.value),onKeyPress:ke,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:E}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:E||!j.trim()||T,className:`generate-button ${T?"disabled":""}`,children:E?"Generating...":T?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star star-partial",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})}),te&&e.jsxs("div",{className:"error-message",children:[e.jsx("span",{className:"error-icon",children:"⚠️"}),te]}),E&&e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:de?"Generating better names based on your preferences...":"Generating creative podcast names..."})]}),p.length>0&&e.jsxs("div",{className:"results-container",children:[O.length>0&&e.jsxs("div",{className:"favorites-section",children:[e.jsxs("div",{className:"favorites-header",children:[e.jsxs("h3",{children:["🏆 Your Winning Podcast Names (",O.length,")"]}),e.jsx("p",{className:"favorites-subtitle",children:"Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!"})]}),e.jsx("div",{className:"favorites-grid",children:O.map((t,n)=>e.jsxs("div",{className:"favorite-card",children:[e.jsxs("div",{className:"favorite-content",children:[e.jsx("h4",{className:"favorite-name",children:t.name}),e.jsx("p",{className:"favorite-description",children:t.description}),t.suggestedDomain&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsx("span",{className:"domain-name",children:t.suggestedDomain}),e.jsx("span",{className:`domain-status ${t.domainStatus}`,children:t.domainStatus==="available"?"✅ Available":t.domainStatus==="taken"?"❌ Taken":t.domainStatus==="error"?"⚠️ Check manually":"🔍 Checking..."})]})]}),e.jsx("div",{className:"favorite-actions",children:e.jsx("button",{onClick:()=>pe(t.name,-1),className:"copy-button small",title:"Copy to clipboard",children:"📋 Copy"})})]},`fav-${n}`))})]}),e.jsxs("div",{className:"input-section-simple",children:[e.jsx("div",{className:"input-help-message-simple",children:e.jsxs("p",{className:"input-sub-description",children:["💡 Want different suggestions? Update your description below - ",e.jsx("strong",{children:"your favorites will stay safe!"})]})}),e.jsx("form",{onSubmit:ye,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:j,onChange:t=>M(t.target.value),onKeyPress:ke,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:E}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:E||!j.trim()||T,className:`generate-button ${T?"disabled":""}`,children:E?"Generating...":T?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star star-partial",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})]}),e.jsxs("div",{className:"suggestions-section",children:[e.jsx("div",{className:"suggestions-header",children:e.jsx("h3",{children:"🎯 Current Suggestions"})}),e.jsx("div",{className:"onboarding-banner",children:e.jsxs("div",{className:"onboarding-content",children:[e.jsx("span",{className:"onboarding-icon",children:"💡"}),e.jsxs("div",{className:"onboarding-text",children:[e.jsx("strong",{children:"Smart AI Learning:"})," The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."]})]})}),e.jsx("div",{className:"results-grid",children:p.map((t,n)=>{const a=ne.find(d=>d.index===n),o=(a==null?void 0:a.liked)===!0,i=(a==null?void 0:a.liked)===!1,l=Ne.has(n),r=ae.has(n);return e.jsxs("div",{className:`result-card ${o?"liked":""} ${i?"disliked":""} ${l?"pending":""}`,style:{opacity:l?.6:1,pointerEvents:l||r?"none":"auto"},children:[e.jsxs("div",{className:"result-header",children:[e.jsx("h4",{className:"result-name",children:r?o?"Generating new suggestion...":"Generating better suggestion...":t.name}),e.jsxs("div",{className:"result-actions",children:[e.jsxs("div",{className:"feedback-buttons",children:[e.jsx("button",{onClick:()=>we(n,!0),className:`feedback-button like-button ${o?"active":""}`,title:"I like this name",disabled:l||r,children:"👍"}),e.jsx("button",{onClick:()=>we(n,!1),className:`feedback-button dislike-button ${i?"active":""} ${r?"loading":""}`,title:r?"Generating replacement...":"I don't like this name",disabled:l||r,children:r?"🔄":"👎"})]}),e.jsx("button",{onClick:()=>pe(t.name,n),className:"copy-button",title:"Copy podcast name",disabled:l||r,children:K===n?"✓ Copied!":"📋 Copy"})]})]}),e.jsx("p",{className:"result-description",children:r?o?"Added to favorites! Generating a new suggestion...":"Creating a better suggestion based on your preferences...":t.description}),t.suggestedDomain&&!r&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsxs("code",{className:"domain-text",children:[t.suggestedDomain,".com"]}),e.jsxs("span",{className:`domain-status ${t.domainStatus}`,children:[(t.domainStatus==="checking"||ae.has(n))&&"⏳ Checking...",t.domainStatus==="available"&&"✅ Available",t.domainStatus==="taken"&&"❌ Taken",t.domainStatus==="error"&&"⚠️ Check manually"]})]})]},n)})})]}),fe&&!E&&e.jsxs("div",{className:"refinement-section",children:[e.jsx("div",{className:"refinement-info",children:e.jsx("p",{children:"💡 I'll keep your liked names and replace the disliked ones with better suggestions!"})}),e.jsx("button",{onClick:Ce,className:"refinement-button",disabled:E,children:de?"🔄 Refining...":"🎯 Replace Disliked Names"})]})]})]})})}});

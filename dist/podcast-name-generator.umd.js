(function(f,W){typeof exports=="object"&&typeof module<"u"?module.exports=W(require("react")):typeof define=="function"&&define.amd?define(["react"],W):(f=typeof globalThis<"u"?globalThis:f||self,f.PodcastNameGenerator=W(f.React))})(this,function(f){"use strict";var W={exports:{}},Q={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var je;function Ce(){if(je)return Q;je=1;var O=Symbol.for("react.transitional.element"),H=Symbol.for("react.fragment");function y(U,p,N){var D=null;if(N!==void 0&&(D=""+N),p.key!==void 0&&(D=""+p.key),"key"in p){N={};for(var G in p)G!=="key"&&(N[G]=p[G])}else N=p;return p=N.ref,{$$typeof:O,type:U,key:D,ref:p!==void 0?p:null,props:N}}return Q.Fragment=H,Q.jsx=y,Q.jsxs=y,Q}var Z={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ye;function Pe(){return ye||(ye=1,process.env.NODE_ENV!=="production"&&function(){function O(s){if(s==null)return null;if(typeof s=="function")return s.$$typeof===be?null:s.displayName||s.name||null;if(typeof s=="string")return s;switch(s){case C:return"Fragment";case de:return"Profiler";case B:return"StrictMode";case z:return"Suspense";case Ne:return"SuspenseList";case q:return"Activity"}if(typeof s=="object")switch(typeof s.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),s.$$typeof){case se:return"Portal";case ve:return(s.displayName||"Context")+".Provider";case ge:return(s._context.displayName||"Context")+".Consumer";case me:var d=s.render;return s=s.displayName,s||(s=d.displayName||d.name||"",s=s!==""?"ForwardRef("+s+")":"ForwardRef"),s;case ue:return d=s.displayName||null,d!==null?d:O(s.type)||"Memo";case ne:d=s._payload,s=s._init;try{return O(s(d))}catch{}}return null}function H(s){return""+s}function y(s){try{H(s);var d=!1}catch{d=!0}if(d){d=console;var h=d.error,b=typeof Symbol=="function"&&Symbol.toStringTag&&s[Symbol.toStringTag]||s.constructor.name||"Object";return h.call(d,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",b),H(s)}}function U(s){if(s===C)return"<>";if(typeof s=="object"&&s!==null&&s.$$typeof===ne)return"<...>";try{var d=O(s);return d?"<"+d+">":"<...>"}catch{return"<...>"}}function p(){var s=F.A;return s===null?null:s.getOwner()}function N(){return Error("react-stack-top-frame")}function D(s){if(he.call(s,"key")){var d=Object.getOwnPropertyDescriptor(s,"key").get;if(d&&d.isReactWarning)return!1}return s.key!==void 0}function G(s,d){function h(){ae||(ae=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",d))}h.isReactWarning=!0,Object.defineProperty(s,"key",{get:h,configurable:!0})}function T(){var s=O(this.type);return re[s]||(re[s]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),s=this.props.ref,s!==void 0?s:null}function J(s,d,h,b,_,E,M,X){return h=E.ref,s={$$typeof:te,type:s,key:d,props:E,_owner:_},(h!==void 0?h:null)!==null?Object.defineProperty(s,"ref",{enumerable:!1,get:T}):Object.defineProperty(s,"ref",{enumerable:!1,value:null}),s._store={},Object.defineProperty(s._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(s,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(s,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:M}),Object.defineProperty(s,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:X}),Object.freeze&&(Object.freeze(s.props),Object.freeze(s)),s}function ee(s,d,h,b,_,E,M,X){var w=d.children;if(w!==void 0)if(b)if(fe(w)){for(b=0;b<w.length;b++)L(w[b]);Object.freeze&&Object.freeze(w)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else L(w);if(he.call(d,"key")){w=O(s);var Y=Object.keys(d).filter(function(pe){return pe!=="key"});b=0<Y.length?"{key: someKey, "+Y.join(": ..., ")+": ...}":"{key: someKey}",P[w+b]||(Y=0<Y.length?"{"+Y.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,b,w,Y,w),P[w+b]=!0)}if(w=null,h!==void 0&&(y(h),w=""+h),D(d)&&(y(d.key),w=""+d.key),"key"in d){h={};for(var ie in d)ie!=="key"&&(h[ie]=d[ie])}else h=d;return w&&G(h,typeof s=="function"?s.displayName||s.name||"Unknown":s),J(s,w,E,_,p(),h,M,X)}function L(s){typeof s=="object"&&s!==null&&s.$$typeof===te&&s._store&&(s._store.validated=1)}var K=f,te=Symbol.for("react.transitional.element"),se=Symbol.for("react.portal"),C=Symbol.for("react.fragment"),B=Symbol.for("react.strict_mode"),de=Symbol.for("react.profiler"),ge=Symbol.for("react.consumer"),ve=Symbol.for("react.context"),me=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),Ne=Symbol.for("react.suspense_list"),ue=Symbol.for("react.memo"),ne=Symbol.for("react.lazy"),q=Symbol.for("react.activity"),be=Symbol.for("react.client.reference"),F=K.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,he=Object.prototype.hasOwnProperty,fe=Array.isArray,V=console.createTask?console.createTask:function(){return null};K={"react-stack-bottom-frame":function(s){return s()}};var ae,re={},we=K["react-stack-bottom-frame"].bind(K,N)(),oe=V(U(N)),P={};Z.Fragment=C,Z.jsx=function(s,d,h,b,_){var E=1e4>F.recentlyCreatedOwnerStacks++;return ee(s,d,h,!1,b,_,E?Error("react-stack-top-frame"):we,E?V(U(s)):oe)},Z.jsxs=function(s,d,h,b,_){var E=1e4>F.recentlyCreatedOwnerStacks++;return ee(s,d,h,!0,b,_,E?Error("react-stack-top-frame"):we,E?V(U(s)):oe)}}()),Z}process.env.NODE_ENV==="production"?W.exports=Ce():W.exports=Pe();var e=W.exports;return({className:O="",style:H={}})=>{const[y,U]=f.useState(""),[p,N]=f.useState([]),[D,G]=f.useState([]),[T,J]=f.useState(!1),[ee,L]=f.useState(null),[K,te]=f.useState(null),[se,C]=f.useState([]),[B,de]=f.useState({likedNames:[],dislikedNames:[],patterns:{preferredLength:null,preferredStyle:null,likedKeywords:[],dislikedKeywords:[],preferredStructure:null}}),[ge,ve]=f.useState(!1),[me,z]=f.useState(!1),[Ne,ue]=f.useState(!1),[ne,q]=f.useState(new Set),[be,F]=f.useState(new Set),[he,fe]=f.useState(new Set),[V,ae]=f.useState(null),re=t=>{const n=window.scrollY,a=document.documentElement.scrollHeight,i=window.innerHeight,l=a-n-i;t(),requestAnimationFrame(()=>{const o=document.documentElement.scrollHeight,r=window.innerHeight,c=o-l-r;Math.abs(o-a)>5?(window.scrollTo(0,Math.max(0,c)),console.log("📏 Scroll adjusted for height change:",{heightChange:o-a,oldScrollY:n,newScrollY:Math.max(0,c)})):window.scrollTo(0,n)})},[we,oe]=f.useState(0),[P,s]=f.useState(!1),d=100,h=()=>{const t=document.createElement("canvas"),n=t.getContext("2d");n.textBaseline="top",n.font="14px Arial",n.fillText("Usage tracking",2,2);const a=t.toDataURL(),i=navigator.userAgent,l=navigator.language,o=Intl.DateTimeFormat().resolvedOptions().timeZone,r=a+i+l+o;let c=0;for(let m=0;m<r.length;m++){const u=r.charCodeAt(m);c=(c<<5)-c+u,c=c&c}return`podcast_usage_${Math.abs(c)}`},b=()=>{const t=h(),n=new Date().toDateString(),a=`${t}_${n}`,i=localStorage.getItem(a),l=i?parseInt(i,10):0;return oe(l),l>=d?(s(!0),!1):!0},_=(t=1)=>{const n=h(),a=new Date().toDateString(),i=`${n}_${a}`,l=localStorage.getItem(i),r=(l?parseInt(l,10):0)+t;localStorage.setItem(i,r.toString()),oe(r),r>=d&&s(!0)};f.useEffect(()=>{b()},[]);const E=t=>{let n=t.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g," ").replace(/^the\s+/,"").trim();const a=["the","and","for","with","from","show","podcast","cast","of","in","on","at","to","a","an"],i=n.split(" "),l=i.filter(r=>r.length>2&&!a.includes(r));if(l.length===1){const r=l[0];return r.length>=6&&r.length<=15?r:r.length<6?r+"pod":M(r)}if(l.length>=2){const r=l[0],c=l[1],m=r+c;if(m.length>=6&&m.length<=15)return m;const u=M(r),k=M(c),j=u+k;if(j.length>=6&&j.length<=15)return j;if(j.length>15)return u+"-"+k}if(l.length>0){const r=M(l[0]),c=["cast","pod","show","talk"];for(const m of c){const u=r+m;if(u.length>=6&&u.length<=15)return u}if(r.length>=6&&r.length<=15)return r}const o=i[0];return o&&o.length>=3?M(o)+"pod":"podcast"+Math.random().toString(36).substring(2,5)},M=t=>{if(t.length<=8)return t;const n={business:"biz",entrepreneur:"entre",marketing:"market",finance:"fin",startup:"start",leadership:"lead",strategy:"strat",success:"win",growth:"grow",innovation:"innov",management:"manage",technology:"tech",development:"dev",digital:"digi",software:"soft",coding:"code",programming:"prog",stories:"story",journey:"path",adventure:"quest",creative:"create",entertainment:"fun",education:"learn",knowledge:"know",wisdom:"wise",lifestyle:"life",wellness:"well",fitness:"fit",health:"heal",mindset:"mind",motivation:"motive",inspiration:"inspire",american:"usa",european:"euro",international:"global",community:"comm",culture:"cult",society:"social"};if(n[t])return n[t];const a=["super","mega","ultra","micro","mini","multi"];for(const l of a)if(t.startsWith(l)&&t.length>l.length+3){const o=t.substring(l.length);if(o.length<=8)return o}const i=["ing","tion","sion","ness","ment","able","ible"];for(const l of i)if(t.endsWith(l)&&t.length>l.length+4){const o=t.substring(0,t.length-l.length);if(o.length>=4&&o.length<=8)return o}return t.length>8?t.substring(0,8):t},X=async t=>{try{const n=await fetch(`https://dns.google/resolve?name=${t}&type=A`,{method:"GET",headers:{Accept:"application/json"}});if(!n.ok)return"error";const a=await n.json();return a.Answer&&a.Answer.length>0?"taken":"available"}catch(n){return console.warn("Domain check failed:",n),"error"}},w=async t=>{const n=[...t];for(let a=0;a<n.length;a++){const i=E(n[a].name);n[a].suggestedDomain=i,n[a].domainStatus="checking",q(l=>new Set([...l,a]))}N(n);for(let a=0;a<n.length;a++){const i=`${n[a].suggestedDomain}.com`;try{const l=await X(i);N(o=>{const r=[...o];return r[a]&&(r[a].domainStatus=l),r})}catch{N(o=>{const r=[...o];return r[a]&&(r[a].domainStatus="error"),r})}finally{q(l=>{const o=new Set(l);return o.delete(a),o})}}},Y=(t,n)=>{if(t.length===0)return null;const a=t.map(r=>r.name.split(" ").length),i=n.map(r=>r.name.split(" ").length),l=a.reduce((r,c)=>r+c,0)/a.length,o=i.length>0?i.reduce((r,c)=>r+c,0)/i.length:0;return l<=2&&o>2?"short":l<=4&&(o<=2||o>4)?"medium":l>4&&o<=4?"long":l<=2?"short":l<=4?"medium":"long"},ie=(t,n)=>{if(t.length===0)return null;const a=t.map(l=>l.description.toLowerCase()).join(" "),i=n.map(l=>l.description.toLowerCase()).join(" ");return(a.includes("professional")||a.includes("business"))&&!i.includes("professional")&&!i.includes("business")?"professional":(a.includes("creative")||a.includes("unique"))&&!i.includes("creative")&&!i.includes("unique")?"creative":(a.includes("fun")||a.includes("playful"))&&!i.includes("fun")&&!i.includes("playful")?"playful":"descriptive"},pe=t=>{const n=[];t.forEach(i=>{const l=["the","a","an","and","or","but","in","on","at","to","for","of","with","by"],o=i.name.toLowerCase().split(/\s+/).filter(r=>r.length>2&&!l.includes(r));n.push(...o)});const a={};return n.forEach(i=>a[i]=(a[i]||0)+1),Object.entries(a).sort(([,i],[,l])=>l-i).slice(0,5).map(([i])=>i)},ke=t=>{const n=t.filter(i=>i.liked===!0),a=t.filter(i=>i.liked===!1);return{preferredLength:Y(n,a),preferredStyle:ie(n,a),likedKeywords:pe(n),dislikedKeywords:pe(a),preferredStructure:null}},_e=(t,n)=>{const a=[...n.likedNames.map(r=>r.name.toLowerCase()),...n.dislikedNames.map(r=>r.name.toLowerCase()),...p.map(r=>r.name.toLowerCase())],i=`Create 4 unique, high-converting podcast names for: ${t}`;let l=`

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;a.length>0&&(l+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${a.map(r=>`- ${r}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);let o="";if(n.patterns.preferredLength&&(o+=`
Focus on ${{short:"1-2 words (punchy and memorable)",medium:"2-3 words (balanced and brandable)",long:"3-4 words (descriptive but still catchy)"}[n.patterns.preferredLength]}. `),n.patterns.preferredStyle&&(o+=`Use ${{descriptive:"clear, straightforward names that explain the content",creative:"imaginative, metaphorical, or playful names",professional:"authoritative, business-focused names",playful:"fun, energetic, engaging names"}[n.patterns.preferredStyle]||n.patterns.preferredStyle}. `),n.patterns.likedKeywords.length>0&&(o+=`
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `),n.patterns.dislikedKeywords.length>0&&(o+=`
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `),n.likedNames.length>0){const r=n.likedNames.slice(-2).map(c=>c.name).join('", "');o+=`
Generate names with similar appeal to: "${r}" (but completely different concepts). `}return`${i}${l}${o}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`},xe=(t,n,a=1)=>{const i=[...n.likedNames.map(c=>c.name.toLowerCase()),...n.dislikedNames.map(c=>c.name.toLowerCase()),...p.map(c=>c.name.toLowerCase())],l=`Create ${a} unique, high-converting podcast name${a>1?"s":""} for: ${t}`;let o=`

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;i.length>0&&(o+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${i.map(c=>`- ${c}`).join(`
`)}
Do not create names similar to any of the above.`);let r="";return n.patterns.likedKeywords.length>0&&(r+=`
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `),n.patterns.dislikedKeywords.length>0&&(r+=`
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `),`${l}${o}${r}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${a>1?', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}':""}]}`},Se=async(t=!1)=>{if(!y.trim()){L("Please describe what your podcast is about");return}if(!b()){L(null);return}J(!0),L(null),N([]),t?z(!0):(C([]),ve(!1),z(!1),ue(!1));try{const n=t?_e(y,B):`Create 4 unique, high-converting podcast names for: ${y}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`,a=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:n}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!a.ok)throw new Error(`API request failed: ${a.status} ${a.statusText}`);const i=await a.json();if(!i.candidates||!i.candidates[0]||!i.candidates[0].content)throw new Error("Invalid response format from API");const o=i.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!o)throw new Error("No valid JSON found in API response");const r=JSON.parse(o[0]);if(!r.podcast_names||!Array.isArray(r.podcast_names))throw new Error("Invalid response structure");N(r.podcast_names),_(4),w(r.podcast_names);const c=r.podcast_names.map((m,u)=>({name:m.name,description:m.description,liked:null,timestamp:Date.now(),index:u}));C(c)}catch(n){console.error("Error generating podcast names:",n),L(n instanceof Error?n.message:"An unexpected error occurred")}finally{J(!1),z(!1)}},Re=async(t,n)=>{try{await navigator.clipboard.writeText(t),te(n),setTimeout(()=>te(null),2e3)}catch(a){console.error("Failed to copy text:",a)}},Ee=async(t,n)=>{const a=p[t];if(a){if(n){const i=window.scrollY,l=document.documentElement.scrollHeight,o=window.innerHeight,r=l-i-o;console.log("📏 Before adding favorite:",{scrollY:i,documentHeight:l,scrollFromBottom:r}),fe(c=>new Set([...c,t])),re(()=>{ae(`"${a.name}" added to favorites!`)}),setTimeout(()=>{re(()=>{ae(null)})},2e3),setTimeout(()=>{G(c=>c.find(m=>m.name===a.name)?c:[...c,a]),setTimeout(()=>{const c=document.documentElement.scrollHeight,m=window.innerHeight,u=c-l,k=c-r-m;console.log("📏 After adding favorite:",{afterDocumentHeight:c,heightDifference:u,newScrollY:k,scrollFromBottom:r}),window.scrollTo(0,Math.max(0,k))},0)},100),setTimeout(()=>{fe(c=>{const m=new Set(c);return m.delete(t),m})},700),de(c=>{const m={...c};return m.dislikedNames=m.dislikedNames.filter(u=>u.name!==a.name),m.likedNames.find(u=>u.name===a.name)||m.likedNames.push({name:a.name,description:a.description,liked:!0,timestamp:Date.now(),index:t}),m.patterns=ke([...m.likedNames,...m.dislikedNames]),m}),F(c=>new Set([...c,t]))}else{const i=window.scrollY;console.log("👎 Disliking item, maintaining scroll at:",i),F(l=>new Set([...l,t])),de(l=>{const o={...l};return o.likedNames=o.likedNames.filter(r=>r.name!==a.name),o.dislikedNames.find(r=>r.name===a.name)||o.dislikedNames.push({name:a.name,description:a.description,liked:!1,timestamp:Date.now(),index:t}),o.patterns=ke([...o.likedNames,...o.dislikedNames]),o}),setTimeout(()=>{window.scrollTo(0,i)},0)}y.trim()&&Oe(t),Ne||ue(!0)}},Oe=async t=>{var n,a,i,l,o,r;if(b()){console.log(`🔄 Starting replacement generation for index ${t}`);try{const c=xe(y,B,1);console.log(`📝 Generated single name prompt for index ${t}:`,c.substring(0,100)+"..."),console.log(`🌐 Making API call for index ${t}...`);const m=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:c}]}]})});if(console.log(`📡 API response status for index ${t}:`,m.status),!m.ok)throw new Error(`Failed to generate replacement suggestion: ${m.status} ${m.statusText}`);const u=await m.json();console.log(`📦 API response data for index ${t}:`,u);const k=(o=(l=(i=(a=(n=u.candidates)==null?void 0:n[0])==null?void 0:a.content)==null?void 0:i.parts)==null?void 0:l[0])==null?void 0:o.text;if(!k)throw new Error("No content in API response");console.log(`📄 API content for index ${t}:`,k.substring(0,200)+"...");const j=k.match(/\{[\s\S]*\}/);if(!j)throw console.error(`❌ No valid JSON found in response for index ${t}:`,k),new Error("No valid JSON found in response");const le=JSON.parse(j[0]);console.log(`🔍 Parsed response for index ${t}:`,le);const A=(r=le.podcast_names)==null?void 0:r[0];if(A){if(console.log(`✅ New name generated for index ${t}:`,A),N(I=>{const x=[...I];return x[t]={name:A.name,description:A.description,suggestedDomain:A.suggestedDomain,domainStatus:"checking"},console.log(`🔄 Updated results for index ${t}:`,x[t]),x}),F(I=>{const x=new Set(I);return x.delete(t),console.log(`🗑️ Removed index ${t} from pending replacements. Remaining:`,Array.from(x)),x}),C(I=>{const x=I.filter(g=>g.index!==t);return console.log(`🧹 Cleared feedback for index ${t}. Remaining feedback:`,x),x}),A.suggestedDomain){console.log(`🌐 Checking domain availability for ${A.suggestedDomain}...`),q(x=>new Set([...x,t]));const I=await X(A.suggestedDomain);console.log(`🏷️ Domain status for ${A.suggestedDomain}:`,I),N(x=>{const g=[...x];return g[t]&&(g[t].domainStatus=I),g}),q(x=>{const g=new Set(x);return g.delete(t),g})}console.log(`🎉 Successfully completed replacement for index ${t}`)}else throw console.error(`❌ No new name found in parsed response for index ${t}:`,le),new Error("No new name found in API response")}catch(c){console.error(`❌ Error generating replacement suggestion for index ${t}:`,c),F(m=>{const u=new Set(m);return u.delete(t),console.log(`🗑️ Removed index ${t} from pending on error. Remaining:`,Array.from(u)),u}),C(m=>{const u=m.filter(k=>k.index!==t);return console.log(`🧹 Cleared feedback for index ${t} on error. Remaining:`,u),u})}}},De=()=>{y.trim()&&Le(y)},Le=async t=>{var n,a,i,l,o,r,c,m,u,k;J(!0),L(""),z(!0);try{const j=p.filter((g,R)=>{const v=se.find(S=>S.index===R);return(v==null?void 0:v.liked)===!0}),le=p.filter((g,R)=>{const v=se.find(S=>S.index===R);return(v==null?void 0:v.liked)===!1}).length,A=Math.max(1,le),x=Math.min(5,j.length+A)-j.length;if(x<=0){const g=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:xe(t,B,1)}]}]})});if(!g.ok)throw new Error(`API request failed: ${g.status}`);const v=(o=(l=(i=(a=(n=(await g.json()).candidates)==null?void 0:n[0])==null?void 0:a.content)==null?void 0:i.parts)==null?void 0:l[0])==null?void 0:o.text;if(!v)throw new Error("No content received from API");const S=v.match(/\{[\s\S]*\}/);if(!S)throw new Error("No valid JSON found in response");const $=JSON.parse(S[0]);if(!$.podcast_names||!Array.isArray($.podcast_names))throw new Error("Invalid response format");const ce=[...j,...$.podcast_names].slice(0,5);N(ce),w(ce)}else{const g=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:xe(t,B,x)}]}]})});if(!g.ok)throw new Error(`API request failed: ${g.status}`);const v=(k=(u=(m=(c=(r=(await g.json()).candidates)==null?void 0:r[0])==null?void 0:c.content)==null?void 0:m.parts)==null?void 0:u[0])==null?void 0:k.text;if(!v)throw new Error("No content received from API");const S=v.match(/\{[\s\S]*\}/);if(!S)throw new Error("No valid JSON found in response");const $=JSON.parse(S[0]);if(!$.podcast_names||!Array.isArray($.podcast_names))throw new Error("Invalid response format");const ce=[...j,...$.podcast_names];N(ce),w(ce)}C(g=>g.filter(R=>{const v=p[R.index];return j.some(S=>S.name===(v==null?void 0:v.name))})),C(g=>g.map(R=>{const v=p[R.index],S=j.findIndex($=>$.name===(v==null?void 0:v.name));return S>=0?{...R,index:S}:R}).filter(R=>R.index>=0))}catch(j){console.error("Error generating refined names:",j),L(j instanceof Error?j.message:"Failed to generate refined names. Please try again.")}finally{J(!1),z(!1)}},Te=t=>{t.preventDefault(),Se()},Ae=t=>{t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),Se())};return e.jsx("div",{className:`podcast-name-generator ${O}`,style:H,children:e.jsxs("div",{className:"generator-container",children:[e.jsxs("div",{className:"header-section",children:[e.jsx("h1",{className:"main-title",children:"Free Podcast Name Generator"}),e.jsx("h2",{className:"main-subtitle",children:"Create the Perfect Name for Your Podcast in Seconds"})]}),e.jsxs("div",{className:"benefits-section",children:[e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"100% Free Forever"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"No Sign-up Required"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"Instant Results"})]})]}),P&&e.jsx("div",{className:"limit-reached-banner",children:e.jsxs("div",{className:"limit-content",children:[e.jsx("span",{className:"limit-icon",children:"⚠️"}),e.jsx("div",{className:"limit-text",children:e.jsx("p",{children:"You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below."})})]})}),p.length===0&&e.jsx("div",{className:"initial-input-section",children:e.jsx("form",{onSubmit:Te,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:y,onChange:t=>U(t.target.value),onKeyPress:Ae,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:T}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:T||!y.trim()||P,className:`generate-button ${P?"disabled":""}`,children:T?"Generating...":P?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star star-partial",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})}),ee&&e.jsxs("div",{className:"error-message",children:[e.jsx("span",{className:"error-icon",children:"⚠️"}),ee]}),V&&e.jsxs("div",{className:"success-message",children:[e.jsx("span",{className:"success-icon",children:"✨"}),V]}),T&&e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:me?"Generating better names based on your preferences...":"Generating creative podcast names..."})]}),p.length>0&&e.jsxs("div",{className:"results-container",children:[D.length>0&&e.jsxs("div",{className:"favorites-section",children:[e.jsxs("div",{className:"favorites-header",children:[e.jsxs("h3",{children:["🏆 Your Winning Podcast Names (",D.length,")"]}),e.jsx("p",{className:"favorites-subtitle",children:"Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!"})]}),e.jsx("div",{className:"favorites-grid",children:D.map((t,n)=>e.jsxs("div",{className:"favorite-card",children:[e.jsxs("div",{className:"favorite-content",children:[e.jsx("h4",{className:"favorite-name",children:t.name}),e.jsx("p",{className:"favorite-description",children:t.description}),t.suggestedDomain&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsx("span",{className:"domain-name",children:t.suggestedDomain}),e.jsx("span",{className:`domain-status ${t.domainStatus}`,children:t.domainStatus==="available"?"✅ Available":t.domainStatus==="taken"?"❌ Taken":t.domainStatus==="error"?"⚠️ Check manually":"🔍 Checking..."})]})]}),e.jsx("div",{className:"favorite-actions",children:e.jsx("button",{onClick:()=>Re(t.name,-1),className:"copy-button small",title:"Copy to clipboard",children:"📋 Copy"})})]},`fav-${n}`))})]}),e.jsxs("div",{className:"input-section-simple",children:[e.jsx("div",{className:"input-help-message-simple",children:e.jsxs("p",{className:"input-sub-description",children:["💡 Want different suggestions? Update your description below - ",e.jsx("strong",{children:"your favorites will stay safe!"})]})}),e.jsx("form",{onSubmit:Te,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:y,onChange:t=>U(t.target.value),onKeyPress:Ae,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:T}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:T||!y.trim()||P,className:`generate-button ${P?"disabled":""}`,children:T?"Generating...":P?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star star-partial",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})]}),e.jsxs("div",{className:"suggestions-section",children:[e.jsx("div",{className:"suggestions-header",children:e.jsx("h3",{children:"🎯 Current Suggestions"})}),e.jsx("div",{className:"onboarding-banner",children:e.jsxs("div",{className:"onboarding-content",children:[e.jsx("span",{className:"onboarding-icon",children:"💡"}),e.jsxs("div",{className:"onboarding-text",children:[e.jsx("strong",{children:"Smart AI Learning:"})," The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."]})]})}),e.jsx("div",{className:"results-grid",children:p.map((t,n)=>{const a=se.find(c=>c.index===n),i=(a==null?void 0:a.liked)===!0,l=(a==null?void 0:a.liked)===!1,o=be.has(n),r=ne.has(n);return e.jsxs("div",{className:`result-card ${i?"liked":""} ${l?"disliked":""} ${o?"pending":""} ${he.has(n)?"flying-to-favorites":""}`,style:{opacity:o?.6:1,pointerEvents:o?"none":"auto"},children:[e.jsxs("div",{className:"result-header",children:[e.jsx("h4",{className:"result-name",children:o?i?"Generating new suggestion...":"Generating better suggestion...":t.name}),e.jsxs("div",{className:"result-actions",children:[e.jsxs("div",{className:"feedback-buttons",children:[e.jsx("button",{onClick:()=>Ee(n,!0),className:`feedback-button like-button ${i?"active":""}`,title:"I like this name",disabled:o,children:"👍"}),e.jsx("button",{onClick:()=>Ee(n,!1),className:`feedback-button dislike-button ${l?"active":""} ${o?"loading":""}`,title:o?"Generating replacement...":"I don't like this name",disabled:o,children:o?"🔄":"👎"})]}),e.jsx("button",{onClick:()=>Re(t.name,n),className:"copy-button",title:"Copy podcast name",disabled:o,children:K===n?"✓ Copied!":"📋 Copy"})]})]}),e.jsx("p",{className:"result-description",children:o?i?"Added to favorites! Generating a new suggestion...":"Creating a better suggestion based on your preferences...":t.description}),t.suggestedDomain&&!r&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsxs("code",{className:"domain-text",children:[t.suggestedDomain,".com"]}),e.jsxs("span",{className:`domain-status ${t.domainStatus}`,children:[(t.domainStatus==="checking"||ne.has(n))&&"⏳ Checking...",t.domainStatus==="available"&&"✅ Available",t.domainStatus==="taken"&&"❌ Taken",t.domainStatus==="error"&&"⚠️ Check manually"]})]})]},n)})})]}),ge&&!T&&e.jsxs("div",{className:"refinement-section",children:[e.jsx("div",{className:"refinement-info",children:e.jsx("p",{children:"💡 I'll keep your liked names and replace the disliked ones with better suggestions!"})}),e.jsx("button",{onClick:De,className:"refinement-button",disabled:T,children:me?"🔄 Refining...":"🎯 Replace Disliked Names"})]})]})]})})}});

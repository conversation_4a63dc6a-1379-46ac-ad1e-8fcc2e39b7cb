(function(f,G){typeof exports=="object"&&typeof module<"u"?module.exports=G(require("react")):typeof define=="function"&&define.amd?define(["react"],G):(f=typeof globalThis<"u"?globalThis:f||self,f.PodcastNameGenerator=G(f.React))})(this,function(f){"use strict";var G={exports:{}},Z={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var je;function Ce(){if(je)return Z;je=1;var D=Symbol.for("react.transitional.element"),q=Symbol.for("react.fragment");function k(U,g,N){var L=null;if(N!==void 0&&(L=""+N),g.key!==void 0&&(L=""+g.key),"key"in g){N={};for(var H in g)H!=="key"&&(N[H]=g[H])}else N=g;return g=N.ref,{$$typeof:D,type:U,key:L,ref:g!==void 0?g:null,props:N}}return Z.Fragment=q,Z.jsx=k,Z.jsxs=k,Z}var ee={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ye;function Pe(){return ye||(ye=1,process.env.NODE_ENV!=="production"&&function(){function D(s){if(s==null)return null;if(typeof s=="function")return s.$$typeof===be?null:s.displayName||s.name||null;if(typeof s=="string")return s;switch(s){case P:return"Fragment";case de:return"Profiler";case B:return"StrictMode";case z:return"Suspense";case Ne:return"SuspenseList";case V:return"Activity"}if(typeof s=="object")switch(typeof s.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),s.$$typeof){case ne:return"Portal";case ve:return(s.displayName||"Context")+".Provider";case pe:return(s._context.displayName||"Context")+".Consumer";case me:var d=s.render;return s=s.displayName,s||(s=d.displayName||d.name||"",s=s!==""?"ForwardRef("+s+")":"ForwardRef"),s;case ue:return d=s.displayName||null,d!==null?d:D(s.type)||"Memo";case ae:d=s._payload,s=s._init;try{return D(s(d))}catch{}}return null}function q(s){return""+s}function k(s){try{q(s);var d=!1}catch{d=!0}if(d){d=console;var h=d.error,b=typeof Symbol=="function"&&Symbol.toStringTag&&s[Symbol.toStringTag]||s.constructor.name||"Object";return h.call(d,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",b),q(s)}}function U(s){if(s===P)return"<>";if(typeof s=="object"&&s!==null&&s.$$typeof===ae)return"<...>";try{var d=D(s);return d?"<"+d+">":"<...>"}catch{return"<...>"}}function g(){var s=F.A;return s===null?null:s.getOwner()}function N(){return Error("react-stack-top-frame")}function L(s){if(he.call(s,"key")){var d=Object.getOwnPropertyDescriptor(s,"key").get;if(d&&d.isReactWarning)return!1}return s.key!==void 0}function H(s,d){function h(){re||(re=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",d))}h.isReactWarning=!0,Object.defineProperty(s,"key",{get:h,configurable:!0})}function C(){var s=D(this.type);return oe[s]||(oe[s]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),s=this.props.ref,s!==void 0?s:null}function J(s,d,h,b,O,T,M,Q){return h=T.ref,s={$$typeof:se,type:s,key:d,props:T,_owner:O},(h!==void 0?h:null)!==null?Object.defineProperty(s,"ref",{enumerable:!1,get:C}):Object.defineProperty(s,"ref",{enumerable:!1,value:null}),s._store={},Object.defineProperty(s._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(s,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(s,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:M}),Object.defineProperty(s,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:Q}),Object.freeze&&(Object.freeze(s.props),Object.freeze(s)),s}function te(s,d,h,b,O,T,M,Q){var w=d.children;if(w!==void 0)if(b)if(fe(w)){for(b=0;b<w.length;b++)I(w[b]);Object.freeze&&Object.freeze(w)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else I(w);if(he.call(d,"key")){w=D(s);var Y=Object.keys(d).filter(function(ge){return ge!=="key"});b=0<Y.length?"{key: someKey, "+Y.join(": ..., ")+": ...}":"{key: someKey}",_[w+b]||(Y=0<Y.length?"{"+Y.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,b,w,Y,w),_[w+b]=!0)}if(w=null,h!==void 0&&(k(h),w=""+h),L(d)&&(k(d.key),w=""+d.key),"key"in d){h={};for(var le in d)le!=="key"&&(h[le]=d[le])}else h=d;return w&&H(h,typeof s=="function"?s.displayName||s.name||"Unknown":s),J(s,w,T,O,g(),h,M,Q)}function I(s){typeof s=="object"&&s!==null&&s.$$typeof===se&&s._store&&(s._store.validated=1)}var K=f,se=Symbol.for("react.transitional.element"),ne=Symbol.for("react.portal"),P=Symbol.for("react.fragment"),B=Symbol.for("react.strict_mode"),de=Symbol.for("react.profiler"),pe=Symbol.for("react.consumer"),ve=Symbol.for("react.context"),me=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),Ne=Symbol.for("react.suspense_list"),ue=Symbol.for("react.memo"),ae=Symbol.for("react.lazy"),V=Symbol.for("react.activity"),be=Symbol.for("react.client.reference"),F=K.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,he=Object.prototype.hasOwnProperty,fe=Array.isArray,X=console.createTask?console.createTask:function(){return null};K={"react-stack-bottom-frame":function(s){return s()}};var re,oe={},we=K["react-stack-bottom-frame"].bind(K,N)(),ie=X(U(N)),_={};ee.Fragment=P,ee.jsx=function(s,d,h,b,O){var T=1e4>F.recentlyCreatedOwnerStacks++;return te(s,d,h,!1,b,O,T?Error("react-stack-top-frame"):we,T?X(U(s)):ie)},ee.jsxs=function(s,d,h,b,O){var T=1e4>F.recentlyCreatedOwnerStacks++;return te(s,d,h,!0,b,O,T?Error("react-stack-top-frame"):we,T?X(U(s)):ie)}}()),ee}process.env.NODE_ENV==="production"?G.exports=Ce():G.exports=Pe();var e=G.exports;return({className:D="",style:q={}})=>{const[k,U]=f.useState(""),[g,N]=f.useState([]),[L,H]=f.useState([]),[C,J]=f.useState(!1),[te,I]=f.useState(null),[K,se]=f.useState(null),[ne,P]=f.useState([]),[B,de]=f.useState({likedNames:[],dislikedNames:[],patterns:{preferredLength:null,preferredStyle:null,likedKeywords:[],dislikedKeywords:[],preferredStructure:null}}),[pe,ve]=f.useState(!1),[me,z]=f.useState(!1),[Ne,ue]=f.useState(!1),[ae,V]=f.useState(new Set),[be,F]=f.useState(new Set),[he,fe]=f.useState(new Set),[X,re]=f.useState(null),oe=t=>{const a=window.scrollY,n=document.documentElement.scrollHeight,i=window.innerHeight,l=n-a-i;t(),requestAnimationFrame(()=>{const o=document.documentElement.scrollHeight,r=window.innerHeight,c=o-l-r;Math.abs(o-n)>5?(window.scrollTo(0,Math.max(0,c)),console.log("📏 Scroll adjusted for height change:",{heightChange:o-n,oldScrollY:a,newScrollY:Math.max(0,c)})):window.scrollTo(0,a)})},[we,ie]=f.useState(0),[_,s]=f.useState(!1),d=100,h=()=>{const t=document.createElement("canvas"),a=t.getContext("2d");a.textBaseline="top",a.font="14px Arial",a.fillText("Usage tracking",2,2);const n=t.toDataURL(),i=navigator.userAgent,l=navigator.language,o=Intl.DateTimeFormat().resolvedOptions().timeZone,r=n+i+l+o;let c=0;for(let m=0;m<r.length;m++){const u=r.charCodeAt(m);c=(c<<5)-c+u,c=c&c}return`podcast_usage_${Math.abs(c)}`},b=()=>{const t=h(),a=new Date().toDateString(),n=`${t}_${a}`,i=localStorage.getItem(n),l=i?parseInt(i,10):0;return ie(l),l>=d?(s(!0),!1):!0},O=(t=1)=>{const a=h(),n=new Date().toDateString(),i=`${a}_${n}`,l=localStorage.getItem(i),r=(l?parseInt(l,10):0)+t;localStorage.setItem(i,r.toString()),ie(r),r>=d&&s(!0)};f.useEffect(()=>{b()},[]);const T=t=>{let a=t.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g," ").replace(/^the\s+/,"").trim();const n=["the","and","for","with","from","show","podcast","cast","of","in","on","at","to","a","an"],i=a.split(" "),l=i.filter(r=>r.length>2&&!n.includes(r));if(l.length===1){const r=l[0];return r.length>=6&&r.length<=15?r:r.length<6?r+"pod":M(r)}if(l.length>=2){const r=l[0],c=l[1],m=r+c;if(m.length>=6&&m.length<=15)return m;const u=M(r),y=M(c),x=u+y;if(x.length>=6&&x.length<=15)return x;if(x.length>15)return u+"-"+y}if(l.length>0){const r=M(l[0]),c=["cast","pod","show","talk"];for(const m of c){const u=r+m;if(u.length>=6&&u.length<=15)return u}if(r.length>=6&&r.length<=15)return r}const o=i[0];return o&&o.length>=3?M(o)+"pod":"podcast"+Math.random().toString(36).substring(2,5)},M=t=>{if(t.length<=8)return t;const a={business:"biz",entrepreneur:"entre",marketing:"market",finance:"fin",startup:"start",leadership:"lead",strategy:"strat",success:"win",growth:"grow",innovation:"innov",management:"manage",technology:"tech",development:"dev",digital:"digi",software:"soft",coding:"code",programming:"prog",stories:"story",journey:"path",adventure:"quest",creative:"create",entertainment:"fun",education:"learn",knowledge:"know",wisdom:"wise",lifestyle:"life",wellness:"well",fitness:"fit",health:"heal",mindset:"mind",motivation:"motive",inspiration:"inspire",american:"usa",european:"euro",international:"global",community:"comm",culture:"cult",society:"social"};if(a[t])return a[t];const n=["super","mega","ultra","micro","mini","multi"];for(const l of n)if(t.startsWith(l)&&t.length>l.length+3){const o=t.substring(l.length);if(o.length<=8)return o}const i=["ing","tion","sion","ness","ment","able","ible"];for(const l of i)if(t.endsWith(l)&&t.length>l.length+4){const o=t.substring(0,t.length-l.length);if(o.length>=4&&o.length<=8)return o}return t.length>8?t.substring(0,8):t},Q=async t=>{try{const a=await fetch(`https://dns.google/resolve?name=${t}&type=A`,{method:"GET",headers:{Accept:"application/json"}});if(!a.ok)return"error";const n=await a.json();return n.Answer&&n.Answer.length>0?"taken":"available"}catch(a){return console.warn("Domain check failed:",a),"error"}},w=async t=>{const a=[...t];for(let n=0;n<a.length;n++){const i=T(a[n].name);a[n].suggestedDomain=i,a[n].domainStatus="checking",V(l=>new Set([...l,n]))}N(a);for(let n=0;n<a.length;n++){const i=`${a[n].suggestedDomain}.com`;try{const l=await Q(i);N(o=>{const r=[...o];return r[n]&&(r[n].domainStatus=l),r})}catch{N(o=>{const r=[...o];return r[n]&&(r[n].domainStatus="error"),r})}finally{V(l=>{const o=new Set(l);return o.delete(n),o})}}},Y=(t,a)=>{if(t.length===0)return null;const n=t.map(r=>r.name.split(" ").length),i=a.map(r=>r.name.split(" ").length),l=n.reduce((r,c)=>r+c,0)/n.length,o=i.length>0?i.reduce((r,c)=>r+c,0)/i.length:0;return l<=2&&o>2?"short":l<=4&&(o<=2||o>4)?"medium":l>4&&o<=4?"long":l<=2?"short":l<=4?"medium":"long"},le=(t,a)=>{if(t.length===0)return null;const n=t.map(l=>l.description.toLowerCase()).join(" "),i=a.map(l=>l.description.toLowerCase()).join(" ");return(n.includes("professional")||n.includes("business"))&&!i.includes("professional")&&!i.includes("business")?"professional":(n.includes("creative")||n.includes("unique"))&&!i.includes("creative")&&!i.includes("unique")?"creative":(n.includes("fun")||n.includes("playful"))&&!i.includes("fun")&&!i.includes("playful")?"playful":"descriptive"},ge=t=>{const a=[];t.forEach(i=>{const l=["the","a","an","and","or","but","in","on","at","to","for","of","with","by"],o=i.name.toLowerCase().split(/\s+/).filter(r=>r.length>2&&!l.includes(r));a.push(...o)});const n={};return a.forEach(i=>n[i]=(n[i]||0)+1),Object.entries(n).sort(([,i],[,l])=>l-i).slice(0,5).map(([i])=>i)},ke=t=>{const a=t.filter(i=>i.liked===!0),n=t.filter(i=>i.liked===!1);return{preferredLength:Y(a,n),preferredStyle:le(a,n),likedKeywords:ge(a),dislikedKeywords:ge(n),preferredStructure:null}},_e=(t,a)=>{const n=[...a.likedNames.map(r=>r.name.toLowerCase()),...a.dislikedNames.map(r=>r.name.toLowerCase()),...g.map(r=>r.name.toLowerCase())],i=`Create 4 unique, high-converting podcast names for: ${t}`;let l=`

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;n.length>0&&(l+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${n.map(r=>`- ${r}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);let o="";if(a.patterns.preferredLength&&(o+=`
Focus on ${{short:"1-2 words (punchy and memorable)",medium:"2-3 words (balanced and brandable)",long:"3-4 words (descriptive but still catchy)"}[a.patterns.preferredLength]}. `),a.patterns.preferredStyle&&(o+=`Use ${{descriptive:"clear, straightforward names that explain the content",creative:"imaginative, metaphorical, or playful names",professional:"authoritative, business-focused names",playful:"fun, energetic, engaging names"}[a.patterns.preferredStyle]||a.patterns.preferredStyle}. `),a.patterns.likedKeywords.length>0&&(o+=`
Incorporate themes similar to: ${a.patterns.likedKeywords.join(", ")}. `),a.patterns.dislikedKeywords.length>0&&(o+=`
Avoid themes like: ${a.patterns.dislikedKeywords.join(", ")}. `),a.likedNames.length>0){const r=a.likedNames.slice(-2).map(c=>c.name).join('", "');o+=`
Generate names with similar appeal to: "${r}" (but completely different concepts). `}return`${i}${l}${o}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`},xe=(t,a,n=1)=>{const i=[...a.likedNames.map(c=>c.name.toLowerCase()),...a.dislikedNames.map(c=>c.name.toLowerCase()),...g.map(c=>c.name.toLowerCase())],l=`Create ${n} unique, high-converting podcast name${n>1?"s":""} for: ${t}`;let o=`

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;i.length>0&&(o+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${i.map(c=>`- ${c}`).join(`
`)}
Do not create names similar to any of the above.`);let r="";return a.patterns.likedKeywords.length>0&&(r+=`
Incorporate themes similar to: ${a.patterns.likedKeywords.join(", ")}. `),a.patterns.dislikedKeywords.length>0&&(r+=`
Avoid themes like: ${a.patterns.dislikedKeywords.join(", ")}. `),`${l}${o}${r}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${n>1?', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}':""}]}`},Se=async(t=!1)=>{if(!k.trim()){I("Please describe what your podcast is about");return}if(!b()){I(null);return}J(!0),I(null),N([]),t?z(!0):(P([]),ve(!1),z(!1),ue(!1));try{const a=t?_e(k,B):`Create 4 unique, high-converting podcast names for: ${k}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`,n=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:a}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!n.ok)throw new Error(`API request failed: ${n.status} ${n.statusText}`);const i=await n.json();if(!i.candidates||!i.candidates[0]||!i.candidates[0].content)throw new Error("Invalid response format from API");const o=i.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!o)throw new Error("No valid JSON found in API response");const r=JSON.parse(o[0]);if(!r.podcast_names||!Array.isArray(r.podcast_names))throw new Error("Invalid response structure");N(r.podcast_names),O(4),w(r.podcast_names);const c=r.podcast_names.map((m,u)=>({name:m.name,description:m.description,liked:null,timestamp:Date.now(),index:u}));P(c)}catch(a){console.error("Error generating podcast names:",a),I(a instanceof Error?a.message:"An unexpected error occurred")}finally{J(!1),z(!1)}},Re=async(t,a)=>{try{await navigator.clipboard.writeText(t),se(a),setTimeout(()=>se(null),2e3)}catch(n){console.error("Failed to copy text:",n)}},Ee=async(t,a)=>{const n=g[t];if(n){if(a){const i=window.scrollY,l=document.documentElement.scrollHeight,o=window.innerHeight,r=l-i-o;console.log("📏 Before adding favorite:",{scrollY:i,documentHeight:l,scrollFromBottom:r}),fe(c=>new Set([...c,t])),oe(()=>{re(`"${n.name}" added to favorites!`)}),setTimeout(()=>{oe(()=>{re(null)})},2e3),setTimeout(()=>{const c=document.querySelector(".favorites-section"),m=c?c.scrollHeight:0;H(u=>u.find(y=>y.name===n.name)?u:[...u,n]),requestAnimationFrame(()=>{requestAnimationFrame(()=>{const u=c?c.scrollHeight:0,y=u-m,W=document.documentElement.scrollHeight-l,S=y>0?y:W,A=i+S;console.log("📏 After adding favorite:",{beforeFavoritesHeight:m,afterFavoritesHeight:u,actualHeightIncrease:y,documentHeightIncrease:W,heightIncrease:S,beforeScrollY:i,newScrollY:A,cardName:n.name}),window.scrollTo(0,Math.max(0,A))})})},100),setTimeout(()=>{fe(c=>{const m=new Set(c);return m.delete(t),m})},700),de(c=>{const m={...c};return m.dislikedNames=m.dislikedNames.filter(u=>u.name!==n.name),m.likedNames.find(u=>u.name===n.name)||m.likedNames.push({name:n.name,description:n.description,liked:!0,timestamp:Date.now(),index:t}),m.patterns=ke([...m.likedNames,...m.dislikedNames]),m}),F(c=>new Set([...c,t]))}else{const i=window.scrollY;console.log("👎 Disliking item, maintaining scroll at:",i),F(l=>new Set([...l,t])),de(l=>{const o={...l};return o.likedNames=o.likedNames.filter(r=>r.name!==n.name),o.dislikedNames.find(r=>r.name===n.name)||o.dislikedNames.push({name:n.name,description:n.description,liked:!1,timestamp:Date.now(),index:t}),o.patterns=ke([...o.likedNames,...o.dislikedNames]),o}),setTimeout(()=>{window.scrollTo(0,i)},0)}k.trim()&&Oe(t),Ne||ue(!0)}},Oe=async t=>{var a,n,i,l,o,r;if(b()){console.log(`🔄 Starting replacement generation for index ${t}`);try{const c=xe(k,B,1);console.log(`📝 Generated single name prompt for index ${t}:`,c.substring(0,100)+"..."),console.log(`🌐 Making API call for index ${t}...`);const m=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:c}]}]})});if(console.log(`📡 API response status for index ${t}:`,m.status),!m.ok)throw new Error(`Failed to generate replacement suggestion: ${m.status} ${m.statusText}`);const u=await m.json();console.log(`📦 API response data for index ${t}:`,u);const y=(o=(l=(i=(n=(a=u.candidates)==null?void 0:a[0])==null?void 0:n.content)==null?void 0:i.parts)==null?void 0:l[0])==null?void 0:o.text;if(!y)throw new Error("No content in API response");console.log(`📄 API content for index ${t}:`,y.substring(0,200)+"...");const x=y.match(/\{[\s\S]*\}/);if(!x)throw console.error(`❌ No valid JSON found in response for index ${t}:`,y),new Error("No valid JSON found in response");const W=JSON.parse(x[0]);console.log(`🔍 Parsed response for index ${t}:`,W);const S=(r=W.podcast_names)==null?void 0:r[0];if(S){if(console.log(`✅ New name generated for index ${t}:`,S),N(A=>{const j=[...A];return j[t]={name:S.name,description:S.description,suggestedDomain:S.suggestedDomain,domainStatus:"checking"},console.log(`🔄 Updated results for index ${t}:`,j[t]),j}),F(A=>{const j=new Set(A);return j.delete(t),console.log(`🗑️ Removed index ${t} from pending replacements. Remaining:`,Array.from(j)),j}),P(A=>{const j=A.filter(p=>p.index!==t);return console.log(`🧹 Cleared feedback for index ${t}. Remaining feedback:`,j),j}),S.suggestedDomain){console.log(`🌐 Checking domain availability for ${S.suggestedDomain}...`),V(j=>new Set([...j,t]));const A=await Q(S.suggestedDomain);console.log(`🏷️ Domain status for ${S.suggestedDomain}:`,A),N(j=>{const p=[...j];return p[t]&&(p[t].domainStatus=A),p}),V(j=>{const p=new Set(j);return p.delete(t),p})}console.log(`🎉 Successfully completed replacement for index ${t}`)}else throw console.error(`❌ No new name found in parsed response for index ${t}:`,W),new Error("No new name found in API response")}catch(c){console.error(`❌ Error generating replacement suggestion for index ${t}:`,c),F(m=>{const u=new Set(m);return u.delete(t),console.log(`🗑️ Removed index ${t} from pending on error. Remaining:`,Array.from(u)),u}),P(m=>{const u=m.filter(y=>y.index!==t);return console.log(`🧹 Cleared feedback for index ${t} on error. Remaining:`,u),u})}}},De=()=>{k.trim()&&Le(k)},Le=async t=>{var a,n,i,l,o,r,c,m,u,y;J(!0),I(""),z(!0);try{const x=g.filter((p,E)=>{const v=ne.find(R=>R.index===E);return(v==null?void 0:v.liked)===!0}),W=g.filter((p,E)=>{const v=ne.find(R=>R.index===E);return(v==null?void 0:v.liked)===!1}).length,S=Math.max(1,W),j=Math.min(5,x.length+S)-x.length;if(j<=0){const p=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:xe(t,B,1)}]}]})});if(!p.ok)throw new Error(`API request failed: ${p.status}`);const v=(o=(l=(i=(n=(a=(await p.json()).candidates)==null?void 0:a[0])==null?void 0:n.content)==null?void 0:i.parts)==null?void 0:l[0])==null?void 0:o.text;if(!v)throw new Error("No content received from API");const R=v.match(/\{[\s\S]*\}/);if(!R)throw new Error("No valid JSON found in response");const $=JSON.parse(R[0]);if(!$.podcast_names||!Array.isArray($.podcast_names))throw new Error("Invalid response format");const ce=[...x,...$.podcast_names].slice(0,5);N(ce),w(ce)}else{const p=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:xe(t,B,j)}]}]})});if(!p.ok)throw new Error(`API request failed: ${p.status}`);const v=(y=(u=(m=(c=(r=(await p.json()).candidates)==null?void 0:r[0])==null?void 0:c.content)==null?void 0:m.parts)==null?void 0:u[0])==null?void 0:y.text;if(!v)throw new Error("No content received from API");const R=v.match(/\{[\s\S]*\}/);if(!R)throw new Error("No valid JSON found in response");const $=JSON.parse(R[0]);if(!$.podcast_names||!Array.isArray($.podcast_names))throw new Error("Invalid response format");const ce=[...x,...$.podcast_names];N(ce),w(ce)}P(p=>p.filter(E=>{const v=g[E.index];return x.some(R=>R.name===(v==null?void 0:v.name))})),P(p=>p.map(E=>{const v=g[E.index],R=x.findIndex($=>$.name===(v==null?void 0:v.name));return R>=0?{...E,index:R}:E}).filter(E=>E.index>=0))}catch(x){console.error("Error generating refined names:",x),I(x instanceof Error?x.message:"Failed to generate refined names. Please try again.")}finally{J(!1),z(!1)}},Te=t=>{t.preventDefault(),Se()},Ae=t=>{t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),Se())};return e.jsx("div",{className:`podcast-name-generator ${D}`,style:q,children:e.jsxs("div",{className:"generator-container",children:[e.jsxs("div",{className:"header-section",children:[e.jsx("h1",{className:"main-title",children:"Free Podcast Name Generator"}),e.jsx("h2",{className:"main-subtitle",children:"Create the Perfect Name for Your Podcast in Seconds"})]}),e.jsxs("div",{className:"benefits-section",children:[e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"100% Free Forever"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"No Sign-up Required"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"Instant Results"})]})]}),_&&e.jsx("div",{className:"limit-reached-banner",children:e.jsxs("div",{className:"limit-content",children:[e.jsx("span",{className:"limit-icon",children:"⚠️"}),e.jsx("div",{className:"limit-text",children:e.jsx("p",{children:"You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below."})})]})}),g.length===0&&e.jsx("div",{className:"initial-input-section",children:e.jsx("form",{onSubmit:Te,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:k,onChange:t=>U(t.target.value),onKeyPress:Ae,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:C}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:C||!k.trim()||_,className:`generate-button ${_?"disabled":""}`,children:C?"Generating...":_?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star star-partial",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})}),te&&e.jsxs("div",{className:"error-message",children:[e.jsx("span",{className:"error-icon",children:"⚠️"}),te]}),X&&e.jsxs("div",{className:"success-message",children:[e.jsx("span",{className:"success-icon",children:"✨"}),X]}),C&&e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:me?"Generating better names based on your preferences...":"Generating creative podcast names..."})]}),g.length>0&&e.jsxs("div",{className:"results-container",children:[L.length>0&&e.jsxs("div",{className:"favorites-section",children:[e.jsxs("div",{className:"favorites-header",children:[e.jsxs("h3",{children:["🏆 Your Winning Podcast Names (",L.length,")"]}),e.jsx("p",{className:"favorites-subtitle",children:"Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!"})]}),e.jsx("div",{className:"favorites-grid",children:L.map((t,a)=>e.jsxs("div",{className:"favorite-card",children:[e.jsxs("div",{className:"favorite-content",children:[e.jsx("h4",{className:"favorite-name",children:t.name}),e.jsx("p",{className:"favorite-description",children:t.description}),t.suggestedDomain&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsx("span",{className:"domain-name",children:t.suggestedDomain}),e.jsx("span",{className:`domain-status ${t.domainStatus}`,children:t.domainStatus==="available"?"✅ Available":t.domainStatus==="taken"?"❌ Taken":t.domainStatus==="error"?"⚠️ Check manually":"🔍 Checking..."})]})]}),e.jsx("div",{className:"favorite-actions",children:e.jsx("button",{onClick:()=>Re(t.name,-1),className:"copy-button small",title:"Copy to clipboard",children:"📋 Copy"})})]},`fav-${a}`))})]}),e.jsxs("div",{className:"input-section-simple",children:[e.jsx("div",{className:"input-help-message-simple",children:e.jsxs("p",{className:"input-sub-description",children:["💡 Want different suggestions? Update your description below - ",e.jsx("strong",{children:"your favorites will stay safe!"})]})}),e.jsx("form",{onSubmit:Te,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:k,onChange:t=>U(t.target.value),onKeyPress:Ae,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:C}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:C||!k.trim()||_,className:`generate-button ${_?"disabled":""}`,children:C?"Generating...":_?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star star-partial",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})]}),e.jsxs("div",{className:"suggestions-section",children:[e.jsx("div",{className:"suggestions-header",children:e.jsx("h3",{children:"🎯 Current Suggestions"})}),e.jsx("div",{className:"onboarding-banner",children:e.jsxs("div",{className:"onboarding-content",children:[e.jsx("span",{className:"onboarding-icon",children:"💡"}),e.jsxs("div",{className:"onboarding-text",children:[e.jsx("strong",{children:"Smart AI Learning:"})," The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."]})]})}),e.jsx("div",{className:"results-grid",children:g.map((t,a)=>{const n=ne.find(c=>c.index===a),i=(n==null?void 0:n.liked)===!0,l=(n==null?void 0:n.liked)===!1,o=be.has(a),r=ae.has(a);return e.jsxs("div",{className:`result-card ${i?"liked":""} ${l?"disliked":""} ${o?"pending":""} ${he.has(a)?"flying-to-favorites":""}`,style:{opacity:o?.6:1,pointerEvents:o?"none":"auto"},children:[e.jsxs("div",{className:"result-header",children:[e.jsx("h4",{className:"result-name",children:o?i?"Generating new suggestion...":"Generating better suggestion...":t.name}),e.jsxs("div",{className:"result-actions",children:[e.jsxs("div",{className:"feedback-buttons",children:[e.jsx("button",{onClick:()=>Ee(a,!0),className:`feedback-button like-button ${i?"active":""}`,title:"I like this name",disabled:o,children:"👍"}),e.jsx("button",{onClick:()=>Ee(a,!1),className:`feedback-button dislike-button ${l?"active":""} ${o?"loading":""}`,title:o?"Generating replacement...":"I don't like this name",disabled:o,children:o?"🔄":"👎"})]}),e.jsx("button",{onClick:()=>Re(t.name,a),className:"copy-button",title:"Copy podcast name",disabled:o,children:K===a?"✓ Copied!":"📋 Copy"})]})]}),e.jsx("p",{className:"result-description",children:o?i?"Added to favorites! Generating a new suggestion...":"Creating a better suggestion based on your preferences...":t.description}),t.suggestedDomain&&!r&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsxs("code",{className:"domain-text",children:[t.suggestedDomain,".com"]}),e.jsxs("span",{className:`domain-status ${t.domainStatus}`,children:[(t.domainStatus==="checking"||ae.has(a))&&"⏳ Checking...",t.domainStatus==="available"&&"✅ Available",t.domainStatus==="taken"&&"❌ Taken",t.domainStatus==="error"&&"⚠️ Check manually"]})]})]},a)})})]}),pe&&!C&&e.jsxs("div",{className:"refinement-section",children:[e.jsx("div",{className:"refinement-info",children:e.jsx("p",{children:"💡 I'll keep your liked names and replace the disliked ones with better suggestions!"})}),e.jsx("button",{onClick:De,className:"refinement-button",disabled:C,children:me?"🔄 Refining...":"🎯 Replace Disliked Names"})]})]})]})})}});

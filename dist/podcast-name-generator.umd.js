(function(u,E){typeof exports=="object"&&typeof module<"u"?module.exports=E(require("react")):typeof define=="function"&&define.amd?define(["react"],E):(u=typeof globalThis<"u"?globalThis:u||self,u.PodcastNameGenerator=E(u.React))})(this,function(u){"use strict";var E={exports:{}},j={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var L;function q(){if(L)return j;L=1;var p=Symbol.for("react.transitional.element"),x=Symbol.for("react.fragment");function g(d,c,i){var h=null;if(i!==void 0&&(h=""+i),c.key!==void 0&&(h=""+c.key),"key"in c){i={};for(var f in c)f!=="key"&&(i[f]=c[f])}else i=c;return c=i.ref,{$$typeof:p,type:d,key:h,ref:c!==void 0?c:null,props:i}}return j.Fragment=x,j.jsx=g,j.jsxs=g,j}var y={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var J;function K(){return J||(J=1,process.env.NODE_ENV!=="production"&&function(){function p(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case w:return"Fragment";case a:return"Profiler";case I:return"StrictMode";case A:return"Suspense";case k:return"SuspenseList";case Q:return"Activity"}if(typeof e=="object")switch(typeof e.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),e.$$typeof){case C:return"Portal";case b:return(e.displayName||"Context")+".Provider";case l:return(e._context.displayName||"Context")+".Consumer";case U:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Z:return r=e.displayName||null,r!==null?r:p(e.type)||"Memo";case W:r=e._payload,e=e._init;try{return p(e(r))}catch{}}return null}function x(e){return""+e}function g(e){try{x(e);var r=!1}catch{r=!0}if(r){r=console;var t=r.error,s=typeof Symbol=="function"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object";return t.call(r,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",s),x(e)}}function d(e){if(e===w)return"<>";if(typeof e=="object"&&e!==null&&e.$$typeof===W)return"<...>";try{var r=p(e);return r?"<"+r+">":"<...>"}catch{return"<...>"}}function c(){var e=Y.A;return e===null?null:e.getOwner()}function i(){return Error("react-stack-top-frame")}function h(e){if(z.call(e,"key")){var r=Object.getOwnPropertyDescriptor(e,"key").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function f(e,r){function t(){$||($=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",r))}t.isReactWarning=!0,Object.defineProperty(e,"key",{get:t,configurable:!0})}function P(){var e=p(this.type);return V[e]||(V[e]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),e=this.props.ref,e!==void 0?e:null}function S(e,r,t,s,v,m,F,G){return t=m.ref,e={$$typeof:N,type:e,key:r,props:m,_owner:v},(t!==void 0?t:null)!==null?Object.defineProperty(e,"ref",{enumerable:!1,get:P}):Object.defineProperty(e,"ref",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:F}),Object.defineProperty(e,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:G}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function R(e,r,t,s,v,m,F,G){var o=r.children;if(o!==void 0)if(s)if(re(o)){for(s=0;s<o.length;s++)O(o[s]);Object.freeze&&Object.freeze(o)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else O(o);if(z.call(r,"key")){o=p(e);var _=Object.keys(r).filter(function(te){return te!=="key"});s=0<_.length?"{key: someKey, "+_.join(": ..., ")+": ...}":"{key: someKey}",H[o+s]||(_=0<_.length?"{"+_.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,s,o,_,o),H[o+s]=!0)}if(o=null,t!==void 0&&(g(t),o=""+t),h(r)&&(g(r.key),o=""+r.key),"key"in r){t={};for(var M in r)M!=="key"&&(t[M]=r[M])}else t=r;return o&&f(t,typeof e=="function"?e.displayName||e.name||"Unknown":e),S(e,o,m,v,c(),t,F,G)}function O(e){typeof e=="object"&&e!==null&&e.$$typeof===N&&e._store&&(e._store.validated=1)}var T=u,N=Symbol.for("react.transitional.element"),C=Symbol.for("react.portal"),w=Symbol.for("react.fragment"),I=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),l=Symbol.for("react.consumer"),b=Symbol.for("react.context"),U=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),k=Symbol.for("react.suspense_list"),Z=Symbol.for("react.memo"),W=Symbol.for("react.lazy"),Q=Symbol.for("react.activity"),ee=Symbol.for("react.client.reference"),Y=T.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,z=Object.prototype.hasOwnProperty,re=Array.isArray,D=console.createTask?console.createTask:function(){return null};T={"react-stack-bottom-frame":function(e){return e()}};var $,V={},X=T["react-stack-bottom-frame"].bind(T,i)(),B=D(d(i)),H={};y.Fragment=w,y.jsx=function(e,r,t,s,v){var m=1e4>Y.recentlyCreatedOwnerStacks++;return R(e,r,t,!1,s,v,m?Error("react-stack-top-frame"):X,m?D(d(e)):B)},y.jsxs=function(e,r,t,s,v){var m=1e4>Y.recentlyCreatedOwnerStacks++;return R(e,r,t,!0,s,v,m?Error("react-stack-top-frame"):X,m?D(d(e)):B)}}()),y}process.env.NODE_ENV==="production"?E.exports=q():E.exports=K();var n=E.exports;return({apiKey:p="AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",className:x="",style:g={}})=>{const[d,c]=u.useState(""),[i,h]=u.useState([]),[f,P]=u.useState(!1),[S,R]=u.useState(null),[O,T]=u.useState(null),N=async()=>{if(!d.trim()){R("Please describe what your podcast is about");return}P(!0),R(null),h([]);try{const a=`Create 5 high-converting and catchy podcast names based on the following user input: ${d}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`,l=await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${p}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:a}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!l.ok)throw new Error(`API request failed: ${l.status} ${l.statusText}`);const b=await l.json();if(!b.candidates||!b.candidates[0]||!b.candidates[0].content)throw new Error("Invalid response format from API");const A=b.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!A)throw new Error("No valid JSON found in API response");const k=JSON.parse(A[0]);if(!k.podcast_names||!Array.isArray(k.podcast_names))throw new Error("Invalid response structure");h(k.podcast_names)}catch(a){console.error("Error generating podcast names:",a),R(a instanceof Error?a.message:"An unexpected error occurred")}finally{P(!1)}},C=async(a,l)=>{try{await navigator.clipboard.writeText(a),T(l),setTimeout(()=>T(null),2e3)}catch(b){console.error("Failed to copy text:",b)}},w=a=>{a.preventDefault(),N()},I=a=>{a.key==="Enter"&&!a.shiftKey&&(a.preventDefault(),N())};return n.jsx("div",{className:`podcast-name-generator ${x}`,style:g,children:n.jsxs("div",{className:"generator-container",children:[n.jsx("h2",{className:"generator-title",children:"Podcast Name Generator"}),n.jsx("p",{className:"generator-subtitle",children:"Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI"}),n.jsx("form",{onSubmit:w,className:"input-form",children:n.jsxs("div",{className:"input-container",children:[n.jsx("textarea",{value:d,onChange:a=>c(a.target.value),onKeyPress:I,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:f}),n.jsx("button",{type:"submit",disabled:f||!d.trim(),className:"generate-button",children:f?"Generating...":"Generate Names"})]})}),S&&n.jsxs("div",{className:"error-message",children:[n.jsx("span",{className:"error-icon",children:"⚠️"}),S]}),f&&n.jsxs("div",{className:"loading-container",children:[n.jsx("div",{className:"loading-spinner"}),n.jsx("p",{children:"Generating creative podcast names..."})]}),i.length>0&&n.jsxs("div",{className:"results-container",children:[n.jsx("h3",{className:"results-title",children:"Your Podcast Name Suggestions"}),n.jsx("div",{className:"results-grid",children:i.map((a,l)=>n.jsxs("div",{className:"result-card",children:[n.jsxs("div",{className:"result-header",children:[n.jsx("h4",{className:"result-name",children:a.name}),n.jsx("button",{onClick:()=>C(a.name,l),className:"copy-button",title:"Copy podcast name",children:O===l?"✓ Copied!":"📋 Copy"})]}),n.jsx("p",{className:"result-description",children:a.description})]},l))})]})]})})}});

(function(f,C){typeof exports=="object"&&typeof module<"u"?module.exports=C(require("react")):typeof define=="function"&&define.amd?define(["react"],C):(f=typeof globalThis<"u"?globalThis:f||self,f.PodcastNameGenerator=C(f.React))})(this,function(f){"use strict";var C={exports:{}},K={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ke;function ve(){if(ke)return K;ke=1;var R=Symbol.for("react.transitional.element"),G=Symbol.for("react.fragment");function D(k,x,h){var N=null;if(h!==void 0&&(N=""+h),x.key!==void 0&&(N=""+x.key),"key"in x){h={};for(var v in x)v!=="key"&&(h[v]=x[v])}else h=x;return x=h.ref,{$$typeof:R,type:k,key:N,ref:x!==void 0?x:null,props:h}}return K.Fragment=G,K.jsx=D,K.jsxs=D,K}var V={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ne;function ye(){return Ne||(Ne=1,process.env.NODE_ENV!=="production"&&function(){function R(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===de?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case E:return"Fragment";case ge:return"Profiler";case H:return"StrictMode";case X:return"Suspense";case ce:return"SuspenseList";case le:return"Activity"}if(typeof e=="object")switch(typeof e.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),e.$$typeof){case O:return"Portal";case ie:return(e.displayName||"Context")+".Provider";case oe:return(e._context.displayName||"Context")+".Consumer";case L:var o=e.render;return e=e.displayName,e||(e=o.displayName||o.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case be:return o=e.displayName||null,o!==null?o:R(e.type)||"Memo";case Z:o=e._payload,e=e._init;try{return R(e(o))}catch{}}return null}function G(e){return""+e}function D(e){try{G(e);var o=!1}catch{o=!0}if(o){o=console;var u=o.error,p=typeof Symbol=="function"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object";return u.call(o,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",p),G(e)}}function k(e){if(e===E)return"<>";if(typeof e=="object"&&e!==null&&e.$$typeof===Z)return"<...>";try{var o=R(e);return o?"<"+o+">":"<...>"}catch{return"<...>"}}function x(){var e=P.A;return e===null?null:e.getOwner()}function h(){return Error("react-stack-top-frame")}function N(e){if(J.call(e,"key")){var o=Object.getOwnPropertyDescriptor(e,"key").get;if(o&&o.isReactWarning)return!1}return e.key!==void 0}function v(e,o){function u(){$||($=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",o))}u.isReactWarning=!0,Object.defineProperty(e,"key",{get:u,configurable:!0})}function U(){var e=R(this.type);return me[e]||(me[e]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),e=this.props.ref,e!==void 0?e:null}function ae(e,o,u,p,T,_,q,ee){return u=_.ref,e={$$typeof:F,type:e,key:o,props:_,_owner:T},(u!==void 0?u:null)!==null?Object.defineProperty(e,"ref",{enumerable:!1,get:U}):Object.defineProperty(e,"ref",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:q}),Object.defineProperty(e,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:ee}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function A(e,o,u,p,T,_,q,ee){var g=o.children;if(g!==void 0)if(p)if(ue(g)){for(p=0;p<g.length;p++)re(g[p]);Object.freeze&&Object.freeze(g)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else re(g);if(J.call(o,"key")){g=R(e);var n=Object.keys(o).filter(function(s){return s!=="key"});p=0<n.length?"{key: someKey, "+n.join(": ..., ")+": ...}":"{key: someKey}",pe[g+p]||(n=0<n.length?"{"+n.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,p,g,n,g),pe[g+p]=!0)}if(g=null,u!==void 0&&(D(u),g=""+u),N(o)&&(D(o.key),g=""+o.key),"key"in o){u={};for(var t in o)t!=="key"&&(u[t]=o[t])}else u=o;return g&&v(u,typeof e=="function"?e.displayName||e.name||"Unknown":e),ae(e,g,_,T,x(),u,q,ee)}function re(e){typeof e=="object"&&e!==null&&e.$$typeof===F&&e._store&&(e._store.validated=1)}var I=f,F=Symbol.for("react.transitional.element"),O=Symbol.for("react.portal"),E=Symbol.for("react.fragment"),H=Symbol.for("react.strict_mode"),ge=Symbol.for("react.profiler"),oe=Symbol.for("react.consumer"),ie=Symbol.for("react.context"),L=Symbol.for("react.forward_ref"),X=Symbol.for("react.suspense"),ce=Symbol.for("react.suspense_list"),be=Symbol.for("react.memo"),Z=Symbol.for("react.lazy"),le=Symbol.for("react.activity"),de=Symbol.for("react.client.reference"),P=I.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,J=Object.prototype.hasOwnProperty,ue=Array.isArray,W=console.createTask?console.createTask:function(){return null};I={"react-stack-bottom-frame":function(e){return e()}};var $,me={},fe=I["react-stack-bottom-frame"].bind(I,h)(),Q=W(k(h)),pe={};V.Fragment=E,V.jsx=function(e,o,u,p,T){var _=1e4>P.recentlyCreatedOwnerStacks++;return A(e,o,u,!1,p,T,_?Error("react-stack-top-frame"):fe,_?W(k(e)):Q)},V.jsxs=function(e,o,u,p,T){var _=1e4>P.recentlyCreatedOwnerStacks++;return A(e,o,u,!0,p,T,_?Error("react-stack-top-frame"):fe,_?W(k(e)):Q)}}()),V}process.env.NODE_ENV==="production"?C.exports=ve():C.exports=ye();var r=C.exports;return({apiKey:R="AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",className:G="",style:D={}})=>{const[k,x]=f.useState(""),[h,N]=f.useState([]),[v,U]=f.useState(!1),[ae,A]=f.useState(null),[re,I]=f.useState(null),[F,O]=f.useState([]),[E,H]=f.useState({likedNames:[],dislikedNames:[],patterns:{preferredLength:null,preferredStyle:null,likedKeywords:[],dislikedKeywords:[],preferredStructure:null},generationRound:0}),[ge,oe]=f.useState(!1),[ie,L]=f.useState(!1),[X,ce]=f.useState([]),[be,Z]=f.useState(!0),[le,de]=f.useState(!1),[P,J]=f.useState(new Set),ue=n=>n.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"").replace(/^the/,"").substring(0,50),W=async n=>{try{const t=await fetch(`https://dns.google/resolve?name=${n}&type=A`,{method:"GET",headers:{Accept:"application/json"}});if(!t.ok)return"error";const s=await t.json();return s.Answer&&s.Answer.length>0?"taken":"available"}catch(t){return console.warn("Domain check failed:",t),"error"}},$=async n=>{const t=[...n];for(let s=0;s<t.length;s++){const a=ue(t[s].name);t[s].suggestedDomain=a,t[s].domainStatus="checking",J(i=>new Set([...i,s]))}N(t);for(let s=0;s<t.length;s++){const a=`${t[s].suggestedDomain}.com`;try{const i=await W(a);N(d=>{const c=[...d];return c[s]&&(c[s].domainStatus=i),c})}catch{N(d=>{const c=[...d];return c[s]&&(c[s].domainStatus="error"),c})}finally{J(i=>{const d=new Set(i);return d.delete(s),d})}}},me=(n,t)=>{if(n.length===0)return null;const s=n.map(c=>c.name.split(" ").length),a=t.map(c=>c.name.split(" ").length),i=s.reduce((c,y)=>c+y,0)/s.length,d=a.length>0?a.reduce((c,y)=>c+y,0)/a.length:0;return i<=2&&d>2?"short":i<=4&&(d<=2||d>4)?"medium":i>4&&d<=4?"long":i<=2?"short":i<=4?"medium":"long"},fe=(n,t)=>{if(n.length===0)return null;const s=n.map(i=>i.description.toLowerCase()).join(" "),a=t.map(i=>i.description.toLowerCase()).join(" ");return(s.includes("professional")||s.includes("business"))&&!a.includes("professional")&&!a.includes("business")?"professional":(s.includes("creative")||s.includes("unique"))&&!a.includes("creative")&&!a.includes("unique")?"creative":(s.includes("fun")||s.includes("playful"))&&!a.includes("fun")&&!a.includes("playful")?"playful":"descriptive"},Q=n=>{const t=[];n.forEach(a=>{const i=["the","a","an","and","or","but","in","on","at","to","for","of","with","by"],d=a.name.toLowerCase().split(/\s+/).filter(c=>c.length>2&&!i.includes(c));t.push(...d)});const s={};return t.forEach(a=>s[a]=(s[a]||0)+1),Object.entries(s).sort(([,a],[,i])=>i-a).slice(0,5).map(([a])=>a)},pe=n=>{const t=n.filter(a=>a.liked===!0),s=n.filter(a=>a.liked===!1);return{preferredLength:me(t,s),preferredStyle:fe(t,s),likedKeywords:Q(t),dislikedKeywords:Q(s),preferredStructure:null}},e=(n,t)=>{const s=`Create 5 high-converting and catchy podcast names based on the following user input: ${n}.`;let a="";if(t.patterns.preferredLength){const i={short:"1-2 words",medium:"3-4 words",long:"5+ words"};a+=`Focus on ${t.patterns.preferredLength} names (${i[t.patterns.preferredLength]}). `}if(t.patterns.preferredStyle&&(a+=`Use a ${t.patterns.preferredStyle} style. `),t.patterns.likedKeywords.length>0&&(a+=`Incorporate concepts similar to: ${t.patterns.likedKeywords.join(", ")}. `),t.patterns.dislikedKeywords.length>0&&(a+=`Avoid concepts like: ${t.patterns.dislikedKeywords.join(", ")}. `),t.likedNames.length>0){const i=t.likedNames.slice(-2).map(d=>d.name).join('", "');a+=`Generate names with similar appeal to: "${i}". `}return`${s} ${a} Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`},o=async(n=!1)=>{if(!k.trim()){A("Please describe what your podcast is about");return}U(!0),A(null),N([]),n?L(!0):(O([]),oe(!1),L(!1),Z(!0),de(!1));try{const t=n?e(k,E):`Create 5 high-converting and catchy podcast names based on the following user input: ${k}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`,s=await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${R}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:t}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!s.ok)throw new Error(`API request failed: ${s.status} ${s.statusText}`);const a=await s.json();if(!a.candidates||!a.candidates[0]||!a.candidates[0].content)throw new Error("Invalid response format from API");const d=a.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!d)throw new Error("No valid JSON found in API response");const c=JSON.parse(d[0]);if(!c.podcast_names||!Array.isArray(c.podcast_names))throw new Error("Invalid response structure");N(c.podcast_names),$(c.podcast_names),ce(b=>[...b,c.podcast_names]),H(b=>({...b,generationRound:b.generationRound+1}));const y=c.podcast_names.map((b,te)=>({name:b.name,description:b.description,liked:null,timestamp:Date.now(),index:te}));O(y)}catch(t){console.error("Error generating podcast names:",t),A(t instanceof Error?t.message:"An unexpected error occurred")}finally{U(!1),L(!1)}},u=async(n,t)=>{try{await navigator.clipboard.writeText(n),I(t),setTimeout(()=>I(null),2e3)}catch(s){console.error("Failed to copy text:",s)}},p=async(n,t)=>{const s=[...F];s[n]={...s[n],liked:t,timestamp:Date.now()},O(s);const a={...E},i=s[n];t?(a.dislikedNames=a.dislikedNames.filter(c=>c.name!==i.name),a.likedNames.find(c=>c.name===i.name)||a.likedNames.push(i)):(a.likedNames=a.likedNames.filter(c=>c.name!==i.name),a.dislikedNames.find(c=>c.name===i.name)||a.dislikedNames.push(i),k.trim()&&T(n,a)),a.patterns=pe([...a.likedNames,...a.dislikedNames]),H(a),a.likedNames.length+a.dislikedNames.length>=2&&oe(!1),le||(de(!0),Z(!1))},T=async(n,t)=>{var s,a,i,d,c;try{J(m=>new Set([...m,n]));const y=e(k,t),b=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key="+R,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:y.replace("Create 5","Create 1")}]}]})});if(!b.ok)throw new Error(`API request failed: ${b.status}`);const ne=(c=(d=(i=(a=(s=(await b.json()).candidates)==null?void 0:s[0])==null?void 0:a.content)==null?void 0:i.parts)==null?void 0:d[0])==null?void 0:c.text;if(!ne)throw new Error("No content received from API");const S=ne.match(/\{[\s\S]*\}/);if(!S)throw new Error("No valid JSON found in response");const z=JSON.parse(S[0]);if(!z.podcast_names||!Array.isArray(z.podcast_names)||z.podcast_names.length===0)throw new Error("Invalid response format");const B=z.podcast_names[0];N(m=>{const l=[...m];return l[n]=B,l});const we=ue(B.name),he=await W(we);N(m=>{const l=[...m];return l[n]={...l[n],suggestedDomain:we,domainStatus:he},l}),O(m=>{const l=[...m];return l[n]={name:B.name,description:B.description,liked:null,timestamp:Date.now(),index:n},l})}catch(y){console.error("Error generating replacement name:",y)}finally{J(y=>{const b=new Set(y);return b.delete(n),b})}},_=()=>{k.trim()&&q(k)},q=async n=>{var t,s,a,i,d,c,y,b,te,ne;U(!0),A(""),L(!0);try{const S=h.filter((m,l)=>{const w=F.find(j=>j.index===l);return(w==null?void 0:w.liked)===!0}),z=h.filter((m,l)=>{const w=F.find(j=>j.index===l);return(w==null?void 0:w.liked)===!1}).length,B=Math.max(1,z),he=Math.min(5,S.length+B)-S.length;if(he<=0){const m=e(n,E),l=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key="+R,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:m.replace("Create 5","Create 1")}]}]})});if(!l.ok)throw new Error(`API request failed: ${l.status}`);const j=(d=(i=(a=(s=(t=(await l.json()).candidates)==null?void 0:t[0])==null?void 0:s.content)==null?void 0:a.parts)==null?void 0:i[0])==null?void 0:d.text;if(!j)throw new Error("No content received from API");const Y=j.match(/\{[\s\S]*\}/);if(!Y)throw new Error("No valid JSON found in response");const M=JSON.parse(Y[0]);if(!M.podcast_names||!Array.isArray(M.podcast_names))throw new Error("Invalid response format");const se=[...S,...M.podcast_names].slice(0,5);N(se),$(se)}else{const m=e(n,E),l=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key="+R,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:m.replace("Create 5",`Create ${he}`)}]}]})});if(!l.ok)throw new Error(`API request failed: ${l.status}`);const j=(ne=(te=(b=(y=(c=(await l.json()).candidates)==null?void 0:c[0])==null?void 0:y.content)==null?void 0:b.parts)==null?void 0:te[0])==null?void 0:ne.text;if(!j)throw new Error("No content received from API");const Y=j.match(/\{[\s\S]*\}/);if(!Y)throw new Error("No valid JSON found in response");const M=JSON.parse(Y[0]);if(!M.podcast_names||!Array.isArray(M.podcast_names))throw new Error("Invalid response format");const se=[...S,...M.podcast_names];N(se),$(se)}ce(m=>[...m,h]),H(m=>({...m,generationRound:m.generationRound+1})),O(m=>m.filter(l=>{const w=h[l.index];return S.some(j=>j.name===(w==null?void 0:w.name))})),O(m=>m.map(l=>{const w=h[l.index],j=S.findIndex(Y=>Y.name===(w==null?void 0:w.name));return j>=0?{...l,index:j}:l}).filter(l=>l.index>=0))}catch(S){console.error("Error generating refined names:",S),A(S instanceof Error?S.message:"Failed to generate refined names. Please try again.")}finally{U(!1),L(!1)}},ee=n=>{n.preventDefault(),o()},g=n=>{n.key==="Enter"&&!n.shiftKey&&(n.preventDefault(),o())};return r.jsx("div",{className:`podcast-name-generator ${G}`,style:D,children:r.jsxs("div",{className:"generator-container",children:[r.jsx("h2",{className:"generator-title",children:"Podcast Name Generator"}),r.jsx("p",{className:"generator-subtitle",children:"Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI"}),be&&r.jsx("div",{className:"onboarding-banner",children:r.jsxs("div",{className:"onboarding-content",children:[r.jsx("span",{className:"onboarding-icon",children:"💡"}),r.jsxs("div",{className:"onboarding-text",children:[r.jsx("strong",{children:"Pro Tip:"})," Use the 👍👎 buttons to rate names. Liked names stay, disliked names get instantly replaced with better suggestions!"]})]})}),r.jsx("form",{onSubmit:ee,className:"input-form",children:r.jsxs("div",{className:"input-container",children:[r.jsx("textarea",{value:k,onChange:n=>x(n.target.value),onKeyPress:g,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:v}),r.jsx("button",{type:"submit",disabled:v||!k.trim(),className:"generate-button",children:v?"Generating...":"Generate Names"})]})}),ae&&r.jsxs("div",{className:"error-message",children:[r.jsx("span",{className:"error-icon",children:"⚠️"}),ae]}),v&&r.jsxs("div",{className:"loading-container",children:[r.jsx("div",{className:"loading-spinner"}),r.jsx("p",{children:ie?"Generating better names based on your preferences...":"Generating creative podcast names..."})]}),h.length>0&&r.jsxs("div",{className:"results-container",children:[r.jsxs("h3",{className:"results-title",children:[E.generationRound>1?"Refined Podcast Name Suggestions":"Your Podcast Name Suggestions",X.length>1&&r.jsxs("span",{className:"generation-counter",children:[" (Round ",E.generationRound,")"]})]}),E.generationRound>1&&r.jsxs("p",{className:"refinement-info",children:["✨ These names are tailored based on your preferences from ",X.length-1," previous round",X.length>2?"s":""]}),E.generationRound===1&&!le&&r.jsx("div",{className:"feedback-hint",children:r.jsxs("div",{className:"feedback-hint-content",children:[r.jsx("span",{className:"feedback-hint-icon",children:"👆"}),r.jsxs("p",{children:[r.jsx("strong",{children:"Like what you see?"})," Use the 👍👎 buttons below! Liked names will stay, disliked names get instantly replaced with new ones!"]})]})}),r.jsx("div",{className:"results-grid",children:h.map((n,t)=>{const s=F.find(d=>d.index===t),a=(s==null?void 0:s.liked)===!0,i=(s==null?void 0:s.liked)===!1;return r.jsxs("div",{className:`result-card ${a?"liked":""} ${i?"disliked":""}`,children:[r.jsxs("div",{className:"result-header",children:[r.jsxs("h4",{className:"result-name",children:[n.name,a&&E.generationRound>1&&r.jsx("span",{className:"kept-indicator",title:"This name was kept from your previous selection",children:"⭐"})]}),r.jsxs("div",{className:"result-actions",children:[r.jsxs("div",{className:"feedback-buttons",children:[r.jsx("button",{onClick:()=>p(t,!0),className:`feedback-button like-button ${a?"active":""}`,title:"I like this name",disabled:v,children:"👍"}),r.jsx("button",{onClick:()=>p(t,!1),className:`feedback-button dislike-button ${i?"active":""} ${P.has(t)?"loading":""}`,title:P.has(t)?"Generating replacement...":"I don't like this name",disabled:v||P.has(t),children:P.has(t)?"🔄":"👎"})]}),r.jsx("button",{onClick:()=>u(n.name,t),className:"copy-button",title:"Copy podcast name",children:re===t?"✓ Copied!":"📋 Copy"})]})]}),r.jsx("p",{className:"result-description",children:n.description}),n.suggestedDomain&&r.jsxs("div",{className:"domain-info",children:[r.jsxs("div",{className:"domain-name",children:[r.jsx("span",{className:"domain-label",children:"Domain:"}),r.jsxs("code",{className:"domain-text",children:[n.suggestedDomain,".com"]})]}),r.jsxs("div",{className:`domain-status ${n.domainStatus}`,children:[(n.domainStatus==="checking"||P.has(t))&&r.jsxs(r.Fragment,{children:[r.jsx("span",{className:"domain-spinner",children:"⏳"}),r.jsx("span",{children:"Checking..."})]}),n.domainStatus==="available"&&r.jsxs(r.Fragment,{children:[r.jsx("span",{className:"domain-icon available",children:"✅"}),r.jsx("span",{children:"Available"})]}),n.domainStatus==="taken"&&r.jsxs(r.Fragment,{children:[r.jsx("span",{className:"domain-icon taken",children:"❌"}),r.jsx("span",{children:"Taken"})]}),n.domainStatus==="error"&&r.jsxs(r.Fragment,{children:[r.jsx("span",{className:"domain-icon error",children:"⚠️"}),r.jsx("span",{children:"Check manually"})]})]})]})]},t)})}),ge&&!v&&r.jsxs("div",{className:"refinement-section",children:[r.jsx("div",{className:"refinement-info",children:r.jsx("p",{children:"💡 I'll keep your liked names and replace the disliked ones with better suggestions!"})}),r.jsx("button",{onClick:_,className:"refinement-button",disabled:v,children:ie?"🔄 Refining...":"🎯 Replace Disliked Names"})]})]})]})})}});

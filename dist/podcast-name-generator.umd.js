(function(f,G){typeof exports=="object"&&typeof module<"u"?module.exports=G(require("react")):typeof define=="function"&&define.amd?define(["react"],G):(f=typeof globalThis<"u"?globalThis:f||self,f.PodcastNameGenerator=G(f.React))})(this,function(f){"use strict";var G={exports:{}},Z={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var je;function Re(){if(je)return Z;je=1;var D=Symbol.for("react.transitional.element"),Y=Symbol.for("react.fragment");function y(U,p,N){var L=null;if(N!==void 0&&(L=""+N),p.key!==void 0&&(L=""+p.key),"key"in p){N={};for(var F in p)F!=="key"&&(N[F]=p[F])}else N=p;return p=N.ref,{$$typeof:D,type:U,key:L,ref:p!==void 0?p:null,props:N}}return Z.Fragment=Y,Z.jsx=y,Z.jsxs=y,Z}var ee={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var we;function Ee(){return we||(we=1,process.env.NODE_ENV!=="production"&&function(){function D(s){if(s==null)return null;if(typeof s=="function")return s.$$typeof===be?null:s.displayName||s.name||null;if(typeof s=="string")return s;switch(s){case P:return"Fragment";case de:return"Profiler";case q:return"StrictMode";case z:return"Suspense";case Ne:return"SuspenseList";case B:return"Activity"}if(typeof s=="object")switch(typeof s.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),s.$$typeof){case ne:return"Portal";case ve:return(s.displayName||"Context")+".Provider";case ge:return(s._context.displayName||"Context")+".Consumer";case me:var c=s.render;return s=s.displayName,s||(s=c.displayName||c.name||"",s=s!==""?"ForwardRef("+s+")":"ForwardRef"),s;case ue:return c=s.displayName||null,c!==null?c:D(s.type)||"Memo";case ae:c=s._payload,s=s._init;try{return D(s(c))}catch{}}return null}function Y(s){return""+s}function y(s){try{Y(s);var c=!1}catch{c=!0}if(c){c=console;var m=c.error,b=typeof Symbol=="function"&&Symbol.toStringTag&&s[Symbol.toStringTag]||s.constructor.name||"Object";return m.call(c,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",b),Y(s)}}function U(s){if(s===P)return"<>";if(typeof s=="object"&&s!==null&&s.$$typeof===ae)return"<...>";try{var c=D(s);return c?"<"+c+">":"<...>"}catch{return"<...>"}}function p(){var s=W.A;return s===null?null:s.getOwner()}function N(){return Error("react-stack-top-frame")}function L(s){if(xe.call(s,"key")){var c=Object.getOwnPropertyDescriptor(s,"key").get;if(c&&c.isReactWarning)return!1}return s.key!==void 0}function F(s,c){function m(){re||(re=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",c))}m.isReactWarning=!0,Object.defineProperty(s,"key",{get:m,configurable:!0})}function T(){var s=D(this.type);return oe[s]||(oe[s]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),s=this.props.ref,s!==void 0?s:null}function J(s,c,m,b,_,R,X,H){return m=R.ref,s={$$typeof:se,type:s,key:c,props:R,_owner:_},(m!==void 0?m:null)!==null?Object.defineProperty(s,"ref",{enumerable:!1,get:T}):Object.defineProperty(s,"ref",{enumerable:!1,value:null}),s._store={},Object.defineProperty(s._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(s,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(s,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:X}),Object.defineProperty(s,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:H}),Object.freeze&&(Object.freeze(s.props),Object.freeze(s)),s}function te(s,c,m,b,_,R,X,H){var j=c.children;if(j!==void 0)if(b)if(he(j)){for(b=0;b<j.length;b++)I(j[b]);Object.freeze&&Object.freeze(j)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else I(j);if(xe.call(c,"key")){j=D(s);var O=Object.keys(c).filter(function(pe){return pe!=="key"});b=0<O.length?"{key: someKey, "+O.join(": ..., ")+": ...}":"{key: someKey}",fe[j+b]||(O=0<O.length?"{"+O.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,b,j,O,j),fe[j+b]=!0)}if(j=null,m!==void 0&&(y(m),j=""+m),L(c)&&(y(c.key),j=""+c.key),"key"in c){m={};for(var Q in c)Q!=="key"&&(m[Q]=c[Q])}else m=c;return j&&F(m,typeof s=="function"?s.displayName||s.name||"Unknown":s),J(s,j,R,_,p(),m,X,H)}function I(s){typeof s=="object"&&s!==null&&s.$$typeof===se&&s._store&&(s._store.validated=1)}var K=f,se=Symbol.for("react.transitional.element"),ne=Symbol.for("react.portal"),P=Symbol.for("react.fragment"),q=Symbol.for("react.strict_mode"),de=Symbol.for("react.profiler"),ge=Symbol.for("react.consumer"),ve=Symbol.for("react.context"),me=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),Ne=Symbol.for("react.suspense_list"),ue=Symbol.for("react.memo"),ae=Symbol.for("react.lazy"),B=Symbol.for("react.activity"),be=Symbol.for("react.client.reference"),W=K.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,xe=Object.prototype.hasOwnProperty,he=Array.isArray,A=console.createTask?console.createTask:function(){return null};K={"react-stack-bottom-frame":function(s){return s()}};var re,oe={},ie=K["react-stack-bottom-frame"].bind(K,N)(),V=A(U(N)),fe={};ee.Fragment=P,ee.jsx=function(s,c,m,b,_){var R=1e4>W.recentlyCreatedOwnerStacks++;return te(s,c,m,!1,b,_,R?Error("react-stack-top-frame"):ie,R?A(U(s)):V)},ee.jsxs=function(s,c,m,b,_){var R=1e4>W.recentlyCreatedOwnerStacks++;return te(s,c,m,!0,b,_,R?Error("react-stack-top-frame"):ie,R?A(U(s)):V)}}()),ee}process.env.NODE_ENV==="production"?G.exports=Re():G.exports=Ee();var e=G.exports;return({className:D="",style:Y={}})=>{const[y,U]=f.useState(""),[p,N]=f.useState([]),[L,F]=f.useState([]),[T,J]=f.useState(!1),[te,I]=f.useState(null),[K,se]=f.useState(null),[ne,P]=f.useState([]),[q,de]=f.useState({likedNames:[],dislikedNames:[],patterns:{preferredLength:null,preferredStyle:null,likedKeywords:[],dislikedKeywords:[],preferredStructure:null}}),[ge,ve]=f.useState(!1),[me,z]=f.useState(!1),[Ne,ue]=f.useState(!1),[ae,B]=f.useState(new Set),[be,W]=f.useState(new Set),[xe,he]=f.useState(0),[A,re]=f.useState(!1),oe=100,ie=()=>{const t=document.createElement("canvas"),n=t.getContext("2d");n.textBaseline="top",n.font="14px Arial",n.fillText("Usage tracking",2,2);const a=t.toDataURL(),i=navigator.userAgent,r=navigator.language,l=Intl.DateTimeFormat().resolvedOptions().timeZone,o=a+i+r+l;let d=0;for(let u=0;u<o.length;u++){const h=o.charCodeAt(u);d=(d<<5)-d+h,d=d&d}return`podcast_usage_${Math.abs(d)}`},V=()=>{const t=ie(),n=new Date().toDateString(),a=`${t}_${n}`,i=localStorage.getItem(a),r=i?parseInt(i,10):0;return he(r),r>=oe?(re(!0),!1):!0},fe=(t=1)=>{const n=ie(),a=new Date().toDateString(),i=`${n}_${a}`,r=localStorage.getItem(i),o=(r?parseInt(r,10):0)+t;localStorage.setItem(i,o.toString()),he(o),o>=oe&&re(!0)};f.useEffect(()=>{V()},[]);const s=t=>{let n=t.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g," ").replace(/^the\s+/,"").trim();const a=["the","and","for","with","from","show","podcast","cast","of","in","on","at","to","a","an"],i=n.split(" "),r=i.filter(o=>o.length>2&&!a.includes(o));if(r.length===1){const o=r[0];return o.length>=6&&o.length<=15?o:o.length<6?o+"pod":c(o)}if(r.length>=2){const o=r[0],d=r[1],u=o+d;if(u.length>=6&&u.length<=15)return u;const h=c(o),E=c(d),w=h+E;if(w.length>=6&&w.length<=15)return w;if(w.length>15)return h+"-"+E}if(r.length>0){const o=c(r[0]),d=["cast","pod","show","talk"];for(const u of d){const h=o+u;if(h.length>=6&&h.length<=15)return h}if(o.length>=6&&o.length<=15)return o}const l=i[0];return l&&l.length>=3?c(l)+"pod":"podcast"+Math.random().toString(36).substring(2,5)},c=t=>{if(t.length<=8)return t;const n={business:"biz",entrepreneur:"entre",marketing:"market",finance:"fin",startup:"start",leadership:"lead",strategy:"strat",success:"win",growth:"grow",innovation:"innov",management:"manage",technology:"tech",development:"dev",digital:"digi",software:"soft",coding:"code",programming:"prog",stories:"story",journey:"path",adventure:"quest",creative:"create",entertainment:"fun",education:"learn",knowledge:"know",wisdom:"wise",lifestyle:"life",wellness:"well",fitness:"fit",health:"heal",mindset:"mind",motivation:"motive",inspiration:"inspire",american:"usa",european:"euro",international:"global",community:"comm",culture:"cult",society:"social"};if(n[t])return n[t];const a=["super","mega","ultra","micro","mini","multi"];for(const r of a)if(t.startsWith(r)&&t.length>r.length+3){const l=t.substring(r.length);if(l.length<=8)return l}const i=["ing","tion","sion","ness","ment","able","ible"];for(const r of i)if(t.endsWith(r)&&t.length>r.length+4){const l=t.substring(0,t.length-r.length);if(l.length>=4&&l.length<=8)return l}return t.length>8?t.substring(0,8):t},m=async t=>{try{const n=await fetch(`https://dns.google/resolve?name=${t}&type=A`,{method:"GET",headers:{Accept:"application/json"}});if(!n.ok)return"error";const a=await n.json();return a.Answer&&a.Answer.length>0?"taken":"available"}catch(n){return console.warn("Domain check failed:",n),"error"}},b=async t=>{const n=[...t];for(let a=0;a<n.length;a++){const i=s(n[a].name);n[a].suggestedDomain=i,n[a].domainStatus="checking",B(r=>new Set([...r,a]))}N(n);for(let a=0;a<n.length;a++){const i=`${n[a].suggestedDomain}.com`;try{const r=await m(i);N(l=>{const o=[...l];return o[a]&&(o[a].domainStatus=r),o})}catch{N(l=>{const o=[...l];return o[a]&&(o[a].domainStatus="error"),o})}finally{B(r=>{const l=new Set(r);return l.delete(a),l})}}},_=(t,n)=>{if(t.length===0)return null;const a=t.map(o=>o.name.split(" ").length),i=n.map(o=>o.name.split(" ").length),r=a.reduce((o,d)=>o+d,0)/a.length,l=i.length>0?i.reduce((o,d)=>o+d,0)/i.length:0;return r<=2&&l>2?"short":r<=4&&(l<=2||l>4)?"medium":r>4&&l<=4?"long":r<=2?"short":r<=4?"medium":"long"},R=(t,n)=>{if(t.length===0)return null;const a=t.map(r=>r.description.toLowerCase()).join(" "),i=n.map(r=>r.description.toLowerCase()).join(" ");return(a.includes("professional")||a.includes("business"))&&!i.includes("professional")&&!i.includes("business")?"professional":(a.includes("creative")||a.includes("unique"))&&!i.includes("creative")&&!i.includes("unique")?"creative":(a.includes("fun")||a.includes("playful"))&&!i.includes("fun")&&!i.includes("playful")?"playful":"descriptive"},X=t=>{const n=[];t.forEach(i=>{const r=["the","a","an","and","or","but","in","on","at","to","for","of","with","by"],l=i.name.toLowerCase().split(/\s+/).filter(o=>o.length>2&&!r.includes(o));n.push(...l)});const a={};return n.forEach(i=>a[i]=(a[i]||0)+1),Object.entries(a).sort(([,i],[,r])=>r-i).slice(0,5).map(([i])=>i)},H=t=>{const n=t.filter(i=>i.liked===!0),a=t.filter(i=>i.liked===!1);return{preferredLength:_(n,a),preferredStyle:R(n,a),likedKeywords:X(n),dislikedKeywords:X(a),preferredStructure:null}},j=(t,n)=>{const a=[...n.likedNames.map(o=>o.name.toLowerCase()),...n.dislikedNames.map(o=>o.name.toLowerCase()),...p.map(o=>o.name.toLowerCase())],i=`Create 4 unique, high-converting podcast names for: ${t}`;let r=`

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;a.length>0&&(r+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${a.map(o=>`- ${o}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);let l="";if(n.patterns.preferredLength&&(l+=`
Focus on ${{short:"1-2 words (punchy and memorable)",medium:"2-3 words (balanced and brandable)",long:"3-4 words (descriptive but still catchy)"}[n.patterns.preferredLength]}. `),n.patterns.preferredStyle&&(l+=`Use ${{descriptive:"clear, straightforward names that explain the content",creative:"imaginative, metaphorical, or playful names",professional:"authoritative, business-focused names",playful:"fun, energetic, engaging names"}[n.patterns.preferredStyle]||n.patterns.preferredStyle}. `),n.patterns.likedKeywords.length>0&&(l+=`
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `),n.patterns.dislikedKeywords.length>0&&(l+=`
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `),n.likedNames.length>0){const o=n.likedNames.slice(-2).map(d=>d.name).join('", "');l+=`
Generate names with similar appeal to: "${o}" (but completely different concepts). `}return`${i}${r}${l}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`},O=(t,n,a=1)=>{const i=[...n.likedNames.map(d=>d.name.toLowerCase()),...n.dislikedNames.map(d=>d.name.toLowerCase()),...p.map(d=>d.name.toLowerCase())],r=`Create ${a} unique, high-converting podcast name${a>1?"s":""} for: ${t}`;let l=`

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;i.length>0&&(l+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${i.map(d=>`- ${d}`).join(`
`)}
Do not create names similar to any of the above.`);let o="";return n.patterns.likedKeywords.length>0&&(o+=`
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `),n.patterns.dislikedKeywords.length>0&&(o+=`
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `),`${r}${l}${o}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${a>1?', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}':""}]}`},Q=async(t=!1)=>{if(!y.trim()){I("Please describe what your podcast is about");return}if(!V()){I(null);return}J(!0),I(null),N([]),t?z(!0):(P([]),ve(!1),z(!1),ue(!1));try{const n=t?j(y,q):`Create 4 unique, high-converting podcast names for: ${y}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`,a=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:n}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!a.ok)throw new Error(`API request failed: ${a.status} ${a.statusText}`);const i=await a.json();if(!i.candidates||!i.candidates[0]||!i.candidates[0].content)throw new Error("Invalid response format from API");const l=i.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!l)throw new Error("No valid JSON found in API response");const o=JSON.parse(l[0]);if(!o.podcast_names||!Array.isArray(o.podcast_names))throw new Error("Invalid response structure");N(o.podcast_names),fe(4),b(o.podcast_names);const d=o.podcast_names.map((u,h)=>({name:u.name,description:u.description,liked:null,timestamp:Date.now(),index:h}));P(d)}catch(n){console.error("Error generating podcast names:",n),I(n instanceof Error?n.message:"An unexpected error occurred")}finally{J(!1),z(!1)}},pe=async(t,n)=>{try{await navigator.clipboard.writeText(t),se(n),setTimeout(()=>se(null),2e3)}catch(a){console.error("Failed to copy text:",a)}},ye=async(t,n)=>{const a=p[t];a&&(n?(F(i=>i.find(r=>r.name===a.name)?i:[...i,a]),de(i=>{const r={...i};return r.dislikedNames=r.dislikedNames.filter(l=>l.name!==a.name),r.likedNames.find(l=>l.name===a.name)||r.likedNames.push({name:a.name,description:a.description,liked:!0,timestamp:Date.now(),index:t}),r.patterns=H([...r.likedNames,...r.dislikedNames]),r}),W(i=>new Set([...i,t]))):(W(i=>new Set([...i,t])),de(i=>{const r={...i};return r.likedNames=r.likedNames.filter(l=>l.name!==a.name),r.dislikedNames.find(l=>l.name===a.name)||r.dislikedNames.push({name:a.name,description:a.description,liked:!1,timestamp:Date.now(),index:t}),r.patterns=H([...r.likedNames,...r.dislikedNames]),r})),y.trim()&&Te(t),Ne||ue(!0))},Te=async t=>{var n,a,i,r,l,o;if(V()){console.log(`🔄 Starting replacement generation for index ${t}`);try{const d=O(y,q,1);console.log(`📝 Generated single name prompt for index ${t}:`,d.substring(0,100)+"..."),console.log(`🌐 Making API call for index ${t}...`);const u=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:d}]}]})});if(console.log(`📡 API response status for index ${t}:`,u.status),!u.ok)throw new Error(`Failed to generate replacement suggestion: ${u.status} ${u.statusText}`);const h=await u.json();console.log(`📦 API response data for index ${t}:`,h);const E=(l=(r=(i=(a=(n=h.candidates)==null?void 0:n[0])==null?void 0:a.content)==null?void 0:i.parts)==null?void 0:r[0])==null?void 0:l.text;if(!E)throw new Error("No content in API response");console.log(`📄 API content for index ${t}:`,E.substring(0,200)+"...");const w=E.match(/\{[\s\S]*\}/);if(!w)throw console.error(`❌ No valid JSON found in response for index ${t}:`,E),new Error("No valid JSON found in response");const le=JSON.parse(w[0]);console.log(`🔍 Parsed response for index ${t}:`,le);const C=(o=le.podcast_names)==null?void 0:o[0];if(C){if(console.log(`✅ New name generated for index ${t}:`,C),N($=>{const x=[...$];return x[t]={name:C.name,description:C.description,suggestedDomain:C.suggestedDomain,domainStatus:"checking"},console.log(`🔄 Updated results for index ${t}:`,x[t]),x}),W($=>{const x=new Set($);return x.delete(t),console.log(`🗑️ Removed index ${t} from pending replacements. Remaining:`,Array.from(x)),x}),P($=>{const x=$.filter(g=>g.index!==t);return console.log(`🧹 Cleared feedback for index ${t}. Remaining feedback:`,x),x}),C.suggestedDomain){console.log(`🌐 Checking domain availability for ${C.suggestedDomain}...`),B(x=>new Set([...x,t]));const $=await m(C.suggestedDomain);console.log(`🏷️ Domain status for ${C.suggestedDomain}:`,$),N(x=>{const g=[...x];return g[t]&&(g[t].domainStatus=$),g}),B(x=>{const g=new Set(x);return g.delete(t),g})}console.log(`🎉 Successfully completed replacement for index ${t}`)}else throw console.error(`❌ No new name found in parsed response for index ${t}:`,le),new Error("No new name found in API response")}catch(d){console.error(`❌ Error generating replacement suggestion for index ${t}:`,d),W(u=>{const h=new Set(u);return h.delete(t),console.log(`🗑️ Removed index ${t} from pending on error. Remaining:`,Array.from(h)),h}),P(u=>{const h=u.filter(E=>E.index!==t);return console.log(`🧹 Cleared feedback for index ${t} on error. Remaining:`,h),h})}}},Ae=()=>{y.trim()&&Ce(y)},Ce=async t=>{var n,a,i,r,l,o,d,u,h,E;J(!0),I(""),z(!0);try{const w=p.filter((g,S)=>{const v=ne.find(k=>k.index===S);return(v==null?void 0:v.liked)===!0}),le=p.filter((g,S)=>{const v=ne.find(k=>k.index===S);return(v==null?void 0:v.liked)===!1}).length,C=Math.max(1,le),x=Math.min(5,w.length+C)-w.length;if(x<=0){const g=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:O(t,q,1)}]}]})});if(!g.ok)throw new Error(`API request failed: ${g.status}`);const v=(l=(r=(i=(a=(n=(await g.json()).candidates)==null?void 0:n[0])==null?void 0:a.content)==null?void 0:i.parts)==null?void 0:r[0])==null?void 0:l.text;if(!v)throw new Error("No content received from API");const k=v.match(/\{[\s\S]*\}/);if(!k)throw new Error("No valid JSON found in response");const M=JSON.parse(k[0]);if(!M.podcast_names||!Array.isArray(M.podcast_names))throw new Error("Invalid response format");const ce=[...w,...M.podcast_names].slice(0,5);N(ce),b(ce)}else{const g=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:O(t,q,x)}]}]})});if(!g.ok)throw new Error(`API request failed: ${g.status}`);const v=(E=(h=(u=(d=(o=(await g.json()).candidates)==null?void 0:o[0])==null?void 0:d.content)==null?void 0:u.parts)==null?void 0:h[0])==null?void 0:E.text;if(!v)throw new Error("No content received from API");const k=v.match(/\{[\s\S]*\}/);if(!k)throw new Error("No valid JSON found in response");const M=JSON.parse(k[0]);if(!M.podcast_names||!Array.isArray(M.podcast_names))throw new Error("Invalid response format");const ce=[...w,...M.podcast_names];N(ce),b(ce)}P(g=>g.filter(S=>{const v=p[S.index];return w.some(k=>k.name===(v==null?void 0:v.name))})),P(g=>g.map(S=>{const v=p[S.index],k=w.findIndex(M=>M.name===(v==null?void 0:v.name));return k>=0?{...S,index:k}:S}).filter(S=>S.index>=0))}catch(w){console.error("Error generating refined names:",w),I(w instanceof Error?w.message:"Failed to generate refined names. Please try again.")}finally{J(!1),z(!1)}},ke=t=>{t.preventDefault(),Q()},Se=t=>{t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),Q())};return e.jsx("div",{className:`podcast-name-generator ${D}`,style:Y,children:e.jsxs("div",{className:"generator-container",children:[e.jsxs("div",{className:"header-section",children:[e.jsx("h1",{className:"main-title",children:"Free Podcast Name Generator"}),e.jsx("h2",{className:"main-subtitle",children:"Create the Perfect Name for Your Podcast in Seconds"})]}),e.jsxs("div",{className:"benefits-section",children:[e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"100% Free Forever"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"No Sign-up Required"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"Instant Results"})]})]}),A&&e.jsx("div",{className:"limit-reached-banner",children:e.jsxs("div",{className:"limit-content",children:[e.jsx("span",{className:"limit-icon",children:"⚠️"}),e.jsx("div",{className:"limit-text",children:e.jsx("p",{children:"You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below."})})]})}),p.length===0&&e.jsx("div",{className:"initial-input-section",children:e.jsx("form",{onSubmit:ke,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:y,onChange:t=>U(t.target.value),onKeyPress:Se,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:T}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:T||!y.trim()||A,className:`generate-button ${A?"disabled":""}`,children:T?"Generating...":A?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star star-partial",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})}),te&&e.jsxs("div",{className:"error-message",children:[e.jsx("span",{className:"error-icon",children:"⚠️"}),te]}),T&&e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:me?"Generating better names based on your preferences...":"Generating creative podcast names..."})]}),p.length>0&&e.jsxs("div",{className:"results-container",children:[L.length>0&&e.jsxs("div",{className:"favorites-section",children:[e.jsxs("div",{className:"favorites-header",children:[e.jsxs("h3",{children:["🏆 Your Winning Podcast Names (",L.length,")"]}),e.jsx("p",{className:"favorites-subtitle",children:"Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!"})]}),e.jsx("div",{className:"favorites-grid",children:L.map((t,n)=>e.jsxs("div",{className:"favorite-card",children:[e.jsxs("div",{className:"favorite-content",children:[e.jsx("h4",{className:"favorite-name",children:t.name}),e.jsx("p",{className:"favorite-description",children:t.description}),t.suggestedDomain&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsx("span",{className:"domain-name",children:t.suggestedDomain}),e.jsx("span",{className:`domain-status ${t.domainStatus}`,children:t.domainStatus==="available"?"✅ Available":t.domainStatus==="taken"?"❌ Taken":t.domainStatus==="error"?"⚠️ Check manually":"🔍 Checking..."})]})]}),e.jsx("div",{className:"favorite-actions",children:e.jsx("button",{onClick:()=>pe(t.name,-1),className:"copy-button small",title:"Copy to clipboard",children:"📋 Copy"})})]},`fav-${n}`))})]}),e.jsxs("div",{className:"input-section-simple",children:[e.jsx("div",{className:"input-help-message-simple",children:e.jsxs("p",{className:"input-sub-description",children:["💡 Want different suggestions? Update your description below - ",e.jsx("strong",{children:"your favorites will stay safe!"})]})}),e.jsx("form",{onSubmit:ke,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:y,onChange:t=>U(t.target.value),onKeyPress:Se,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:T}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:T||!y.trim()||A,className:`generate-button ${A?"disabled":""}`,children:T?"Generating...":A?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star star-partial",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})]}),e.jsxs("div",{className:"suggestions-section",children:[e.jsx("div",{className:"suggestions-header",children:e.jsx("h3",{children:"🎯 Current Suggestions"})}),e.jsx("div",{className:"onboarding-banner",children:e.jsxs("div",{className:"onboarding-content",children:[e.jsx("span",{className:"onboarding-icon",children:"💡"}),e.jsxs("div",{className:"onboarding-text",children:[e.jsx("strong",{children:"Smart AI Learning:"})," The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."]})]})}),e.jsx("div",{className:"results-grid",children:p.map((t,n)=>{const a=ne.find(d=>d.index===n),i=(a==null?void 0:a.liked)===!0,r=(a==null?void 0:a.liked)===!1,l=be.has(n),o=ae.has(n);return e.jsxs("div",{className:`result-card ${i?"liked":""} ${r?"disliked":""} ${l?"pending":""}`,style:{opacity:l?.6:1,pointerEvents:l?"none":"auto"},children:[e.jsxs("div",{className:"result-header",children:[e.jsx("h4",{className:"result-name",children:l?i?"Generating new suggestion...":"Generating better suggestion...":t.name}),e.jsxs("div",{className:"result-actions",children:[e.jsxs("div",{className:"feedback-buttons",children:[e.jsx("button",{onClick:()=>ye(n,!0),className:`feedback-button like-button ${i?"active":""}`,title:"I like this name",disabled:l,children:"👍"}),e.jsx("button",{onClick:()=>ye(n,!1),className:`feedback-button dislike-button ${r?"active":""} ${l?"loading":""}`,title:l?"Generating replacement...":"I don't like this name",disabled:l,children:l?"🔄":"👎"})]}),e.jsx("button",{onClick:()=>pe(t.name,n),className:"copy-button",title:"Copy podcast name",disabled:l,children:K===n?"✓ Copied!":"📋 Copy"})]})]}),e.jsx("p",{className:"result-description",children:l?i?"Added to favorites! Generating a new suggestion...":"Creating a better suggestion based on your preferences...":t.description}),t.suggestedDomain&&!o&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsxs("code",{className:"domain-text",children:[t.suggestedDomain,".com"]}),e.jsxs("span",{className:`domain-status ${t.domainStatus}`,children:[(t.domainStatus==="checking"||ae.has(n))&&"⏳ Checking...",t.domainStatus==="available"&&"✅ Available",t.domainStatus==="taken"&&"❌ Taken",t.domainStatus==="error"&&"⚠️ Check manually"]})]})]},n)})})]}),ge&&!T&&e.jsxs("div",{className:"refinement-section",children:[e.jsx("div",{className:"refinement-info",children:e.jsx("p",{children:"💡 I'll keep your liked names and replace the disliked ones with better suggestions!"})}),e.jsx("button",{onClick:Ae,className:"refinement-button",disabled:T,children:me?"🔄 Refining...":"🎯 Replace Disliked Names"})]})]})]})})}});

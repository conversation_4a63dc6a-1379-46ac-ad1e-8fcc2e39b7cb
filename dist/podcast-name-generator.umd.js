(function(h,W){typeof exports=="object"&&typeof module<"u"?module.exports=W(require("react")):typeof define=="function"&&define.amd?define(["react"],W):(h=typeof globalThis<"u"?globalThis:h||self,h.PodcastNameGenerator=W(h.React))})(this,function(h){"use strict";var W={exports:{}},Z={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var je;function Ae(){if(je)return Z;je=1;var O=Symbol.for("react.transitional.element"),J=Symbol.for("react.fragment");function y($,p,N){var D=null;if(N!==void 0&&(D=""+N),p.key!==void 0&&(D=""+p.key),"key"in p){N={};for(var G in p)G!=="key"&&(N[G]=p[G])}else N=p;return p=N.ref,{$$typeof:O,type:$,key:D,ref:p!==void 0?p:null,props:N}}return Z.Fragment=J,Z.jsx=y,Z.jsxs=y,Z}var ee={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ye;function Ce(){return ye||(ye=1,process.env.NODE_ENV!=="production"&&function(){function O(s){if(s==null)return null;if(typeof s=="function")return s.$$typeof===be?null:s.displayName||s.name||null;if(typeof s=="string")return s;switch(s){case C:return"Fragment";case de:return"Profiler";case B:return"StrictMode";case Y:return"Suspense";case Ne:return"SuspenseList";case V:return"Activity"}if(typeof s=="object")switch(typeof s.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),s.$$typeof){case ne:return"Portal";case ve:return(s.displayName||"Context")+".Provider";case ge:return(s._context.displayName||"Context")+".Consumer";case me:var d=s.render;return s=s.displayName,s||(s=d.displayName||d.name||"",s=s!==""?"ForwardRef("+s+")":"ForwardRef"),s;case ue:return d=s.displayName||null,d!==null?d:O(s.type)||"Memo";case ae:d=s._payload,s=s._init;try{return O(s(d))}catch{}}return null}function J(s){return""+s}function y(s){try{J(s);var d=!1}catch{d=!0}if(d){d=console;var m=d.error,w=typeof Symbol=="function"&&Symbol.toStringTag&&s[Symbol.toStringTag]||s.constructor.name||"Object";return m.call(d,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",w),J(s)}}function $(s){if(s===C)return"<>";if(typeof s=="object"&&s!==null&&s.$$typeof===ae)return"<...>";try{var d=O(s);return d?"<"+d+">":"<...>"}catch{return"<...>"}}function p(){var s=U.A;return s===null?null:s.getOwner()}function N(){return Error("react-stack-top-frame")}function D(s){if(he.call(s,"key")){var d=Object.getOwnPropertyDescriptor(s,"key").get;if(d&&d.isReactWarning)return!1}return s.key!==void 0}function G(s,d){function m(){re||(re=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",d))}m.isReactWarning=!0,Object.defineProperty(s,"key",{get:m,configurable:!0})}function T(){var s=O(this.type);return we[s]||(we[s]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),s=this.props.ref,s!==void 0?s:null}function K(s,d,m,w,_,k,H,z){return m=k.ref,s={$$typeof:se,type:s,key:d,props:k,_owner:_},(m!==void 0?m:null)!==null?Object.defineProperty(s,"ref",{enumerable:!1,get:T}):Object.defineProperty(s,"ref",{enumerable:!1,value:null}),s._store={},Object.defineProperty(s._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(s,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(s,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:H}),Object.defineProperty(s,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:z}),Object.freeze&&(Object.freeze(s.props),Object.freeze(s)),s}function te(s,d,m,w,_,k,H,z){var x=d.children;if(x!==void 0)if(w)if(fe(x)){for(w=0;w<x.length;w++)L(x[w]);Object.freeze&&Object.freeze(x)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else L(x);if(he.call(d,"key")){x=O(s);var F=Object.keys(d).filter(function(pe){return pe!=="key"});w=0<F.length?"{key: someKey, "+F.join(": ..., ")+": ...}":"{key: someKey}",ie[x+w]||(F=0<F.length?"{"+F.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,w,x,F,x),ie[x+w]=!0)}if(x=null,m!==void 0&&(y(m),x=""+m),D(d)&&(y(d.key),x=""+d.key),"key"in d){m={};for(var Q in d)Q!=="key"&&(m[Q]=d[Q])}else m=d;return x&&G(m,typeof s=="function"?s.displayName||s.name||"Unknown":s),K(s,x,k,_,p(),m,H,z)}function L(s){typeof s=="object"&&s!==null&&s.$$typeof===se&&s._store&&(s._store.validated=1)}var q=h,se=Symbol.for("react.transitional.element"),ne=Symbol.for("react.portal"),C=Symbol.for("react.fragment"),B=Symbol.for("react.strict_mode"),de=Symbol.for("react.profiler"),ge=Symbol.for("react.consumer"),ve=Symbol.for("react.context"),me=Symbol.for("react.forward_ref"),Y=Symbol.for("react.suspense"),Ne=Symbol.for("react.suspense_list"),ue=Symbol.for("react.memo"),ae=Symbol.for("react.lazy"),V=Symbol.for("react.activity"),be=Symbol.for("react.client.reference"),U=q.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,he=Object.prototype.hasOwnProperty,fe=Array.isArray,X=console.createTask?console.createTask:function(){return null};q={"react-stack-bottom-frame":function(s){return s()}};var re,we={},oe=q["react-stack-bottom-frame"].bind(q,N)(),P=X($(N)),ie={};ee.Fragment=C,ee.jsx=function(s,d,m,w,_){var k=1e4>U.recentlyCreatedOwnerStacks++;return te(s,d,m,!1,w,_,k?Error("react-stack-top-frame"):oe,k?X($(s)):P)},ee.jsxs=function(s,d,m,w,_){var k=1e4>U.recentlyCreatedOwnerStacks++;return te(s,d,m,!0,w,_,k?Error("react-stack-top-frame"):oe,k?X($(s)):P)}}()),ee}process.env.NODE_ENV==="production"?W.exports=Ae():W.exports=Ce();var e=W.exports;return({className:O="",style:J={}})=>{const[y,$]=h.useState(""),[p,N]=h.useState([]),[D,G]=h.useState([]),[T,K]=h.useState(!1),[te,L]=h.useState(null),[q,se]=h.useState(null),[ne,C]=h.useState([]),[B,de]=h.useState({likedNames:[],dislikedNames:[],patterns:{preferredLength:null,preferredStyle:null,likedKeywords:[],dislikedKeywords:[],preferredStructure:null}}),[ge,ve]=h.useState(!1),[me,Y]=h.useState(!1),[Ne,ue]=h.useState(!1),[ae,V]=h.useState(new Set),[be,U]=h.useState(new Set),[he,fe]=h.useState(new Set),[X,re]=h.useState(null),[we,oe]=h.useState(0),[P,ie]=h.useState(!1),s=100,d=()=>{const t=document.createElement("canvas"),a=t.getContext("2d");a.textBaseline="top",a.font="14px Arial",a.fillText("Usage tracking",2,2);const r=t.toDataURL(),i=navigator.userAgent,l=navigator.language,o=Intl.DateTimeFormat().resolvedOptions().timeZone,n=r+i+l+o;let c=0;for(let u=0;u<n.length;u++){const f=n.charCodeAt(u);c=(c<<5)-c+f,c=c&c}return`podcast_usage_${Math.abs(c)}`},m=()=>{const t=d(),a=new Date().toDateString(),r=`${t}_${a}`,i=localStorage.getItem(r),l=i?parseInt(i,10):0;return oe(l),l>=s?(ie(!0),!1):!0},w=(t=1)=>{const a=d(),r=new Date().toDateString(),i=`${a}_${r}`,l=localStorage.getItem(i),n=(l?parseInt(l,10):0)+t;localStorage.setItem(i,n.toString()),oe(n),n>=s&&ie(!0)};h.useEffect(()=>{m()},[]);const _=t=>{let a=t.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g," ").replace(/^the\s+/,"").trim();const r=["the","and","for","with","from","show","podcast","cast","of","in","on","at","to","a","an"],i=a.split(" "),l=i.filter(n=>n.length>2&&!r.includes(n));if(l.length===1){const n=l[0];return n.length>=6&&n.length<=15?n:n.length<6?n+"pod":k(n)}if(l.length>=2){const n=l[0],c=l[1],u=n+c;if(u.length>=6&&u.length<=15)return u;const f=k(n),E=k(c),j=f+E;if(j.length>=6&&j.length<=15)return j;if(j.length>15)return f+"-"+E}if(l.length>0){const n=k(l[0]),c=["cast","pod","show","talk"];for(const u of c){const f=n+u;if(f.length>=6&&f.length<=15)return f}if(n.length>=6&&n.length<=15)return n}const o=i[0];return o&&o.length>=3?k(o)+"pod":"podcast"+Math.random().toString(36).substring(2,5)},k=t=>{if(t.length<=8)return t;const a={business:"biz",entrepreneur:"entre",marketing:"market",finance:"fin",startup:"start",leadership:"lead",strategy:"strat",success:"win",growth:"grow",innovation:"innov",management:"manage",technology:"tech",development:"dev",digital:"digi",software:"soft",coding:"code",programming:"prog",stories:"story",journey:"path",adventure:"quest",creative:"create",entertainment:"fun",education:"learn",knowledge:"know",wisdom:"wise",lifestyle:"life",wellness:"well",fitness:"fit",health:"heal",mindset:"mind",motivation:"motive",inspiration:"inspire",american:"usa",european:"euro",international:"global",community:"comm",culture:"cult",society:"social"};if(a[t])return a[t];const r=["super","mega","ultra","micro","mini","multi"];for(const l of r)if(t.startsWith(l)&&t.length>l.length+3){const o=t.substring(l.length);if(o.length<=8)return o}const i=["ing","tion","sion","ness","ment","able","ible"];for(const l of i)if(t.endsWith(l)&&t.length>l.length+4){const o=t.substring(0,t.length-l.length);if(o.length>=4&&o.length<=8)return o}return t.length>8?t.substring(0,8):t},H=async t=>{try{const a=await fetch(`https://dns.google/resolve?name=${t}&type=A`,{method:"GET",headers:{Accept:"application/json"}});if(!a.ok)return"error";const r=await a.json();return r.Answer&&r.Answer.length>0?"taken":"available"}catch(a){return console.warn("Domain check failed:",a),"error"}},z=async t=>{const a=[...t];for(let r=0;r<a.length;r++){const i=_(a[r].name);a[r].suggestedDomain=i,a[r].domainStatus="checking",V(l=>new Set([...l,r]))}N(a);for(let r=0;r<a.length;r++){const i=`${a[r].suggestedDomain}.com`;try{const l=await H(i);N(o=>{const n=[...o];return n[r]&&(n[r].domainStatus=l),n})}catch{N(o=>{const n=[...o];return n[r]&&(n[r].domainStatus="error"),n})}finally{V(l=>{const o=new Set(l);return o.delete(r),o})}}},x=(t,a)=>{if(t.length===0)return null;const r=t.map(n=>n.name.split(" ").length),i=a.map(n=>n.name.split(" ").length),l=r.reduce((n,c)=>n+c,0)/r.length,o=i.length>0?i.reduce((n,c)=>n+c,0)/i.length:0;return l<=2&&o>2?"short":l<=4&&(o<=2||o>4)?"medium":l>4&&o<=4?"long":l<=2?"short":l<=4?"medium":"long"},F=(t,a)=>{if(t.length===0)return null;const r=t.map(l=>l.description.toLowerCase()).join(" "),i=a.map(l=>l.description.toLowerCase()).join(" ");return(r.includes("professional")||r.includes("business"))&&!i.includes("professional")&&!i.includes("business")?"professional":(r.includes("creative")||r.includes("unique"))&&!i.includes("creative")&&!i.includes("unique")?"creative":(r.includes("fun")||r.includes("playful"))&&!i.includes("fun")&&!i.includes("playful")?"playful":"descriptive"},Q=t=>{const a=[];t.forEach(i=>{const l=["the","a","an","and","or","but","in","on","at","to","for","of","with","by"],o=i.name.toLowerCase().split(/\s+/).filter(n=>n.length>2&&!l.includes(n));a.push(...o)});const r={};return a.forEach(i=>r[i]=(r[i]||0)+1),Object.entries(r).sort(([,i],[,l])=>l-i).slice(0,5).map(([i])=>i)},pe=t=>{const a=t.filter(i=>i.liked===!0),r=t.filter(i=>i.liked===!1);return{preferredLength:x(a,r),preferredStyle:F(a,r),likedKeywords:Q(a),dislikedKeywords:Q(r),preferredStructure:null}},Pe=(t,a)=>{const r=[...a.likedNames.map(n=>n.name.toLowerCase()),...a.dislikedNames.map(n=>n.name.toLowerCase()),...p.map(n=>n.name.toLowerCase())],i=`Create 4 unique, high-converting podcast names for: ${t}`;let l=`

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;r.length>0&&(l+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${r.map(n=>`- ${n}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);let o="";if(a.patterns.preferredLength&&(o+=`
Focus on ${{short:"1-2 words (punchy and memorable)",medium:"2-3 words (balanced and brandable)",long:"3-4 words (descriptive but still catchy)"}[a.patterns.preferredLength]}. `),a.patterns.preferredStyle&&(o+=`Use ${{descriptive:"clear, straightforward names that explain the content",creative:"imaginative, metaphorical, or playful names",professional:"authoritative, business-focused names",playful:"fun, energetic, engaging names"}[a.patterns.preferredStyle]||a.patterns.preferredStyle}. `),a.patterns.likedKeywords.length>0&&(o+=`
Incorporate themes similar to: ${a.patterns.likedKeywords.join(", ")}. `),a.patterns.dislikedKeywords.length>0&&(o+=`
Avoid themes like: ${a.patterns.dislikedKeywords.join(", ")}. `),a.likedNames.length>0){const n=a.likedNames.slice(-2).map(c=>c.name).join('", "');o+=`
Generate names with similar appeal to: "${n}" (but completely different concepts). `}return`${i}${l}${o}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`},xe=(t,a,r=1)=>{const i=[...a.likedNames.map(c=>c.name.toLowerCase()),...a.dislikedNames.map(c=>c.name.toLowerCase()),...p.map(c=>c.name.toLowerCase())],l=`Create ${r} unique, high-converting podcast name${r>1?"s":""} for: ${t}`;let o=`

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;i.length>0&&(o+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${i.map(c=>`- ${c}`).join(`
`)}
Do not create names similar to any of the above.`);let n="";return a.patterns.likedKeywords.length>0&&(n+=`
Incorporate themes similar to: ${a.patterns.likedKeywords.join(", ")}. `),a.patterns.dislikedKeywords.length>0&&(n+=`
Avoid themes like: ${a.patterns.dislikedKeywords.join(", ")}. `),`${l}${o}${n}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${r>1?', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}':""}]}`},ke=async(t=!1)=>{if(!y.trim()){L("Please describe what your podcast is about");return}if(!m()){L(null);return}K(!0),L(null),N([]),t?Y(!0):(C([]),ve(!1),Y(!1),ue(!1));try{const a=t?Pe(y,B):`Create 4 unique, high-converting podcast names for: ${y}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`,r=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:a}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!r.ok)throw new Error(`API request failed: ${r.status} ${r.statusText}`);const i=await r.json();if(!i.candidates||!i.candidates[0]||!i.candidates[0].content)throw new Error("Invalid response format from API");const o=i.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!o)throw new Error("No valid JSON found in API response");const n=JSON.parse(o[0]);if(!n.podcast_names||!Array.isArray(n.podcast_names))throw new Error("Invalid response structure");N(n.podcast_names),w(4),z(n.podcast_names);const c=n.podcast_names.map((u,f)=>({name:u.name,description:u.description,liked:null,timestamp:Date.now(),index:f}));C(c)}catch(a){console.error("Error generating podcast names:",a),L(a instanceof Error?a.message:"An unexpected error occurred")}finally{K(!1),Y(!1)}},Se=async(t,a)=>{try{await navigator.clipboard.writeText(t),se(a),setTimeout(()=>se(null),2e3)}catch(r){console.error("Failed to copy text:",r)}},Re=async(t,a)=>{const r=p[t];if(r){if(a){const i=window.scrollY,l=()=>window.scrollTo(0,i);window.addEventListener("scroll",l,{passive:!1}),fe(o=>new Set([...o,t])),re(`"${r.name}" added to favorites!`),setTimeout(()=>re(null),2e3),setTimeout(()=>{G(o=>o.find(n=>n.name===r.name)?o:[...o,r]),setTimeout(()=>{window.scrollTo(0,i)},0)},100),setTimeout(()=>{fe(o=>{const n=new Set(o);return n.delete(t),n}),window.removeEventListener("scroll",l),window.scrollTo(0,i)},700),de(o=>{const n={...o};return n.dislikedNames=n.dislikedNames.filter(c=>c.name!==r.name),n.likedNames.find(c=>c.name===r.name)||n.likedNames.push({name:r.name,description:r.description,liked:!0,timestamp:Date.now(),index:t}),n.patterns=pe([...n.likedNames,...n.dislikedNames]),n}),U(o=>new Set([...o,t]))}else{const i=window.scrollY;U(l=>new Set([...l,t])),de(l=>{const o={...l};return o.likedNames=o.likedNames.filter(n=>n.name!==r.name),o.dislikedNames.find(n=>n.name===r.name)||o.dislikedNames.push({name:r.name,description:r.description,liked:!1,timestamp:Date.now(),index:t}),o.patterns=pe([...o.likedNames,...o.dislikedNames]),o}),setTimeout(()=>{window.scrollTo(0,i)},0)}y.trim()&&_e(t),Ne||ue(!0)}},_e=async t=>{var a,r,i,l,o,n;if(m()){console.log(`🔄 Starting replacement generation for index ${t}`);try{const c=xe(y,B,1);console.log(`📝 Generated single name prompt for index ${t}:`,c.substring(0,100)+"..."),console.log(`🌐 Making API call for index ${t}...`);const u=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:c}]}]})});if(console.log(`📡 API response status for index ${t}:`,u.status),!u.ok)throw new Error(`Failed to generate replacement suggestion: ${u.status} ${u.statusText}`);const f=await u.json();console.log(`📦 API response data for index ${t}:`,f);const E=(o=(l=(i=(r=(a=f.candidates)==null?void 0:a[0])==null?void 0:r.content)==null?void 0:i.parts)==null?void 0:l[0])==null?void 0:o.text;if(!E)throw new Error("No content in API response");console.log(`📄 API content for index ${t}:`,E.substring(0,200)+"...");const j=E.match(/\{[\s\S]*\}/);if(!j)throw console.error(`❌ No valid JSON found in response for index ${t}:`,E),new Error("No valid JSON found in response");const le=JSON.parse(j[0]);console.log(`🔍 Parsed response for index ${t}:`,le);const A=(n=le.podcast_names)==null?void 0:n[0];if(A){if(console.log(`✅ New name generated for index ${t}:`,A),N(I=>{const b=[...I];return b[t]={name:A.name,description:A.description,suggestedDomain:A.suggestedDomain,domainStatus:"checking"},console.log(`🔄 Updated results for index ${t}:`,b[t]),b}),U(I=>{const b=new Set(I);return b.delete(t),console.log(`🗑️ Removed index ${t} from pending replacements. Remaining:`,Array.from(b)),b}),C(I=>{const b=I.filter(g=>g.index!==t);return console.log(`🧹 Cleared feedback for index ${t}. Remaining feedback:`,b),b}),A.suggestedDomain){console.log(`🌐 Checking domain availability for ${A.suggestedDomain}...`),V(b=>new Set([...b,t]));const I=await H(A.suggestedDomain);console.log(`🏷️ Domain status for ${A.suggestedDomain}:`,I),N(b=>{const g=[...b];return g[t]&&(g[t].domainStatus=I),g}),V(b=>{const g=new Set(b);return g.delete(t),g})}console.log(`🎉 Successfully completed replacement for index ${t}`)}else throw console.error(`❌ No new name found in parsed response for index ${t}:`,le),new Error("No new name found in API response")}catch(c){console.error(`❌ Error generating replacement suggestion for index ${t}:`,c),U(u=>{const f=new Set(u);return f.delete(t),console.log(`🗑️ Removed index ${t} from pending on error. Remaining:`,Array.from(f)),f}),C(u=>{const f=u.filter(E=>E.index!==t);return console.log(`🧹 Cleared feedback for index ${t} on error. Remaining:`,f),f})}}},Oe=()=>{y.trim()&&De(y)},De=async t=>{var a,r,i,l,o,n,c,u,f,E;K(!0),L(""),Y(!0);try{const j=p.filter((g,R)=>{const v=ne.find(S=>S.index===R);return(v==null?void 0:v.liked)===!0}),le=p.filter((g,R)=>{const v=ne.find(S=>S.index===R);return(v==null?void 0:v.liked)===!1}).length,A=Math.max(1,le),b=Math.min(5,j.length+A)-j.length;if(b<=0){const g=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:xe(t,B,1)}]}]})});if(!g.ok)throw new Error(`API request failed: ${g.status}`);const v=(o=(l=(i=(r=(a=(await g.json()).candidates)==null?void 0:a[0])==null?void 0:r.content)==null?void 0:i.parts)==null?void 0:l[0])==null?void 0:o.text;if(!v)throw new Error("No content received from API");const S=v.match(/\{[\s\S]*\}/);if(!S)throw new Error("No valid JSON found in response");const M=JSON.parse(S[0]);if(!M.podcast_names||!Array.isArray(M.podcast_names))throw new Error("Invalid response format");const ce=[...j,...M.podcast_names].slice(0,5);N(ce),z(ce)}else{const g=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:xe(t,B,b)}]}]})});if(!g.ok)throw new Error(`API request failed: ${g.status}`);const v=(E=(f=(u=(c=(n=(await g.json()).candidates)==null?void 0:n[0])==null?void 0:c.content)==null?void 0:u.parts)==null?void 0:f[0])==null?void 0:E.text;if(!v)throw new Error("No content received from API");const S=v.match(/\{[\s\S]*\}/);if(!S)throw new Error("No valid JSON found in response");const M=JSON.parse(S[0]);if(!M.podcast_names||!Array.isArray(M.podcast_names))throw new Error("Invalid response format");const ce=[...j,...M.podcast_names];N(ce),z(ce)}C(g=>g.filter(R=>{const v=p[R.index];return j.some(S=>S.name===(v==null?void 0:v.name))})),C(g=>g.map(R=>{const v=p[R.index],S=j.findIndex(M=>M.name===(v==null?void 0:v.name));return S>=0?{...R,index:S}:R}).filter(R=>R.index>=0))}catch(j){console.error("Error generating refined names:",j),L(j instanceof Error?j.message:"Failed to generate refined names. Please try again.")}finally{K(!1),Y(!1)}},Ee=t=>{t.preventDefault(),ke()},Te=t=>{t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),ke())};return e.jsx("div",{className:`podcast-name-generator ${O}`,style:J,children:e.jsxs("div",{className:"generator-container",children:[e.jsxs("div",{className:"header-section",children:[e.jsx("h1",{className:"main-title",children:"Free Podcast Name Generator"}),e.jsx("h2",{className:"main-subtitle",children:"Create the Perfect Name for Your Podcast in Seconds"})]}),e.jsxs("div",{className:"benefits-section",children:[e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"100% Free Forever"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"No Sign-up Required"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"Instant Results"})]})]}),P&&e.jsx("div",{className:"limit-reached-banner",children:e.jsxs("div",{className:"limit-content",children:[e.jsx("span",{className:"limit-icon",children:"⚠️"}),e.jsx("div",{className:"limit-text",children:e.jsx("p",{children:"You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below."})})]})}),p.length===0&&e.jsx("div",{className:"initial-input-section",children:e.jsx("form",{onSubmit:Ee,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:y,onChange:t=>$(t.target.value),onKeyPress:Te,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:T}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:T||!y.trim()||P,className:`generate-button ${P?"disabled":""}`,children:T?"Generating...":P?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star star-partial",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})}),te&&e.jsxs("div",{className:"error-message",children:[e.jsx("span",{className:"error-icon",children:"⚠️"}),te]}),X&&e.jsxs("div",{className:"success-message",children:[e.jsx("span",{className:"success-icon",children:"✨"}),X]}),T&&e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:me?"Generating better names based on your preferences...":"Generating creative podcast names..."})]}),p.length>0&&e.jsxs("div",{className:"results-container",children:[D.length>0&&e.jsxs("div",{className:"favorites-section",children:[e.jsxs("div",{className:"favorites-header",children:[e.jsxs("h3",{children:["🏆 Your Winning Podcast Names (",D.length,")"]}),e.jsx("p",{className:"favorites-subtitle",children:"Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!"})]}),e.jsx("div",{className:"favorites-grid",children:D.map((t,a)=>e.jsxs("div",{className:"favorite-card",children:[e.jsxs("div",{className:"favorite-content",children:[e.jsx("h4",{className:"favorite-name",children:t.name}),e.jsx("p",{className:"favorite-description",children:t.description}),t.suggestedDomain&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsx("span",{className:"domain-name",children:t.suggestedDomain}),e.jsx("span",{className:`domain-status ${t.domainStatus}`,children:t.domainStatus==="available"?"✅ Available":t.domainStatus==="taken"?"❌ Taken":t.domainStatus==="error"?"⚠️ Check manually":"🔍 Checking..."})]})]}),e.jsx("div",{className:"favorite-actions",children:e.jsx("button",{onClick:()=>Se(t.name,-1),className:"copy-button small",title:"Copy to clipboard",children:"📋 Copy"})})]},`fav-${a}`))})]}),e.jsxs("div",{className:"input-section-simple",children:[e.jsx("div",{className:"input-help-message-simple",children:e.jsxs("p",{className:"input-sub-description",children:["💡 Want different suggestions? Update your description below - ",e.jsx("strong",{children:"your favorites will stay safe!"})]})}),e.jsx("form",{onSubmit:Ee,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:y,onChange:t=>$(t.target.value),onKeyPress:Te,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:T}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:T||!y.trim()||P,className:`generate-button ${P?"disabled":""}`,children:T?"Generating...":P?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star star-partial",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})]}),e.jsxs("div",{className:"suggestions-section",children:[e.jsx("div",{className:"suggestions-header",children:e.jsx("h3",{children:"🎯 Current Suggestions"})}),e.jsx("div",{className:"onboarding-banner",children:e.jsxs("div",{className:"onboarding-content",children:[e.jsx("span",{className:"onboarding-icon",children:"💡"}),e.jsxs("div",{className:"onboarding-text",children:[e.jsx("strong",{children:"Smart AI Learning:"})," The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."]})]})}),e.jsx("div",{className:"results-grid",children:p.map((t,a)=>{const r=ne.find(c=>c.index===a),i=(r==null?void 0:r.liked)===!0,l=(r==null?void 0:r.liked)===!1,o=be.has(a),n=ae.has(a);return e.jsxs("div",{className:`result-card ${i?"liked":""} ${l?"disliked":""} ${o?"pending":""} ${he.has(a)?"flying-to-favorites":""}`,style:{opacity:o?.6:1,pointerEvents:o?"none":"auto"},children:[e.jsxs("div",{className:"result-header",children:[e.jsx("h4",{className:"result-name",children:o?i?"Generating new suggestion...":"Generating better suggestion...":t.name}),e.jsxs("div",{className:"result-actions",children:[e.jsxs("div",{className:"feedback-buttons",children:[e.jsx("button",{onClick:()=>Re(a,!0),className:`feedback-button like-button ${i?"active":""}`,title:"I like this name",disabled:o,children:"👍"}),e.jsx("button",{onClick:()=>Re(a,!1),className:`feedback-button dislike-button ${l?"active":""} ${o?"loading":""}`,title:o?"Generating replacement...":"I don't like this name",disabled:o,children:o?"🔄":"👎"})]}),e.jsx("button",{onClick:()=>Se(t.name,a),className:"copy-button",title:"Copy podcast name",disabled:o,children:q===a?"✓ Copied!":"📋 Copy"})]})]}),e.jsx("p",{className:"result-description",children:o?i?"Added to favorites! Generating a new suggestion...":"Creating a better suggestion based on your preferences...":t.description}),t.suggestedDomain&&!n&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsxs("code",{className:"domain-text",children:[t.suggestedDomain,".com"]}),e.jsxs("span",{className:`domain-status ${t.domainStatus}`,children:[(t.domainStatus==="checking"||ae.has(a))&&"⏳ Checking...",t.domainStatus==="available"&&"✅ Available",t.domainStatus==="taken"&&"❌ Taken",t.domainStatus==="error"&&"⚠️ Check manually"]})]})]},a)})})]}),ge&&!T&&e.jsxs("div",{className:"refinement-section",children:[e.jsx("div",{className:"refinement-info",children:e.jsx("p",{children:"💡 I'll keep your liked names and replace the disliked ones with better suggestions!"})}),e.jsx("button",{onClick:Oe,className:"refinement-button",disabled:T,children:me?"🔄 Refining...":"🎯 Replace Disliked Names"})]})]})]})})}});

(function(u,y){typeof exports=="object"&&typeof module<"u"?module.exports=y(require("react")):typeof define=="function"&&define.amd?define(["react"],y):(u=typeof globalThis<"u"?globalThis:u||self,u.PodcastNameGenerator=y(u.React))})(this,function(u){"use strict";var y={exports:{}},T={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var se;function ae(){if(se)return T;se=1;var N=Symbol.for("react.transitional.element"),R=Symbol.for("react.fragment");function x(b,h,p){var k=null;if(p!==void 0&&(k=""+p),h.key!==void 0&&(k=""+h.key),"key"in h){p={};for(var f in h)f!=="key"&&(p[f]=h[f])}else p=h;return h=p.ref,{$$typeof:N,type:b,key:k,ref:h!==void 0?h:null,props:p}}return T.Fragment=R,T.jsx=x,T.jsxs=x,T}var _={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var re;function oe(){return re||(re=1,process.env.NODE_ENV!=="production"&&function(){function N(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===$?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case v:return"Fragment";case Q:return"Profiler";case J:return"StrictMode";case C:return"Suspense";case ee:return"SuspenseList";case B:return"Activity"}if(typeof e=="object")switch(typeof e.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),e.$$typeof){case A:return"Portal";case q:return(e.displayName||"Context")+".Provider";case K:return(e._context.displayName||"Context")+".Consumer";case O:var o=e.render;return e=e.displayName,e||(e=o.displayName||o.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case te:return o=e.displayName||null,o!==null?o:N(e.type)||"Memo";case D:o=e._payload,e=e._init;try{return N(e(o))}catch{}}return null}function R(e){return""+e}function x(e){try{R(e);var o=!1}catch{o=!0}if(o){o=console;var l=o.error,m=typeof Symbol=="function"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object";return l.call(o,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",m),R(e)}}function b(e){if(e===v)return"<>";if(typeof e=="object"&&e!==null&&e.$$typeof===D)return"<...>";try{var o=N(e);return o?"<"+o+">":"<...>"}catch{return"<...>"}}function h(){var e=I.A;return e===null?null:e.getOwner()}function p(){return Error("react-stack-top-frame")}function k(e){if(F.call(e,"key")){var o=Object.getOwnPropertyDescriptor(e,"key").get;if(o&&o.isReactWarning)return!1}return e.key!==void 0}function f(e,o){function l(){V||(V=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",o))}l.isReactWarning=!0,Object.defineProperty(e,"key",{get:l,configurable:!0})}function z(){var e=N(this.type);return H[e]||(H[e]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),e=this.props.ref,e!==void 0?e:null}function U(e,o,l,m,j,g,G,a){return l=g.ref,e={$$typeof:P,type:e,key:o,props:g,_owner:j},(l!==void 0?l:null)!==null?Object.defineProperty(e,"ref",{enumerable:!1,get:z}):Object.defineProperty(e,"ref",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:G}),Object.defineProperty(e,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:a}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function S(e,o,l,m,j,g,G,a){var t=o.children;if(t!==void 0)if(m)if(ne(t)){for(m=0;m<t.length;m++)W(t[m]);Object.freeze&&Object.freeze(t)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else W(t);if(F.call(o,"key")){t=N(e);var n=Object.keys(o).filter(function(i){return i!=="key"});m=0<n.length?"{key: someKey, "+n.join(": ..., ")+": ...}":"{key: someKey}",Z[t+m]||(n=0<n.length?"{"+n.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,m,t,n,t),Z[t+m]=!0)}if(t=null,l!==void 0&&(x(l),t=""+l),k(o)&&(x(o.key),t=""+o.key),"key"in o){l={};for(var r in o)r!=="key"&&(l[r]=o[r])}else l=o;return t&&f(l,typeof e=="function"?e.displayName||e.name||"Unknown":e),U(e,t,g,j,h(),l,G,a)}function W(e){typeof e=="object"&&e!==null&&e.$$typeof===P&&e._store&&(e._store.validated=1)}var w=u,P=Symbol.for("react.transitional.element"),A=Symbol.for("react.portal"),v=Symbol.for("react.fragment"),J=Symbol.for("react.strict_mode"),Q=Symbol.for("react.profiler"),K=Symbol.for("react.consumer"),q=Symbol.for("react.context"),O=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),ee=Symbol.for("react.suspense_list"),te=Symbol.for("react.memo"),D=Symbol.for("react.lazy"),B=Symbol.for("react.activity"),$=Symbol.for("react.client.reference"),I=w.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F=Object.prototype.hasOwnProperty,ne=Array.isArray,L=console.createTask?console.createTask:function(){return null};w={"react-stack-bottom-frame":function(e){return e()}};var V,H={},X=w["react-stack-bottom-frame"].bind(w,p)(),Y=L(b(p)),Z={};_.Fragment=v,_.jsx=function(e,o,l,m,j){var g=1e4>I.recentlyCreatedOwnerStacks++;return S(e,o,l,!1,m,j,g?Error("react-stack-top-frame"):X,g?L(b(e)):Y)},_.jsxs=function(e,o,l,m,j){var g=1e4>I.recentlyCreatedOwnerStacks++;return S(e,o,l,!0,m,j,g?Error("react-stack-top-frame"):X,g?L(b(e)):Y)}}()),_}process.env.NODE_ENV==="production"?y.exports=ae():y.exports=oe();var s=y.exports;return({apiKey:N="AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",className:R="",style:x={}})=>{const[b,h]=u.useState(""),[p,k]=u.useState([]),[f,z]=u.useState(!1),[U,S]=u.useState(null),[W,w]=u.useState(null),[P,A]=u.useState([]),[v,J]=u.useState({likedNames:[],dislikedNames:[],patterns:{preferredLength:null,preferredStyle:null,likedKeywords:[],dislikedKeywords:[],preferredStructure:null},generationRound:0}),[Q,K]=u.useState(!1),[q,O]=u.useState(!1),[C,ee]=u.useState([]),[te,D]=u.useState(!0),[B,$]=u.useState(!1),[I,F]=u.useState(new Set),ne=a=>a.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"").replace(/^the/,"").substring(0,50),L=async a=>{try{const t=await fetch(`https://dns.google/resolve?name=${a}&type=A`,{method:"GET",headers:{Accept:"application/json"}});if(!t.ok)return"error";const n=await t.json();return n.Answer&&n.Answer.length>0?"taken":"available"}catch(t){return console.warn("Domain check failed:",t),"error"}},V=async a=>{const t=[...a];for(let n=0;n<t.length;n++){const r=ne(t[n].name);t[n].suggestedDomain=r,t[n].domainStatus="checking",F(i=>new Set([...i,n]))}k(t);for(let n=0;n<t.length;n++){const r=`${t[n].suggestedDomain}.com`;try{const i=await L(r);k(d=>{const c=[...d];return c[n]&&(c[n].domainStatus=i),c})}catch{k(d=>{const c=[...d];return c[n]&&(c[n].domainStatus="error"),c})}finally{F(i=>{const d=new Set(i);return d.delete(n),d})}}},H=(a,t)=>{if(a.length===0)return null;const n=a.map(c=>c.name.split(" ").length),r=t.map(c=>c.name.split(" ").length),i=n.reduce((c,M)=>c+M,0)/n.length,d=r.length>0?r.reduce((c,M)=>c+M,0)/r.length:0;return i<=2&&d>2?"short":i<=4&&(d<=2||d>4)?"medium":i>4&&d<=4?"long":i<=2?"short":i<=4?"medium":"long"},X=(a,t)=>{if(a.length===0)return null;const n=a.map(i=>i.description.toLowerCase()).join(" "),r=t.map(i=>i.description.toLowerCase()).join(" ");return(n.includes("professional")||n.includes("business"))&&!r.includes("professional")&&!r.includes("business")?"professional":(n.includes("creative")||n.includes("unique"))&&!r.includes("creative")&&!r.includes("unique")?"creative":(n.includes("fun")||n.includes("playful"))&&!r.includes("fun")&&!r.includes("playful")?"playful":"descriptive"},Y=a=>{const t=[];a.forEach(r=>{const i=["the","a","an","and","or","but","in","on","at","to","for","of","with","by"],d=r.name.toLowerCase().split(/\s+/).filter(c=>c.length>2&&!i.includes(c));t.push(...d)});const n={};return t.forEach(r=>n[r]=(n[r]||0)+1),Object.entries(n).sort(([,r],[,i])=>i-r).slice(0,5).map(([r])=>r)},Z=a=>{const t=a.filter(r=>r.liked===!0),n=a.filter(r=>r.liked===!1);return{preferredLength:H(t,n),preferredStyle:X(t,n),likedKeywords:Y(t),dislikedKeywords:Y(n),preferredStructure:null}},e=(a,t)=>{const n=`Create 5 high-converting and catchy podcast names based on the following user input: ${a}.`;let r="";if(t.patterns.preferredLength){const i={short:"1-2 words",medium:"3-4 words",long:"5+ words"};r+=`Focus on ${t.patterns.preferredLength} names (${i[t.patterns.preferredLength]}). `}if(t.patterns.preferredStyle&&(r+=`Use a ${t.patterns.preferredStyle} style. `),t.patterns.likedKeywords.length>0&&(r+=`Incorporate concepts similar to: ${t.patterns.likedKeywords.join(", ")}. `),t.patterns.dislikedKeywords.length>0&&(r+=`Avoid concepts like: ${t.patterns.dislikedKeywords.join(", ")}. `),t.likedNames.length>0){const i=t.likedNames.slice(-2).map(d=>d.name).join('", "');r+=`Generate names with similar appeal to: "${i}". `}return`${n} ${r} Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`},o=async(a=!1)=>{if(!b.trim()){S("Please describe what your podcast is about");return}z(!0),S(null),k([]),a?O(!0):(A([]),K(!1),O(!1),D(!0),$(!1));try{const t=a?e(b,v):`Create 5 high-converting and catchy podcast names based on the following user input: ${b}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`,n=await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${N}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:t}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!n.ok)throw new Error(`API request failed: ${n.status} ${n.statusText}`);const r=await n.json();if(!r.candidates||!r.candidates[0]||!r.candidates[0].content)throw new Error("Invalid response format from API");const d=r.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!d)throw new Error("No valid JSON found in API response");const c=JSON.parse(d[0]);if(!c.podcast_names||!Array.isArray(c.podcast_names))throw new Error("Invalid response structure");k(c.podcast_names),V(c.podcast_names),ee(E=>[...E,c.podcast_names]),J(E=>({...E,generationRound:E.generationRound+1}));const M=c.podcast_names.map((E,ie)=>({name:E.name,description:E.description,liked:null,timestamp:Date.now(),index:ie}));A(M)}catch(t){console.error("Error generating podcast names:",t),S(t instanceof Error?t.message:"An unexpected error occurred")}finally{z(!1),O(!1)}},l=async(a,t)=>{try{await navigator.clipboard.writeText(a),w(t),setTimeout(()=>w(null),2e3)}catch(n){console.error("Failed to copy text:",n)}},m=(a,t)=>{const n=[...P];n[a]={...n[a],liked:t,timestamp:Date.now()},A(n);const r={...v},i=n[a];t?(r.dislikedNames=r.dislikedNames.filter(c=>c.name!==i.name),r.likedNames.find(c=>c.name===i.name)||r.likedNames.push(i)):(r.likedNames=r.likedNames.filter(c=>c.name!==i.name),r.dislikedNames.find(c=>c.name===i.name)||r.dislikedNames.push(i)),r.patterns=Z([...r.likedNames,...r.dislikedNames]),J(r),r.likedNames.length+r.dislikedNames.length>=2&&K(!0),B||($(!0),D(!1))},j=()=>{o(!0)},g=a=>{a.preventDefault(),o()},G=a=>{a.key==="Enter"&&!a.shiftKey&&(a.preventDefault(),o())};return s.jsx("div",{className:`podcast-name-generator ${R}`,style:x,children:s.jsxs("div",{className:"generator-container",children:[s.jsx("h2",{className:"generator-title",children:"Podcast Name Generator"}),s.jsx("p",{className:"generator-subtitle",children:"Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI"}),te&&s.jsx("div",{className:"onboarding-banner",children:s.jsxs("div",{className:"onboarding-content",children:[s.jsx("span",{className:"onboarding-icon",children:"💡"}),s.jsxs("div",{className:"onboarding-text",children:[s.jsx("strong",{children:"Pro Tip:"})," After generating names, use the 👍👎 buttons to teach the AI your preferences. It will then generate better, more personalized suggestions just for you!"]})]})}),s.jsx("form",{onSubmit:g,className:"input-form",children:s.jsxs("div",{className:"input-container",children:[s.jsx("textarea",{value:b,onChange:a=>h(a.target.value),onKeyPress:G,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:f}),s.jsx("button",{type:"submit",disabled:f||!b.trim(),className:"generate-button",children:f?"Generating...":"Generate Names"})]})}),U&&s.jsxs("div",{className:"error-message",children:[s.jsx("span",{className:"error-icon",children:"⚠️"}),U]}),f&&s.jsxs("div",{className:"loading-container",children:[s.jsx("div",{className:"loading-spinner"}),s.jsx("p",{children:q?"Generating better names based on your preferences...":"Generating creative podcast names..."})]}),p.length>0&&s.jsxs("div",{className:"results-container",children:[s.jsxs("h3",{className:"results-title",children:[v.generationRound>1?"Refined Podcast Name Suggestions":"Your Podcast Name Suggestions",C.length>1&&s.jsxs("span",{className:"generation-counter",children:[" (Round ",v.generationRound,")"]})]}),v.generationRound>1&&s.jsxs("p",{className:"refinement-info",children:["✨ These names are tailored based on your preferences from ",C.length-1," previous round",C.length>2?"s":""]}),v.generationRound===1&&!B&&s.jsx("div",{className:"feedback-hint",children:s.jsxs("div",{className:"feedback-hint-content",children:[s.jsx("span",{className:"feedback-hint-icon",children:"👆"}),s.jsxs("p",{children:[s.jsx("strong",{children:"Like what you see?"})," Use the 👍👎 buttons below to rate each name. The AI will learn your style and generate even better suggestions!"]})]})}),s.jsx("div",{className:"results-grid",children:p.map((a,t)=>{const n=P.find(d=>d.index===t),r=(n==null?void 0:n.liked)===!0,i=(n==null?void 0:n.liked)===!1;return s.jsxs("div",{className:`result-card ${r?"liked":""} ${i?"disliked":""}`,children:[s.jsxs("div",{className:"result-header",children:[s.jsx("h4",{className:"result-name",children:a.name}),s.jsxs("div",{className:"result-actions",children:[s.jsxs("div",{className:"feedback-buttons",children:[s.jsx("button",{onClick:()=>m(t,!0),className:`feedback-button like-button ${r?"active":""}`,title:"I like this name",disabled:f,children:"👍"}),s.jsx("button",{onClick:()=>m(t,!1),className:`feedback-button dislike-button ${i?"active":""}`,title:"I don't like this name",disabled:f,children:"👎"})]}),s.jsx("button",{onClick:()=>l(a.name,t),className:"copy-button",title:"Copy podcast name",children:W===t?"✓ Copied!":"📋 Copy"})]})]}),s.jsx("p",{className:"result-description",children:a.description}),a.suggestedDomain&&s.jsxs("div",{className:"domain-info",children:[s.jsxs("div",{className:"domain-name",children:[s.jsx("span",{className:"domain-label",children:"Domain:"}),s.jsxs("code",{className:"domain-text",children:[a.suggestedDomain,".com"]})]}),s.jsxs("div",{className:`domain-status ${a.domainStatus}`,children:[(a.domainStatus==="checking"||I.has(t))&&s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"domain-spinner",children:"⏳"}),s.jsx("span",{children:"Checking..."})]}),a.domainStatus==="available"&&s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"domain-icon available",children:"✅"}),s.jsx("span",{children:"Available"})]}),a.domainStatus==="taken"&&s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"domain-icon taken",children:"❌"}),s.jsx("span",{children:"Taken"})]}),a.domainStatus==="error"&&s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"domain-icon error",children:"⚠️"}),s.jsx("span",{children:"Check manually"})]})]})]})]},t)})}),Q&&!f&&s.jsxs("div",{className:"refinement-section",children:[s.jsx("div",{className:"refinement-info",children:s.jsx("p",{children:"💡 Based on your feedback, I can generate better names that match your preferences!"})}),s.jsx("button",{onClick:j,className:"refinement-button",disabled:f,children:"🎯 Generate Better Names"})]})]})]})})}});

(function(h,F){typeof exports=="object"&&typeof module<"u"?module.exports=F(require("react")):typeof define=="function"&&define.amd?define(["react"],F):(h=typeof globalThis<"u"?globalThis:h||self,h.PodcastNameGenerator=F(h.React))})(this,function(h){"use strict";var F={exports:{}},H={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xe;function Te(){if(xe)return H;xe=1;var _=Symbol.for("react.transitional.element"),z=Symbol.for("react.fragment");function x(L,p,v){var P=null;if(v!==void 0&&(P=""+v),p.key!==void 0&&(P=""+p.key),"key"in p){v={};for(var G in p)G!=="key"&&(v[G]=p[G])}else v=p;return p=v.ref,{$$typeof:_,type:L,key:P,ref:p!==void 0?p:null,props:v}}return H.Fragment=z,H.jsx=x,H.jsxs=x,H}var X={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var je;function Ce(){return je||(je=1,process.env.NODE_ENV!=="production"&&function(){function _(s){if(s==null)return null;if(typeof s=="function")return s.$$typeof===be?null:s.displayName||s.name||null;if(typeof s=="string")return s;switch(s){case M:return"Fragment";case de:return"Profiler";case K:return"StrictMode";case $:return"Suspense";case Ne:return"SuspenseList";case q:return"Activity"}if(typeof s=="object")switch(typeof s.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),s.$$typeof){case ee:return"Portal";case ve:return(s.displayName||"Context")+".Provider";case ge:return(s._context.displayName||"Context")+".Consumer";case me:var d=s.render;return s=s.displayName,s||(s=d.displayName||d.name||"",s=s!==""?"ForwardRef("+s+")":"ForwardRef"),s;case ue:return d=s.displayName||null,d!==null?d:_(s.type)||"Memo";case te:d=s._payload,s=s._init;try{return _(s(d))}catch{}}return null}function z(s){return""+s}function x(s){try{z(s);var d=!1}catch{d=!0}if(d){d=console;var m=d.error,f=typeof Symbol=="function"&&Symbol.toStringTag&&s[Symbol.toStringTag]||s.constructor.name||"Object";return m.call(d,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",f),z(s)}}function L(s){if(s===M)return"<>";if(typeof s=="object"&&s!==null&&s.$$typeof===te)return"<...>";try{var d=_(s);return d?"<"+d+">":"<...>"}catch{return"<...>"}}function p(){var s=B.A;return s===null?null:s.getOwner()}function v(){return Error("react-stack-top-frame")}function P(s){if(he.call(s,"key")){var d=Object.getOwnPropertyDescriptor(s,"key").get;if(d&&d.isReactWarning)return!1}return s.key!==void 0}function G(s,d){function m(){ne||(ne=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",d))}m.isReactWarning=!0,Object.defineProperty(s,"key",{get:m,configurable:!0})}function E(){var s=_(this.type);return C[s]||(C[s]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),s=this.props.ref,s!==void 0?s:null}function Y(s,d,m,f,T,y,oe,le){return m=y.ref,s={$$typeof:Z,type:s,key:d,props:y,_owner:T},(m!==void 0?m:null)!==null?Object.defineProperty(s,"ref",{enumerable:!1,get:E}):Object.defineProperty(s,"ref",{enumerable:!1,value:null}),s._store={},Object.defineProperty(s._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(s,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(s,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:oe}),Object.defineProperty(s,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:le}),Object.freeze&&(Object.freeze(s.props),Object.freeze(s)),s}function Q(s,d,m,f,T,y,oe,le){var N=d.children;if(N!==void 0)if(f)if(se(N)){for(f=0;f<N.length;f++)O(N[f]);Object.freeze&&Object.freeze(N)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else O(N);if(he.call(d,"key")){N=_(s);var I=Object.keys(d).filter(function(fe){return fe!=="key"});f=0<I.length?"{key: someKey, "+I.join(": ..., ")+": ...}":"{key: someKey}",ie[N+f]||(I=0<I.length?"{"+I.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,f,N,I,N),ie[N+f]=!0)}if(N=null,m!==void 0&&(x(m),N=""+m),P(d)&&(x(d.key),N=""+d.key),"key"in d){m={};for(var V in d)V!=="key"&&(m[V]=d[V])}else m=d;return N&&G(m,typeof s=="function"?s.displayName||s.name||"Unknown":s),Y(s,N,y,T,p(),m,oe,le)}function O(s){typeof s=="object"&&s!==null&&s.$$typeof===Z&&s._store&&(s._store.validated=1)}var J=h,Z=Symbol.for("react.transitional.element"),ee=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),K=Symbol.for("react.strict_mode"),de=Symbol.for("react.profiler"),ge=Symbol.for("react.consumer"),ve=Symbol.for("react.context"),me=Symbol.for("react.forward_ref"),$=Symbol.for("react.suspense"),Ne=Symbol.for("react.suspense_list"),ue=Symbol.for("react.memo"),te=Symbol.for("react.lazy"),q=Symbol.for("react.activity"),be=Symbol.for("react.client.reference"),B=J.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,he=Object.prototype.hasOwnProperty,se=Array.isArray,pe=console.createTask?console.createTask:function(){return null};J={"react-stack-bottom-frame":function(s){return s()}};var ne,C={},ae=J["react-stack-bottom-frame"].bind(J,v)(),re=pe(L(v)),ie={};X.Fragment=M,X.jsx=function(s,d,m,f,T){var y=1e4>B.recentlyCreatedOwnerStacks++;return Q(s,d,m,!1,f,T,y?Error("react-stack-top-frame"):ae,y?pe(L(s)):re)},X.jsxs=function(s,d,m,f,T){var y=1e4>B.recentlyCreatedOwnerStacks++;return Q(s,d,m,!0,f,T,y?Error("react-stack-top-frame"):ae,y?pe(L(s)):re)}}()),X}process.env.NODE_ENV==="production"?F.exports=Te():F.exports=Ce();var e=F.exports;return({className:_="",style:z={}})=>{const[x,L]=h.useState(""),[p,v]=h.useState([]),[P,G]=h.useState([]),[E,Y]=h.useState(!1),[Q,O]=h.useState(null),[J,Z]=h.useState(null),[ee,M]=h.useState([]),[K,de]=h.useState({likedNames:[],dislikedNames:[],patterns:{preferredLength:null,preferredStyle:null,likedKeywords:[],dislikedKeywords:[],preferredStructure:null}}),[ge,ve]=h.useState(!1),[me,$]=h.useState(!1),[Ne,ue]=h.useState(!1),[te,q]=h.useState(new Set),[be,B]=h.useState(new Set),[he,se]=h.useState(new Set),[pe,ne]=h.useState(0),[C,ae]=h.useState(!1),re=100,ie=()=>{const t=document.createElement("canvas"),n=t.getContext("2d");n.textBaseline="top",n.font="14px Arial",n.fillText("Usage tracking",2,2);const a=t.toDataURL(),o=navigator.userAgent,i=navigator.language,l=Intl.DateTimeFormat().resolvedOptions().timeZone,r=a+o+i+l;let c=0;for(let u=0;u<r.length;u++){const j=r.charCodeAt(u);c=(c<<5)-c+j,c=c&c}return`podcast_usage_${Math.abs(c)}`},s=()=>{const t=ie(),n=new Date().toDateString(),a=`${t}_${n}`,o=localStorage.getItem(a),i=o?parseInt(o,10):0;return ne(i),i>=re?(ae(!0),!1):!0},d=(t=1)=>{const n=ie(),a=new Date().toDateString(),o=`${n}_${a}`,i=localStorage.getItem(o),r=(i?parseInt(i,10):0)+t;localStorage.setItem(o,r.toString()),ne(r),r>=re&&ae(!0)};h.useEffect(()=>{s()},[]);const m=t=>{let n=t.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g," ").replace(/^the\s+/,"").trim();const a=["the","and","for","with","from","show","podcast","cast","of","in","on","at","to","a","an"],o=n.split(" "),i=o.filter(r=>r.length>2&&!a.includes(r));if(i.length===1){const r=i[0];return r.length>=6&&r.length<=15?r:r.length<6?r+"pod":f(r)}if(i.length>=2){const r=i[0],c=i[1],u=r+c;if(u.length>=6&&u.length<=15)return u;const j=f(r),U=f(c),b=j+U;if(b.length>=6&&b.length<=15)return b;if(b.length>15)return j+"-"+U}if(i.length>0){const r=f(i[0]),c=["cast","pod","show","talk"];for(const u of c){const j=r+u;if(j.length>=6&&j.length<=15)return j}if(r.length>=6&&r.length<=15)return r}const l=o[0];return l&&l.length>=3?f(l)+"pod":"podcast"+Math.random().toString(36).substring(2,5)},f=t=>{if(t.length<=8)return t;const n={business:"biz",entrepreneur:"entre",marketing:"market",finance:"fin",startup:"start",leadership:"lead",strategy:"strat",success:"win",growth:"grow",innovation:"innov",management:"manage",technology:"tech",development:"dev",digital:"digi",software:"soft",coding:"code",programming:"prog",stories:"story",journey:"path",adventure:"quest",creative:"create",entertainment:"fun",education:"learn",knowledge:"know",wisdom:"wise",lifestyle:"life",wellness:"well",fitness:"fit",health:"heal",mindset:"mind",motivation:"motive",inspiration:"inspire",american:"usa",european:"euro",international:"global",community:"comm",culture:"cult",society:"social"};if(n[t])return n[t];const a=["super","mega","ultra","micro","mini","multi"];for(const i of a)if(t.startsWith(i)&&t.length>i.length+3){const l=t.substring(i.length);if(l.length<=8)return l}const o=["ing","tion","sion","ness","ment","able","ible"];for(const i of o)if(t.endsWith(i)&&t.length>i.length+4){const l=t.substring(0,t.length-i.length);if(l.length>=4&&l.length<=8)return l}return t.length>8?t.substring(0,8):t},T=async t=>{try{const n=await fetch(`https://dns.google/resolve?name=${t}&type=A`,{method:"GET",headers:{Accept:"application/json"}});if(!n.ok)return"error";const a=await n.json();return a.Answer&&a.Answer.length>0?"taken":"available"}catch(n){return console.warn("Domain check failed:",n),"error"}},y=async t=>{const n=[...t];for(let a=0;a<n.length;a++){const o=m(n[a].name);n[a].suggestedDomain=o,n[a].domainStatus="checking",q(i=>new Set([...i,a]))}v(n);for(let a=0;a<n.length;a++){const o=`${n[a].suggestedDomain}.com`;try{const i=await T(o);v(l=>{const r=[...l];return r[a]&&(r[a].domainStatus=i),r})}catch{v(l=>{const r=[...l];return r[a]&&(r[a].domainStatus="error"),r})}finally{q(i=>{const l=new Set(i);return l.delete(a),l})}}},oe=(t,n)=>{if(t.length===0)return null;const a=t.map(r=>r.name.split(" ").length),o=n.map(r=>r.name.split(" ").length),i=a.reduce((r,c)=>r+c,0)/a.length,l=o.length>0?o.reduce((r,c)=>r+c,0)/o.length:0;return i<=2&&l>2?"short":i<=4&&(l<=2||l>4)?"medium":i>4&&l<=4?"long":i<=2?"short":i<=4?"medium":"long"},le=(t,n)=>{if(t.length===0)return null;const a=t.map(i=>i.description.toLowerCase()).join(" "),o=n.map(i=>i.description.toLowerCase()).join(" ");return(a.includes("professional")||a.includes("business"))&&!o.includes("professional")&&!o.includes("business")?"professional":(a.includes("creative")||a.includes("unique"))&&!o.includes("creative")&&!o.includes("unique")?"creative":(a.includes("fun")||a.includes("playful"))&&!o.includes("fun")&&!o.includes("playful")?"playful":"descriptive"},N=t=>{const n=[];t.forEach(o=>{const i=["the","a","an","and","or","but","in","on","at","to","for","of","with","by"],l=o.name.toLowerCase().split(/\s+/).filter(r=>r.length>2&&!i.includes(r));n.push(...l)});const a={};return n.forEach(o=>a[o]=(a[o]||0)+1),Object.entries(a).sort(([,o],[,i])=>i-o).slice(0,5).map(([o])=>o)},I=t=>{const n=t.filter(o=>o.liked===!0),a=t.filter(o=>o.liked===!1);return{preferredLength:oe(n,a),preferredStyle:le(n,a),likedKeywords:N(n),dislikedKeywords:N(a),preferredStructure:null}},V=(t,n)=>{const a=[...n.likedNames.map(r=>r.name.toLowerCase()),...n.dislikedNames.map(r=>r.name.toLowerCase()),...p.map(r=>r.name.toLowerCase())],o=`Create 4 unique, high-converting podcast names for: ${t}`;let i=`

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;a.length>0&&(i+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${a.map(r=>`- ${r}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);let l="";if(n.patterns.preferredLength&&(l+=`
Focus on ${{short:"1-2 words (punchy and memorable)",medium:"2-3 words (balanced and brandable)",long:"3-4 words (descriptive but still catchy)"}[n.patterns.preferredLength]}. `),n.patterns.preferredStyle&&(l+=`Use ${{descriptive:"clear, straightforward names that explain the content",creative:"imaginative, metaphorical, or playful names",professional:"authoritative, business-focused names",playful:"fun, energetic, engaging names"}[n.patterns.preferredStyle]||n.patterns.preferredStyle}. `),n.patterns.likedKeywords.length>0&&(l+=`
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `),n.patterns.dislikedKeywords.length>0&&(l+=`
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `),n.likedNames.length>0){const r=n.likedNames.slice(-2).map(c=>c.name).join('", "');l+=`
Generate names with similar appeal to: "${r}" (but completely different concepts). `}return`${o}${i}${l}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`},fe=(t,n,a=1)=>{const o=[...n.likedNames.map(c=>c.name.toLowerCase()),...n.dislikedNames.map(c=>c.name.toLowerCase()),...p.map(c=>c.name.toLowerCase())],i=`Create ${a} unique, high-converting podcast name${a>1?"s":""} for: ${t}`;let l=`

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;o.length>0&&(l+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${o.map(c=>`- ${c}`).join(`
`)}
Do not create names similar to any of the above.`);let r="";return n.patterns.likedKeywords.length>0&&(r+=`
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `),n.patterns.dislikedKeywords.length>0&&(r+=`
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `),`${i}${l}${r}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works"}${a>1?', {"name": "Unique Name 2", "description": "Why this works"}':""}]}`},we=async(t=!1)=>{if(!x.trim()){O("Please describe what your podcast is about");return}if(!s()){O(null);return}Y(!0),O(null),v([]),t?$(!0):(M([]),ve(!1),$(!1),ue(!1));try{const n=t?V(x,K):`Create 4 unique, high-converting podcast names for: ${x}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`,a=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:n}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!a.ok)throw new Error(`API request failed: ${a.status} ${a.statusText}`);const o=await a.json();if(!o.candidates||!o.candidates[0]||!o.candidates[0].content)throw new Error("Invalid response format from API");const l=o.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!l)throw new Error("No valid JSON found in API response");const r=JSON.parse(l[0]);if(!r.podcast_names||!Array.isArray(r.podcast_names))throw new Error("Invalid response structure");v(r.podcast_names),d(4),y(r.podcast_names);const c=r.podcast_names.map((u,j)=>({name:u.name,description:u.description,liked:null,timestamp:Date.now(),index:j}));M(c)}catch(n){console.error("Error generating podcast names:",n),O(n instanceof Error?n.message:"An unexpected error occurred")}finally{Y(!1),$(!1)}},ye=async(t,n)=>{try{await navigator.clipboard.writeText(t),Z(n),setTimeout(()=>Z(null),2e3)}catch(a){console.error("Failed to copy text:",a)}},ke=async(t,n)=>{const a=p[t];a&&(n?(G(o=>o.find(i=>i.name===a.name)?o:[...o,a]),de(o=>{const i={...o};return i.dislikedNames=i.dislikedNames.filter(l=>l.name!==a.name),i.likedNames.find(l=>l.name===a.name)||i.likedNames.push({name:a.name,description:a.description,liked:!0,timestamp:Date.now(),index:t}),i.patterns=I([...i.likedNames,...i.dislikedNames]),i}),B(o=>new Set([...o,t]))):(se(o=>new Set([...o,t])),de(o=>{const i={...o};return i.likedNames=i.likedNames.filter(l=>l.name!==a.name),i.dislikedNames.find(l=>l.name===a.name)||i.dislikedNames.push({name:a.name,description:a.description,liked:!1,timestamp:Date.now(),index:t}),i.patterns=I([...i.likedNames,...i.dislikedNames]),i}),x.trim()&&Ae(t)),Ne||ue(!0))},Ae=async t=>{var n,a,o,i,l,r;if(s())try{q(A=>new Set([...A,t]));const c=V(x,K),u=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:c}]}]})});if(!u.ok)throw new Error("Failed to generate replacement suggestion");const U=(l=(i=(o=(a=(n=(await u.json()).candidates)==null?void 0:n[0])==null?void 0:a.content)==null?void 0:o.parts)==null?void 0:i[0])==null?void 0:l.text;if(!U)throw new Error("No content in API response");const b=U.match(/\{[\s\S]*\}/);if(!b)throw new Error("No valid JSON found in response");const W=(r=JSON.parse(b[0]).names)==null?void 0:r[0];if(W&&(v(A=>{const S=[...A];return S[t]={name:W.name,description:W.description,suggestedDomain:W.suggestedDomain,domainStatus:"checking"},S}),B(A=>{const S=new Set(A);return S.delete(t),S}),se(A=>{const S=new Set(A);return S.delete(t),S}),W.suggestedDomain)){const A=await T(W.suggestedDomain);v(S=>{const w=[...S];return w[t]&&(w[t].domainStatus=A),w})}}catch(c){console.error("Error generating replacement suggestion:",c),se(u=>{const j=new Set(u);return j.delete(t),j})}finally{q(c=>{const u=new Set(c);return u.delete(t),u})}},_e=()=>{x.trim()&&Pe(x)},Pe=async t=>{var n,a,o,i,l,r,c,u,j,U;Y(!0),O(""),$(!0);try{const b=p.filter((w,R)=>{const g=ee.find(k=>k.index===R);return(g==null?void 0:g.liked)===!0}),Ee=p.filter((w,R)=>{const g=ee.find(k=>k.index===R);return(g==null?void 0:g.liked)===!1}).length,W=Math.max(1,Ee),S=Math.min(5,b.length+W)-b.length;if(S<=0){const w=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:fe(t,K,1)}]}]})});if(!w.ok)throw new Error(`API request failed: ${w.status}`);const g=(l=(i=(o=(a=(n=(await w.json()).candidates)==null?void 0:n[0])==null?void 0:a.content)==null?void 0:o.parts)==null?void 0:i[0])==null?void 0:l.text;if(!g)throw new Error("No content received from API");const k=g.match(/\{[\s\S]*\}/);if(!k)throw new Error("No valid JSON found in response");const D=JSON.parse(k[0]);if(!D.podcast_names||!Array.isArray(D.podcast_names))throw new Error("Invalid response format");const ce=[...b,...D.podcast_names].slice(0,5);v(ce),y(ce)}else{const w=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:fe(t,K,S)}]}]})});if(!w.ok)throw new Error(`API request failed: ${w.status}`);const g=(U=(j=(u=(c=(r=(await w.json()).candidates)==null?void 0:r[0])==null?void 0:c.content)==null?void 0:u.parts)==null?void 0:j[0])==null?void 0:U.text;if(!g)throw new Error("No content received from API");const k=g.match(/\{[\s\S]*\}/);if(!k)throw new Error("No valid JSON found in response");const D=JSON.parse(k[0]);if(!D.podcast_names||!Array.isArray(D.podcast_names))throw new Error("Invalid response format");const ce=[...b,...D.podcast_names];v(ce),y(ce)}M(w=>w.filter(R=>{const g=p[R.index];return b.some(k=>k.name===(g==null?void 0:g.name))})),M(w=>w.map(R=>{const g=p[R.index],k=b.findIndex(D=>D.name===(g==null?void 0:g.name));return k>=0?{...R,index:k}:R}).filter(R=>R.index>=0))}catch(b){console.error("Error generating refined names:",b),O(b instanceof Error?b.message:"Failed to generate refined names. Please try again.")}finally{Y(!1),$(!1)}},Se=t=>{t.preventDefault(),we()},Re=t=>{t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),we())};return e.jsx("div",{className:`podcast-name-generator ${_}`,style:z,children:e.jsxs("div",{className:"generator-container",children:[e.jsxs("div",{className:"header-section",children:[e.jsx("h1",{className:"main-title",children:"Free Podcast Name Generator"}),e.jsx("h2",{className:"main-subtitle",children:"Create the Perfect Name for Your Podcast in Seconds"})]}),e.jsxs("div",{className:"benefits-section",children:[e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"100% Free Forever"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"No Sign-up Required"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"Instant Results"})]})]}),C&&e.jsx("div",{className:"limit-reached-banner",children:e.jsxs("div",{className:"limit-content",children:[e.jsx("span",{className:"limit-icon",children:"⚠️"}),e.jsx("div",{className:"limit-text",children:e.jsx("p",{children:"You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below."})})]})}),p.length===0&&e.jsx("div",{className:"initial-input-section",children:e.jsx("form",{onSubmit:Se,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:x,onChange:t=>L(t.target.value),onKeyPress:Re,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:E}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:E||!x.trim()||C,className:`generate-button ${C?"disabled":""}`,children:E?"Generating...":C?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star star-partial",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})}),Q&&e.jsxs("div",{className:"error-message",children:[e.jsx("span",{className:"error-icon",children:"⚠️"}),Q]}),E&&e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:me?"Generating better names based on your preferences...":"Generating creative podcast names..."})]}),p.length>0&&e.jsxs("div",{className:"results-container",children:[P.length>0&&e.jsxs("div",{className:"favorites-section",children:[e.jsxs("div",{className:"favorites-header",children:[e.jsxs("h3",{children:["🏆 Your Winning Podcast Names (",P.length,")"]}),e.jsx("p",{className:"favorites-subtitle",children:"Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!"})]}),e.jsx("div",{className:"favorites-grid",children:P.map((t,n)=>e.jsxs("div",{className:"favorite-card",children:[e.jsxs("div",{className:"favorite-content",children:[e.jsx("h4",{className:"favorite-name",children:t.name}),e.jsx("p",{className:"favorite-description",children:t.description}),t.suggestedDomain&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsx("span",{className:"domain-name",children:t.suggestedDomain}),e.jsx("span",{className:`domain-status ${t.domainStatus}`,children:t.domainStatus==="available"?"✅ Available":t.domainStatus==="taken"?"❌ Taken":t.domainStatus==="error"?"⚠️ Check manually":"🔍 Checking..."})]})]}),e.jsx("div",{className:"favorite-actions",children:e.jsx("button",{onClick:()=>ye(t.name,-1),className:"copy-button small",title:"Copy to clipboard",children:"📋 Copy"})})]},`fav-${n}`))})]}),e.jsxs("div",{className:"input-section-simple",children:[e.jsx("div",{className:"input-help-message-simple",children:e.jsxs("p",{className:"input-sub-description",children:["💡 Want different suggestions? Update your description below - ",e.jsx("strong",{children:"your favorites will stay safe!"})]})}),e.jsx("form",{onSubmit:Se,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:x,onChange:t=>L(t.target.value),onKeyPress:Re,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:E}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:E||!x.trim()||C,className:`generate-button ${C?"disabled":""}`,children:E?"Generating...":C?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star star-partial",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})]}),e.jsxs("div",{className:"suggestions-section",children:[e.jsx("div",{className:"suggestions-header",children:e.jsx("h3",{children:"🎯 Current Suggestions"})}),e.jsx("div",{className:"onboarding-banner",children:e.jsxs("div",{className:"onboarding-content",children:[e.jsx("span",{className:"onboarding-icon",children:"💡"}),e.jsxs("div",{className:"onboarding-text",children:[e.jsx("strong",{children:"Smart AI Learning:"})," The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."]})]})}),e.jsx("div",{className:"results-grid",children:p.map((t,n)=>{const a=ee.find(u=>u.index===n),o=(a==null?void 0:a.liked)===!0,i=(a==null?void 0:a.liked)===!1,l=be.has(n),r=he.has(n),c=te.has(n);return l&&!r&&!c?null:e.jsxs("div",{className:`result-card ${o?"liked":""} ${i?"disliked":""} ${r?"pending":""}`,style:{opacity:r?.6:1,pointerEvents:r||c?"none":"auto"},children:[e.jsxs("div",{className:"result-header",children:[e.jsx("h4",{className:"result-name",children:c?"Generating new suggestion...":t.name}),e.jsxs("div",{className:"result-actions",children:[e.jsxs("div",{className:"feedback-buttons",children:[e.jsx("button",{onClick:()=>ke(n,!0),className:`feedback-button like-button ${o?"active":""}`,title:"I like this name",disabled:r||c,children:"👍"}),e.jsx("button",{onClick:()=>ke(n,!1),className:`feedback-button dislike-button ${i?"active":""} ${c?"loading":""}`,title:c?"Generating replacement...":"I don't like this name",disabled:r||c,children:c?"🔄":"👎"})]}),e.jsx("button",{onClick:()=>ye(t.name,n),className:"copy-button",title:"Copy podcast name",disabled:r||c,children:J===n?"✓ Copied!":"📋 Copy"})]})]}),e.jsx("p",{className:"result-description",children:c?"Creating a better suggestion based on your preferences...":t.description}),t.suggestedDomain&&!c&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsxs("code",{className:"domain-text",children:[t.suggestedDomain,".com"]}),e.jsxs("span",{className:`domain-status ${t.domainStatus}`,children:[(t.domainStatus==="checking"||te.has(n))&&"⏳ Checking...",t.domainStatus==="available"&&"✅ Available",t.domainStatus==="taken"&&"❌ Taken",t.domainStatus==="error"&&"⚠️ Check manually"]})]})]},n)})})]}),ge&&!E&&e.jsxs("div",{className:"refinement-section",children:[e.jsx("div",{className:"refinement-info",children:e.jsx("p",{children:"💡 I'll keep your liked names and replace the disliked ones with better suggestions!"})}),e.jsx("button",{onClick:_e,className:"refinement-button",disabled:E,children:me?"🔄 Refining...":"🎯 Replace Disliked Names"})]})]})]})})}});

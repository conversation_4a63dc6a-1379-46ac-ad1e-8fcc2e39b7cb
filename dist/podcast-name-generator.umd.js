(function(l,v){typeof exports=="object"&&typeof module<"u"?module.exports=v(require("react")):typeof define=="function"&&define.amd?define(["react"],v):(l=typeof globalThis<"u"?globalThis:l||self,l.PodcastNameGenerator=v(l.React))})(this,function(l){"use strict";var v={exports:{}},T={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Q;function ee(){if(Q)return T;Q=1;var g=Symbol.for("react.transitional.element"),w=Symbol.for("react.fragment");function y(p,f,m){var k=null;if(m!==void 0&&(k=""+m),f.key!==void 0&&(k=""+f.key),"key"in f){m={};for(var u in f)u!=="key"&&(m[u]=f[u])}else m=f;return f=m.ref,{$$typeof:g,type:p,key:k,ref:f!==void 0?f:null,props:m}}return T.Fragment=w,T.jsx=y,T.jsxs=y,T}var _={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var q;function te(){return q||(q=1,process.env.NODE_ENV!=="production"&&function(){function g(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===H?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case b:return"Fragment";case K:return"Profiler";case Y:return"StrictMode";case A:return"Suspense";case B:return"SuspenseList";case W:return"Activity"}if(typeof e=="object")switch(typeof e.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),e.$$typeof){case P:return"Portal";case $:return(e.displayName||"Context")+".Provider";case G:return(e._context.displayName||"Context")+".Consumer";case O:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case V:return t=e.displayName||null,t!==null?t:g(e.type)||"Memo";case M:t=e._payload,e=e._init;try{return g(e(t))}catch{}}return null}function w(e){return""+e}function y(e){try{w(e);var t=!1}catch{t=!0}if(t){t=console;var n=t.error,i=typeof Symbol=="function"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object";return n.call(t,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",i),w(e)}}function p(e){if(e===b)return"<>";if(typeof e=="object"&&e!==null&&e.$$typeof===M)return"<...>";try{var t=g(e);return t?"<"+t+">":"<...>"}catch{return"<...>"}}function f(){var e=C.A;return e===null?null:e.getOwner()}function m(){return Error("react-stack-top-frame")}function k(e){if(R.call(e,"key")){var t=Object.getOwnPropertyDescriptor(e,"key").get;if(t&&t.isReactWarning)return!1}return e.key!==void 0}function u(e,t){function n(){J||(J=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",t))}n.isReactWarning=!0,Object.defineProperty(e,"key",{get:n,configurable:!0})}function L(){var e=g(this.type);return U[e]||(U[e]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),e=this.props.ref,e!==void 0?e:null}function D(e,t,n,i,a,c,h,I){return n=c.ref,e={$$typeof:S,type:e,key:t,props:c,_owner:a},(n!==void 0?n:null)!==null?Object.defineProperty(e,"ref",{enumerable:!1,get:L}):Object.defineProperty(e,"ref",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:h}),Object.defineProperty(e,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:I}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function E(e,t,n,i,a,c,h,I){var d=t.children;if(d!==void 0)if(i)if(X(d)){for(i=0;i<d.length;i++)F(d[i]);Object.freeze&&Object.freeze(d)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else F(d);if(R.call(t,"key")){d=g(e);var x=Object.keys(t).filter(function(ne){return ne!=="key"});i=0<x.length?"{key: someKey, "+x.join(": ..., ")+": ...}":"{key: someKey}",r[d+i]||(x=0<x.length?"{"+x.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,i,d,x,d),r[d+i]=!0)}if(d=null,n!==void 0&&(y(n),d=""+n),k(t)&&(y(t.key),d=""+t.key),"key"in t){n={};for(var Z in t)Z!=="key"&&(n[Z]=t[Z])}else n=t;return d&&u(n,typeof e=="function"?e.displayName||e.name||"Unknown":e),D(e,d,c,a,f(),n,h,I)}function F(e){typeof e=="object"&&e!==null&&e.$$typeof===S&&e._store&&(e._store.validated=1)}var N=l,S=Symbol.for("react.transitional.element"),P=Symbol.for("react.portal"),b=Symbol.for("react.fragment"),Y=Symbol.for("react.strict_mode"),K=Symbol.for("react.profiler"),G=Symbol.for("react.consumer"),$=Symbol.for("react.context"),O=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),B=Symbol.for("react.suspense_list"),V=Symbol.for("react.memo"),M=Symbol.for("react.lazy"),W=Symbol.for("react.activity"),H=Symbol.for("react.client.reference"),C=N.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,R=Object.prototype.hasOwnProperty,X=Array.isArray,j=console.createTask?console.createTask:function(){return null};N={"react-stack-bottom-frame":function(e){return e()}};var J,U={},z=N["react-stack-bottom-frame"].bind(N,m)(),s=j(p(m)),r={};_.Fragment=b,_.jsx=function(e,t,n,i,a){var c=1e4>C.recentlyCreatedOwnerStacks++;return E(e,t,n,!1,i,a,c?Error("react-stack-top-frame"):z,c?j(p(e)):s)},_.jsxs=function(e,t,n,i,a){var c=1e4>C.recentlyCreatedOwnerStacks++;return E(e,t,n,!0,i,a,c?Error("react-stack-top-frame"):z,c?j(p(e)):s)}}()),_}process.env.NODE_ENV==="production"?v.exports=ee():v.exports=te();var o=v.exports;return({apiKey:g="AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",className:w="",style:y={}})=>{const[p,f]=l.useState(""),[m,k]=l.useState([]),[u,L]=l.useState(!1),[D,E]=l.useState(null),[F,N]=l.useState(null),[S,P]=l.useState([]),[b,Y]=l.useState({likedNames:[],dislikedNames:[],patterns:{preferredLength:null,preferredStyle:null,likedKeywords:[],dislikedKeywords:[],preferredStructure:null},generationRound:0}),[K,G]=l.useState(!1),[$,O]=l.useState(!1),[A,B]=l.useState([]),V=(s,r)=>{if(s.length===0)return null;const e=s.map(a=>a.name.split(" ").length),t=r.map(a=>a.name.split(" ").length),n=e.reduce((a,c)=>a+c,0)/e.length,i=t.length>0?t.reduce((a,c)=>a+c,0)/t.length:0;return n<=2&&i>2?"short":n<=4&&(i<=2||i>4)?"medium":n>4&&i<=4?"long":n<=2?"short":n<=4?"medium":"long"},M=(s,r)=>{if(s.length===0)return null;const e=s.map(n=>n.description.toLowerCase()).join(" "),t=r.map(n=>n.description.toLowerCase()).join(" ");return(e.includes("professional")||e.includes("business"))&&!t.includes("professional")&&!t.includes("business")?"professional":(e.includes("creative")||e.includes("unique"))&&!t.includes("creative")&&!t.includes("unique")?"creative":(e.includes("fun")||e.includes("playful"))&&!t.includes("fun")&&!t.includes("playful")?"playful":"descriptive"},W=s=>{const r=[];s.forEach(t=>{const n=["the","a","an","and","or","but","in","on","at","to","for","of","with","by"],i=t.name.toLowerCase().split(/\s+/).filter(a=>a.length>2&&!n.includes(a));r.push(...i)});const e={};return r.forEach(t=>e[t]=(e[t]||0)+1),Object.entries(e).sort(([,t],[,n])=>n-t).slice(0,5).map(([t])=>t)},H=s=>{const r=s.filter(t=>t.liked===!0),e=s.filter(t=>t.liked===!1);return{preferredLength:V(r,e),preferredStyle:M(r,e),likedKeywords:W(r),dislikedKeywords:W(e),preferredStructure:null}},C=(s,r)=>{const e=`Create 5 high-converting and catchy podcast names based on the following user input: ${s}.`;let t="";if(r.patterns.preferredLength){const n={short:"1-2 words",medium:"3-4 words",long:"5+ words"};t+=`Focus on ${r.patterns.preferredLength} names (${n[r.patterns.preferredLength]}). `}if(r.patterns.preferredStyle&&(t+=`Use a ${r.patterns.preferredStyle} style. `),r.patterns.likedKeywords.length>0&&(t+=`Incorporate concepts similar to: ${r.patterns.likedKeywords.join(", ")}. `),r.patterns.dislikedKeywords.length>0&&(t+=`Avoid concepts like: ${r.patterns.dislikedKeywords.join(", ")}. `),r.likedNames.length>0){const n=r.likedNames.slice(-2).map(i=>i.name).join('", "');t+=`Generate names with similar appeal to: "${n}". `}return`${e} ${t} Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`},R=async(s=!1)=>{if(!p.trim()){E("Please describe what your podcast is about");return}L(!0),E(null),k([]),s?O(!0):(P([]),G(!1),O(!1));try{const r=s?C(p,b):`Create 5 high-converting and catchy podcast names based on the following user input: ${p}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`,e=await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${g}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:r}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!e.ok)throw new Error(`API request failed: ${e.status} ${e.statusText}`);const t=await e.json();if(!t.candidates||!t.candidates[0]||!t.candidates[0].content)throw new Error("Invalid response format from API");const i=t.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!i)throw new Error("No valid JSON found in API response");const a=JSON.parse(i[0]);if(!a.podcast_names||!Array.isArray(a.podcast_names))throw new Error("Invalid response structure");k(a.podcast_names),B(h=>[...h,a.podcast_names]),Y(h=>({...h,generationRound:h.generationRound+1}));const c=a.podcast_names.map((h,I)=>({name:h.name,description:h.description,liked:null,timestamp:Date.now(),index:I}));P(c)}catch(r){console.error("Error generating podcast names:",r),E(r instanceof Error?r.message:"An unexpected error occurred")}finally{L(!1),O(!1)}},X=async(s,r)=>{try{await navigator.clipboard.writeText(s),N(r),setTimeout(()=>N(null),2e3)}catch(e){console.error("Failed to copy text:",e)}},j=(s,r)=>{const e=[...S];e[s]={...e[s],liked:r,timestamp:Date.now()},P(e);const t={...b},n=e[s];r?(t.dislikedNames=t.dislikedNames.filter(a=>a.name!==n.name),t.likedNames.find(a=>a.name===n.name)||t.likedNames.push(n)):(t.likedNames=t.likedNames.filter(a=>a.name!==n.name),t.dislikedNames.find(a=>a.name===n.name)||t.dislikedNames.push(n)),t.patterns=H([...t.likedNames,...t.dislikedNames]),Y(t),t.likedNames.length+t.dislikedNames.length>=2&&G(!0)},J=()=>{R(!0)},U=s=>{s.preventDefault(),R()},z=s=>{s.key==="Enter"&&!s.shiftKey&&(s.preventDefault(),R())};return o.jsx("div",{className:`podcast-name-generator ${w}`,style:y,children:o.jsxs("div",{className:"generator-container",children:[o.jsx("h2",{className:"generator-title",children:"Podcast Name Generator"}),o.jsx("p",{className:"generator-subtitle",children:"Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI"}),o.jsx("form",{onSubmit:U,className:"input-form",children:o.jsxs("div",{className:"input-container",children:[o.jsx("textarea",{value:p,onChange:s=>f(s.target.value),onKeyPress:z,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:u}),o.jsx("button",{type:"submit",disabled:u||!p.trim(),className:"generate-button",children:u?"Generating...":"Generate Names"})]})}),D&&o.jsxs("div",{className:"error-message",children:[o.jsx("span",{className:"error-icon",children:"⚠️"}),D]}),u&&o.jsxs("div",{className:"loading-container",children:[o.jsx("div",{className:"loading-spinner"}),o.jsx("p",{children:$?"Generating better names based on your preferences...":"Generating creative podcast names..."})]}),m.length>0&&o.jsxs("div",{className:"results-container",children:[o.jsxs("h3",{className:"results-title",children:[b.generationRound>1?"Refined Podcast Name Suggestions":"Your Podcast Name Suggestions",A.length>1&&o.jsxs("span",{className:"generation-counter",children:[" (Round ",b.generationRound,")"]})]}),b.generationRound>1&&o.jsxs("p",{className:"refinement-info",children:["✨ These names are tailored based on your preferences from ",A.length-1," previous round",A.length>2?"s":""]}),o.jsx("div",{className:"results-grid",children:m.map((s,r)=>{const e=S.find(i=>i.index===r),t=(e==null?void 0:e.liked)===!0,n=(e==null?void 0:e.liked)===!1;return o.jsxs("div",{className:`result-card ${t?"liked":""} ${n?"disliked":""}`,children:[o.jsxs("div",{className:"result-header",children:[o.jsx("h4",{className:"result-name",children:s.name}),o.jsxs("div",{className:"result-actions",children:[o.jsxs("div",{className:"feedback-buttons",children:[o.jsx("button",{onClick:()=>j(r,!0),className:`feedback-button like-button ${t?"active":""}`,title:"I like this name",disabled:u,children:"👍"}),o.jsx("button",{onClick:()=>j(r,!1),className:`feedback-button dislike-button ${n?"active":""}`,title:"I don't like this name",disabled:u,children:"👎"})]}),o.jsx("button",{onClick:()=>X(s.name,r),className:"copy-button",title:"Copy podcast name",children:F===r?"✓ Copied!":"📋 Copy"})]})]}),o.jsx("p",{className:"result-description",children:s.description})]},r)})}),K&&!u&&o.jsxs("div",{className:"refinement-section",children:[o.jsx("div",{className:"refinement-info",children:o.jsx("p",{children:"💡 Based on your feedback, I can generate better names that match your preferences!"})}),o.jsx("button",{onClick:J,className:"refinement-button",disabled:u,children:"🎯 Generate Better Names"})]})]})]})})}});

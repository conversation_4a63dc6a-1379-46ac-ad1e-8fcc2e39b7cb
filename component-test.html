<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Syntax Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif;
            background: #f5f5f5;
        }
        .test-result {
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
    </style>
</head>
<body>
    <h1>Podcast Name Generator Component Test</h1>
    <div id="test-results"></div>
    <div id="component-container"></div>

    <script type="text/babel">
        const { useState } = React;

        // Test Results Component
        function TestResults({ results }) {
            return (
                <div>
                    {results.map((result, index) => (
                        <div key={index} className={`test-result ${result.success ? 'success' : 'error'}`}>
                            <strong>{result.test}:</strong> {result.message}
                        </div>
                    ))}
                </div>
            );
        }

        // Simplified Component for Testing
        function PodcastNameGenerator() {
            const [input, setInput] = useState('');
            const [results, setResults] = useState([]);
            const [likedNames, setLikedNames] = useState([]);
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState(null);
            const [copiedIndex, setCopiedIndex] = useState(null);
            const [preferences, setPreferences] = useState({
                likedNames: [],
                dislikedNames: [],
                preferredLength: null,
                preferredStyle: null,
                preferredKeywords: []
            });
            const [userInput, setUserInput] = useState('');
            const [usageCount, setUsageCount] = useState(0);
            const [showLearningMessage, setShowLearningMessage] = useState(false);

            const MAX_USAGE = 100;

            const generatePodcastNames = async () => {
                if (!input.trim()) {
                    setError('Please describe what your podcast is about');
                    return;
                }

                setLoading(true);
                setError(null);
                setUserInput(input);

                // Simulate API call for testing
                setTimeout(() => {
                    const mockResults = [
                        { name: "Test Podcast 1", description: "A great test name" },
                        { name: "Test Podcast 2", description: "Another test name" },
                        { name: "Test Podcast 3", description: "Third test name" },
                        { name: "Test Podcast 4", description: "Fourth test name" }
                    ];
                    setResults(mockResults);
                    setUsageCount(prev => prev + 4);
                    setLoading(false);
                }, 1000);
            };

            const handleLike = (index) => {
                const name = results[index];
                if (!name || name.liked) return;

                const updatedLikedNames = [...likedNames, { ...name, liked: true }];
                setLikedNames(updatedLikedNames);

                const updatedResults = results.filter((_, i) => i !== index);
                setResults(updatedResults);

                setShowLearningMessage(true);
                setTimeout(() => setShowLearningMessage(false), 3000);
            };

            const handleDislike = (index) => {
                const updatedResults = results.filter((_, i) => i !== index);
                setResults(updatedResults);
            };

            const copyToClipboard = async (text, index) => {
                try {
                    await navigator.clipboard.writeText(text);
                    setCopiedIndex(index);
                    setTimeout(() => setCopiedIndex(null), 2000);
                } catch (err) {
                    console.error('Failed to copy text:', err);
                }
            };

            const handleSubmit = (e) => {
                e.preventDefault();
                generatePodcastNames();
            };

            return (
                <div style={{
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif',
                    maxWidth: '900px',
                    margin: '0 auto',
                    padding: '20px',
                    background: '#ffffff',
                    borderRadius: '16px',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                    width: '100%',
                    minHeight: '600px'
                }}>
                    <div style={{ width: '100%' }}>
                        <h1 style={{
                            fontSize: '2.5rem',
                            fontWeight: 700,
                            color: '#1a1a1a',
                            textAlign: 'center',
                            margin: '0 0 8px 0',
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            backgroundClip: 'text'
                        }}>
                            Free Podcast Name Generator
                        </h1>

                        <h2 style={{
                            fontSize: '1.5rem',
                            fontWeight: 600,
                            color: '#4a5568',
                            textAlign: 'center',
                            margin: '0 0 24px 0',
                            lineHeight: 1.4
                        }}>
                            Create the Perfect Name for Your Podcast in Seconds
                        </h2>

                        {/* Trust Indicators */}
                        <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            gap: '24px',
                            marginBottom: '32px',
                            flexWrap: 'wrap'
                        }}>
                            {['100% Free Forever', 'No Sign-up Required', 'Instant Results'].map((text, index) => (
                                <div key={index} style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    color: '#4a5568',
                                    fontSize: '0.95rem',
                                    fontWeight: 500
                                }}>
                                    <div style={{
                                        width: '20px',
                                        height: '20px',
                                        borderRadius: '50%',
                                        backgroundColor: '#6941C7',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: 'white',
                                        fontSize: '12px',
                                        fontWeight: 'bold'
                                    }}>
                                        ✓
                                    </div>
                                    {text}
                                </div>
                            ))}
                        </div>

                        <p style={{
                            fontSize: '1.1rem',
                            color: '#666',
                            textAlign: 'center',
                            margin: '0 0 32px 0',
                            lineHeight: 1.5
                        }}>
                            Describe your podcast topic and get 4 catchy, high-converting name suggestions powered by AI
                        </p>

                        {/* Favorites Section */}
                        {likedNames.length > 0 && (
                            <div style={{ marginBottom: '32px' }}>
                                <h3 style={{
                                    fontSize: '1.4rem',
                                    fontWeight: 600,
                                    color: '#1a1a1a',
                                    margin: '0 0 16px 0',
                                    textAlign: 'center'
                                }}>
                                    🌟 Favoured Podcast Names
                                </h3>
                                <div style={{
                                    display: 'grid',
                                    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                                    gap: '16px',
                                    marginBottom: '24px'
                                }}>
                                    {likedNames.map((name, index) => (
                                        <div key={`liked-${index}`} style={{
                                            background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                                            border: '2px solid #0ea5e9',
                                            borderRadius: '12px',
                                            padding: '20px',
                                            position: 'relative',
                                            boxShadow: '0 4px 12px rgba(14, 165, 233, 0.15)'
                                        }}>
                                            <div style={{
                                                position: 'absolute',
                                                top: '12px',
                                                right: '12px',
                                                width: '24px',
                                                height: '24px',
                                                borderRadius: '50%',
                                                backgroundColor: '#0ea5e9',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                color: 'white',
                                                fontSize: '14px',
                                                fontWeight: 'bold'
                                            }}>
                                                ⭐
                                            </div>
                                            <h4 style={{
                                                fontSize: '1.2rem',
                                                fontWeight: 600,
                                                color: '#0c4a6e',
                                                margin: '0 0 8px 0',
                                                paddingRight: '32px'
                                            }}>
                                                {name.name}
                                            </h4>
                                            <p style={{
                                                color: '#0c4a6e',
                                                lineHeight: 1.4,
                                                margin: 0,
                                                fontSize: '0.9rem',
                                                opacity: 0.8
                                            }}>
                                                {name.description}
                                            </p>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Input Section */}
                        <div style={{
                            marginBottom: '32px'
                        }}>
                            {results.length > 0 && (
                                <div style={{
                                    marginBottom: '16px',
                                    textAlign: 'center'
                                }}>
                                    <p style={{
                                        color: '#6b7280',
                                        fontSize: '0.95rem',
                                        margin: 0,
                                        lineHeight: 1.4
                                    }}>
                                        💡 Want different suggestions? Update your description below - <strong>your favorites will stay safe!</strong>
                                    </p>
                                </div>
                            )}

                            <form onSubmit={handleSubmit}>
                                <div style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '16px'
                                }}>
                                    <textarea
                                        value={input}
                                        onChange={(e) => setInput(e.target.value)}
                                        placeholder="Describe what your podcast is about..."
                                        style={{
                                            width: '100%',
                                            padding: '16px 20px',
                                            fontSize: '1rem',
                                            border: '2px solid #e1e5e9',
                                            borderRadius: '12px',
                                            resize: 'vertical',
                                            minHeight: '80px',
                                            fontFamily: 'inherit',
                                            transition: 'all 0.2s ease',
                                            boxSizing: 'border-box'
                                        }}
                                        rows={3}
                                        disabled={loading}
                                    />

                                    <button
                                        type="submit"
                                        disabled={loading || !input.trim()}
                                        style={{
                                            padding: '14px 28px',
                                            fontSize: '1rem',
                                            fontWeight: 600,
                                            color: 'white',
                                            background: loading || !input.trim()
                                                ? '#9ca3af'
                                                : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                            border: 'none',
                                            borderRadius: '12px',
                                            cursor: loading || !input.trim() ? 'not-allowed' : 'pointer',
                                            transition: 'all 0.2s ease',
                                            minWidth: '160px'
                                        }}
                                    >
                                        {loading ? 'Generating...' : 'Generate Names'}
                                    </button>
                                </div>
                            </form>
                        </div>

                        {/* Error Display */}
                        {error && (
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '8px',
                                padding: '16px 20px',
                                backgroundColor: '#fef2f2',
                                border: '1px solid #fecaca',
                                borderRadius: '12px',
                                color: '#dc2626',
                                fontWeight: 500,
                                marginBottom: '24px'
                            }}>
                                <span style={{ fontSize: '1.2rem' }}>⚠️</span>
                                {error}
                            </div>
                        )}

                        {/* Learning Message */}
                        {showLearningMessage && (
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '8px',
                                padding: '12px 16px',
                                backgroundColor: '#f0f9ff',
                                border: '1px solid #0ea5e9',
                                borderRadius: '8px',
                                color: '#0c4a6e',
                                fontWeight: 500,
                                marginBottom: '16px',
                                fontSize: '0.9rem'
                            }}>
                                <span>🧠</span>
                                Great choice! I'm learning your preferences to suggest better names.
                            </div>
                        )}

                        {/* Loading State */}
                        {loading && (
                            <div style={{
                                textAlign: 'center',
                                padding: '40px 20px',
                                color: '#666'
                            }}>
                                <div style={{
                                    width: '40px',
                                    height: '40px',
                                    border: '3px solid #f3f3f3',
                                    borderTop: '3px solid #667eea',
                                    borderRadius: '50%',
                                    animation: 'spin 1s linear infinite',
                                    margin: '0 auto 16px'
                                }}></div>
                                <p>Generating creative podcast names...</p>
                            </div>
                        )}

                        {/* Current Suggestions */}
                        {results.length > 0 && (
                            <div style={{ marginTop: '32px' }}>
                                <h3 style={{
                                    fontSize: '1.4rem',
                                    fontWeight: 600,
                                    color: '#1a1a1a',
                                    margin: '0 0 20px 0',
                                    textAlign: 'center'
                                }}>
                                    Current Suggestions
                                </h3>
                                <div style={{
                                    display: 'grid',
                                    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                                    gap: '16px',
                                    marginBottom: '24px'
                                }}>
                                    {results.map((result, index) => (
                                        <div key={index} style={{
                                            background: '#ffffff',
                                            border: '1px solid #e5e7eb',
                                            borderRadius: '12px',
                                            padding: '20px',
                                            transition: 'all 0.2s ease',
                                            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
                                        }}>
                                            <div style={{
                                                display: 'flex',
                                                justifyContent: 'space-between',
                                                alignItems: 'flex-start',
                                                gap: '12px',
                                                marginBottom: '12px'
                                            }}>
                                                <h4 style={{
                                                    fontSize: '1.2rem',
                                                    fontWeight: 600,
                                                    color: '#1a1a1a',
                                                    margin: 0,
                                                    flex: 1,
                                                    lineHeight: 1.3
                                                }}>
                                                    {result.name}
                                                </h4>
                                                <button
                                                    onClick={() => copyToClipboard(result.name, index)}
                                                    style={{
                                                        padding: '6px 10px',
                                                        fontSize: '0.8rem',
                                                        fontWeight: 500,
                                                        background: '#667eea',
                                                        color: 'white',
                                                        border: 'none',
                                                        borderRadius: '6px',
                                                        cursor: 'pointer',
                                                        transition: 'all 0.2s ease',
                                                        whiteSpace: 'nowrap',
                                                        flexShrink: 0
                                                    }}
                                                    title="Copy podcast name"
                                                >
                                                    {copiedIndex === index ? '✓' : '📋'}
                                                </button>
                                            </div>

                                            <p style={{
                                                color: '#6b7280',
                                                lineHeight: 1.4,
                                                margin: '0 0 16px 0',
                                                fontSize: '0.9rem'
                                            }}>
                                                {result.description}
                                            </p>

                                            {/* Like/Dislike Buttons */}
                                            <div style={{
                                                display: 'flex',
                                                gap: '8px',
                                                justifyContent: 'center'
                                            }}>
                                                <button
                                                    onClick={() => handleLike(index)}
                                                    disabled={result.liked}
                                                    style={{
                                                        padding: '8px 16px',
                                                        fontSize: '0.9rem',
                                                        fontWeight: 500,
                                                        background: result.liked ? '#10b981' : '#f3f4f6',
                                                        color: result.liked ? 'white' : '#374151',
                                                        border: result.liked ? '1px solid #10b981' : '1px solid #d1d5db',
                                                        borderRadius: '8px',
                                                        cursor: result.liked ? 'default' : 'pointer',
                                                        transition: 'all 0.2s ease',
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        gap: '4px'
                                                    }}
                                                    title={result.liked ? 'Already liked' : 'Like this name'}
                                                >
                                                    👍 {result.liked ? 'Liked' : 'Like'}
                                                </button>
                                                <button
                                                    onClick={() => handleDislike(index)}
                                                    style={{
                                                        padding: '8px 16px',
                                                        fontSize: '0.9rem',
                                                        fontWeight: 500,
                                                        background: '#f3f4f6',
                                                        color: '#374151',
                                                        border: '1px solid #d1d5db',
                                                        borderRadius: '8px',
                                                        cursor: 'pointer',
                                                        transition: 'all 0.2s ease',
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        gap: '4px'
                                                    }}
                                                    title="Dislike this name"
                                                >
                                                    👎 Pass
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            );
        }

        // Run Tests
        function runTests() {
            const tests = [];

            try {
                // Test 1: Component renders without errors
                const testDiv = document.createElement('div');
                ReactDOM.render(<PodcastNameGenerator />, testDiv);
                tests.push({
                    test: "Component Rendering",
                    success: true,
                    message: "Component renders successfully without syntax errors"
                });
            } catch (error) {
                tests.push({
                    test: "Component Rendering",
                    success: false,
                    message: `Rendering failed: ${error.message}`
                });
            }

            try {
                // Test 2: State initialization
                const { useState } = React;
                const [testState, setTestState] = useState('test');
                tests.push({
                    test: "React Hooks",
                    success: true,
                    message: "useState hook works correctly"
                });
            } catch (error) {
                tests.push({
                    test: "React Hooks",
                    success: false,
                    message: `Hook error: ${error.message}`
                });
            }

            // Test 3: JSX syntax validation
            try {
                const testJSX = (
                    <div style={{ color: 'red' }}>
                        <span>Test</span>
                    </div>
                );
                tests.push({
                    test: "JSX Syntax",
                    success: true,
                    message: "JSX syntax is valid"
                });
            } catch (error) {
                tests.push({
                    test: "JSX Syntax",
                    success: false,
                    message: `JSX error: ${error.message}`
                });
            }

            return tests;
        }

        // Render tests and component
        const testResults = runTests();
        ReactDOM.render(<TestResults results={testResults} />, document.getElementById('test-results'));
        ReactDOM.render(<PodcastNameGenerator />, document.getElementById('component-container'));
    </script>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</body>
</html>
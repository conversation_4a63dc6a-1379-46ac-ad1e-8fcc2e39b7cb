<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Podcast Name Generator Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif;
            background: #f5f5f5;
        }
        
        @media (max-width: 768px) {
            .podcast-generator {
                padding: 16px !important;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        function PodcastNameGenerator() {
            const [input, setInput] = useState('');
            const [results, setResults] = useState([]);
            const [likedNames, setLikedNames] = useState([]);
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState(null);
            const [copiedIndex, setCopiedIndex] = useState(null);
            const [preferences, setPreferences] = useState({
                likedNames: [],
                dislikedNames: [],
                preferredLength: null,
                preferredStyle: null,
                preferredKeywords: []
            });
            const [userInput, setUserInput] = useState('');
            const [usageCount, setUsageCount] = useState(0);
            const [showLearningMessage, setShowLearningMessage] = useState(false);

            const MAX_USAGE = 100;

            const generateAdaptivePrompt = (input, prefs) => {
                let prompt = `Create 4 unique, high-converting podcast names for: "${input}".

CRITICAL REQUIREMENTS:
1. Each name must be completely unique and distinct from all others
2. No duplicates, variations, or similar names allowed
3. No singular/plural versions of the same concept
4. Each name should represent a different creative approach

Domain Generation Rules:
- Create domains that are 6-15 characters when possible
- Use 2-3 words maximum
- Avoid hyphens and numbers
- Make them easy to spell and pronounce
- Include relevant keywords when natural
- Generate smart, brandable domains (avoid overly long concatenations)

`;

                if (prefs.likedNames.length > 0) {
                    prompt += `\nUser has liked these names (learn from their style): ${prefs.likedNames.slice(-3).join(', ')}`;
                }

                if (prefs.dislikedNames.length > 0) {
                    prompt += `\nUser disliked these names (avoid similar styles): ${prefs.dislikedNames.slice(-3).join(', ')}`;
                }

                if (prefs.preferredLength) {
                    const lengthMap = {
                        'short': 'Keep names concise (1-2 words)',
                        'medium': 'Use moderate length names (2-3 words)',
                        'long': 'Create descriptive, longer names (3-4 words)'
                    };
                    prompt += `\nLength preference: ${lengthMap[prefs.preferredLength]}`;
                }

                if (prefs.preferredStyle) {
                    const styleMap = {
                        'creative': 'Use creative, unique, and memorable names',
                        'descriptive': 'Use clear, descriptive names that explain the content',
                        'brandable': 'Create brandable, business-focused names'
                    };
                    prompt += `\nStyle preference: ${styleMap[prefs.preferredStyle]}`;
                }

                prompt += `

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;

                return prompt;
            };

            const generateSingleNamePrompt = (input, prefs, count) => {
                let prompt = `Generate ${count} completely unique podcast name${count > 1 ? 's' : ''} for: "${input}".

CRITICAL: Must be totally different from any previous suggestions. No variations or similar concepts.

`;

                if (prefs.likedNames.length > 0) {
                    prompt += `Learn from these liked names: ${prefs.likedNames.slice(-2).join(', ')}\n`;
                }

                if (prefs.dislikedNames.length > 0) {
                    prompt += `Avoid styles like these disliked names: ${prefs.dislikedNames.slice(-2).join(', ')}\n`;
                }

                prompt += `Return as JSON: {"podcast_names": [{"name": "Name", "description": "Why it works"}]}`;

                return prompt;
            };

            const generatePodcastNames = async () => {
                if (!input.trim()) {
                    setError('Please describe what your podcast is about');
                    return;
                }

                if (usageCount >= MAX_USAGE) {
                    setError(`You've reached the limit of ${MAX_USAGE} suggestions. Thank you for using our generator!`);
                    return;
                }

                setLoading(true);
                setError(null);
                setUserInput(input);

                try {
                    const prompt = generateAdaptivePrompt(input, preferences);

                    const response = await fetch('https://api.yttranscribe.com/podcastNameGenerator', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            contents: [{
                                parts: [{
                                    text: prompt
                                }]
                            }],
                            generationConfig: {
                                temperature: 0.7,
                                topK: 40,
                                topP: 0.95,
                                maxOutputTokens: 1024,
                            }
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`Failed to generate new suggestion. Please try again.`);
                    }

                    const data = await response.json();

                    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                        throw new Error('Invalid response format from API');
                    }

                    const generatedText = data.candidates[0].content.parts[0].text;

                    // Extract JSON from the response
                    const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
                    if (!jsonMatch) {
                        throw new Error('No valid JSON found in API response');
                    }

                    const parsedResponse = JSON.parse(jsonMatch[0]);

                    if (!parsedResponse.podcast_names || !Array.isArray(parsedResponse.podcast_names)) {
                        throw new Error('Invalid response structure');
                    }

                    setResults(parsedResponse.podcast_names);
                    setUsageCount(prev => prev + 4);
                    checkDomainsForResults(parsedResponse.podcast_names);
                } catch (err) {
                    console.error('Error generating podcast names:', err);
                    setError(err instanceof Error ? err.message : 'Failed to generate new suggestion. Please try again.');
                } finally {
                    setLoading(false);
                }
            };

            const checkDomainsForResults = async (names) => {
                const updatedNames = [...names];

                for (let i = 0; i < updatedNames.length; i++) {
                    const name = updatedNames[i];
                    const domain = generateDomainFromName(name.name);
                    updatedNames[i] = { ...name, domain, domainChecking: true };
                    setResults([...updatedNames]);

                    try {
                        const isAvailable = await checkDomainAvailability(domain);
                        updatedNames[i] = { ...updatedNames[i], domainAvailable: isAvailable, domainChecking: false };
                        setResults([...updatedNames]);
                    } catch (error) {
                        updatedNames[i] = { ...updatedNames[i], domainAvailable: null, domainChecking: false };
                        setResults([...updatedNames]);
                    }
                }
            };

            const generateDomainFromName = (name) => {
                // Smart domain generation following best practices
                const words = name.toLowerCase()
                    .replace(/[^\w\s]/g, '') // Remove special characters
                    .split(/\s+/)
                    .filter(word => word.length > 0);

                // If single word, use as-is if reasonable length
                if (words.length === 1) {
                    const word = words[0];
                    if (word.length <= 15) {
                        return `${word}.com`;
                    }
                    // If too long, try to shorten intelligently
                    return `${word.substring(0, 12)}.com`;
                }

                // For multiple words, try different combinations
                if (words.length === 2) {
                    const combined = words.join('');
                    if (combined.length <= 15) {
                        return `${combined}.com`;
                    }
                    // Try abbreviating
                    const abbreviated = words.map(w => w.substring(0, Math.max(3, Math.floor(12/words.length)))).join('');
                    return `${abbreviated}.com`;
                }

                // For 3+ words, be more selective
                const important = words.filter(word =>
                    !['the', 'and', 'or', 'of', 'in', 'on', 'at', 'to', 'for', 'with', 'by'].includes(word)
                ).slice(0, 2);

                if (important.length >= 2) {
                    const combined = important.join('');
                    if (combined.length <= 15) {
                        return `${combined}.com`;
                    }
                }

                // Fallback: use first two meaningful words, abbreviated if needed
                const fallback = words.slice(0, 2).map(w => w.substring(0, 6)).join('');
                return `${fallback}.com`;
            };

            const checkDomainAvailability = async (domain) => {
                try {
                    // Simple DNS lookup to check if domain exists
                    const response = await fetch(`https://dns.google/resolve?name=${domain}&type=A`);
                    const data = await response.json();

                    // If no answer section or empty, domain might be available
                    // This is a basic check - for production, use a proper domain availability API
                    return !data.Answer || data.Answer.length === 0;
                } catch (error) {
                    // If DNS lookup fails, we can't determine availability
                    return false;
                }
            };

            const handleLike = async (index) => {
                const name = results[index];
                if (!name || name.liked) return;

                // Add to liked names
                const updatedLikedNames = [...likedNames, { ...name, liked: true }];
                setLikedNames(updatedLikedNames);

                // Update preferences
                const newPrefs = {
                    ...preferences,
                    likedNames: [...preferences.likedNames, name.name]
                };
                setPreferences(newPrefs);

                // Remove from current results
                const updatedResults = results.filter((_, i) => i !== index);
                setResults(updatedResults);

                // Show learning message
                setShowLearningMessage(true);
                setTimeout(() => setShowLearningMessage(false), 3000);

                // Generate replacement if we have fewer than 4 suggestions
                if (updatedResults.length < 4 && userInput && usageCount < MAX_USAGE) {
                    await generateReplacement();
                }
            };

            const handleDislike = async (index) => {
                const name = results[index];
                if (!name) return;

                // Update preferences
                const newPrefs = {
                    ...preferences,
                    dislikedNames: [...preferences.dislikedNames, name.name]
                };
                setPreferences(newPrefs);

                // Remove from current results
                const updatedResults = results.filter((_, i) => i !== index);
                setResults(updatedResults);

                // Generate replacement immediately
                if (userInput && usageCount < MAX_USAGE) {
                    await generateReplacement();
                }
            };

            const generateReplacement = async () => {
                if (!userInput || usageCount >= MAX_USAGE) return;

                try {
                    setLoading(true);

                    const response = await fetch('https://api.yttranscribe.com/podcastNameGenerator', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            contents: [{
                                parts: [{
                                    text: generateSingleNamePrompt(userInput, preferences, 1)
                                }]
                            }]
                        })
                    });

                    if (!response.ok) {
                        throw new Error('Failed to generate replacement');
                    }

                    const data = await response.json();
                    const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

                    if (!content) {
                        throw new Error('No content received from API');
                    }

                    const jsonMatch = content.match(/\{[\s\S]*\}/);
                    if (!jsonMatch) {
                        throw new Error('No valid JSON found in response');
                    }

                    const parsedResponse = JSON.parse(jsonMatch[0]);
                    if (!parsedResponse.podcast_names || !Array.isArray(parsedResponse.podcast_names) || parsedResponse.podcast_names.length === 0) {
                        throw new Error('Invalid response format');
                    }

                    const newName = parsedResponse.podcast_names[0];

                    // Add the new name to results
                    setResults(prev => [...prev, newName]);
                    setUsageCount(prev => prev + 1);

                    // Check domain for the new name
                    const domain = generateDomainFromName(newName.name);
                    const updatedName = { ...newName, domain, domainChecking: true };
                    setResults(prev => prev.map((item, i) => i === prev.length - 1 ? updatedName : item));

                    try {
                        const isAvailable = await checkDomainAvailability(domain);
                        setResults(prev => prev.map((item, i) =>
                            i === prev.length - 1 ? { ...item, domainAvailable: isAvailable, domainChecking: false } : item
                        ));
                    } catch (error) {
                        setResults(prev => prev.map((item, i) =>
                            i === prev.length - 1 ? { ...item, domainAvailable: null, domainChecking: false } : item
                        ));
                    }
                } catch (err) {
                    console.error('Error generating replacement:', err);
                } finally {
                    setLoading(false);
                }
            };

            const copyToClipboard = async (text, index) => {
                try {
                    await navigator.clipboard.writeText(text);
                    setCopiedIndex(index);
                    setTimeout(() => setCopiedIndex(null), 2000);
                } catch (err) {
                    console.error('Failed to copy text:', err);
                }
            };

            const handleSubmit = (e) => {
                e.preventDefault();
                generatePodcastNames();
            };

            const handleKeyPress = (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    generatePodcastNames();
                }
            };

            return (
                <div style={{
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif',
                    maxWidth: '900px',
                    margin: '0 auto',
                    padding: '20px',
                    background: '#ffffff',
                    borderRadius: '16px',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                    width: '100%',
                    minHeight: '600px'
                }}>
                    <div style={{ width: '100%' }}>
                        {/* Header Section */}
                        <h1 style={{
                            fontSize: '2.5rem',
                            fontWeight: 700,
                            color: '#1a1a1a',
                            textAlign: 'center',
                            margin: '0 0 8px 0',
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            backgroundClip: 'text'
                        }}>
                            Free Podcast Name Generator
                        </h1>

                        <h2 style={{
                            fontSize: '1.5rem',
                            fontWeight: 600,
                            color: '#4a5568',
                            textAlign: 'center',
                            margin: '0 0 24px 0',
                            lineHeight: 1.4
                        }}>
                            Create the Perfect Name for Your Podcast in Seconds
                        </h2>

                        {/* Trust Indicators */}
                        <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            gap: '24px',
                            marginBottom: '32px',
                            flexWrap: 'wrap'
                        }}>
                            {['100% Free Forever', 'No Sign-up Required', 'Instant Results'].map((text, index) => (
                                <div key={index} style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    color: '#4a5568',
                                    fontSize: '0.95rem',
                                    fontWeight: 500
                                }}>
                                    <div style={{
                                        width: '20px',
                                        height: '20px',
                                        borderRadius: '50%',
                                        backgroundColor: '#6941C7',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: 'white',
                                        fontSize: '12px',
                                        fontWeight: 'bold'
                                    }}>
                                        ✓
                                    </div>
                                    {text}
                                </div>
                            ))}
                        </div>

                        <p style={{
                            fontSize: '1.1rem',
                            color: '#666',
                            textAlign: 'center',
                            margin: '0 0 32px 0',
                            lineHeight: 1.5
                        }}>
                            Describe your podcast topic and get 4 catchy, high-converting name suggestions powered by AI
                        </p>

                        {/* Test Button for Development */}
                        <div style={{ textAlign: 'center', marginBottom: '20px' }}>
                            <button 
                                onClick={() => {
                                    console.log('Component loaded successfully!');
                                    alert('Component is working! Check console for details.');
                                }}
                                style={{
                                    padding: '10px 20px',
                                    background: '#10b981',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '8px',
                                    cursor: 'pointer'
                                }}
                            >
                                Test Component
                            </button>
                        </div>

                        {/* Input Section */}
                        <div style={{ marginBottom: '32px' }}>
                            <form onSubmit={handleSubmit}>
                                <div style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '16px'
                                }}>
                                    <textarea
                                        value={input}
                                        onChange={(e) => setInput(e.target.value)}
                                        onKeyPress={handleKeyPress}
                                        placeholder="Describe what your podcast is about..."
                                        style={{
                                            width: '100%',
                                            padding: '16px 20px',
                                            fontSize: '1rem',
                                            border: '2px solid #e1e5e9',
                                            borderRadius: '12px',
                                            resize: 'vertical',
                                            minHeight: '80px',
                                            fontFamily: 'inherit',
                                            transition: 'all 0.2s ease',
                                            boxSizing: 'border-box'
                                        }}
                                        rows={3}
                                        disabled={loading}
                                    />

                                    <button
                                        type="submit"
                                        disabled={loading || !input.trim() || usageCount >= MAX_USAGE}
                                        style={{
                                            padding: '14px 28px',
                                            fontSize: '1rem',
                                            fontWeight: 600,
                                            color: 'white',
                                            background: loading || !input.trim() || usageCount >= MAX_USAGE
                                                ? '#9ca3af'
                                                : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                            border: 'none',
                                            borderRadius: '12px',
                                            cursor: loading || !input.trim() || usageCount >= MAX_USAGE ? 'not-allowed' : 'pointer',
                                            transition: 'all 0.2s ease',
                                            minWidth: '160px'
                                        }}
                                    >
                                        {loading ? 'Generating...' : 'Generate Names'}
                                    </button>
                                </div>
                            </form>
                        </div>

                        {/* Error Display */}
                        {error && (
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '8px',
                                padding: '16px 20px',
                                backgroundColor: '#fef2f2',
                                border: '1px solid #fecaca',
                                borderRadius: '12px',
                                color: '#dc2626',
                                fontWeight: 500,
                                marginBottom: '24px'
                            }}>
                                <span style={{ fontSize: '1.2rem' }}>⚠️</span>
                                {error}
                            </div>
                        )}

                        {/* Loading State */}
                        {loading && (
                            <div style={{
                                textAlign: 'center',
                                padding: '40px 20px',
                                color: '#666'
                            }}>
                                <div style={{
                                    width: '40px',
                                    height: '40px',
                                    border: '3px solid #f3f3f3',
                                    borderTop: '3px solid #667eea',
                                    borderRadius: '50%',
                                    animation: 'spin 1s linear infinite',
                                    margin: '0 auto 16px'
                                }}></div>
                                <p>Generating creative podcast names...</p>
                            </div>
                        )}

                        {/* Results would go here */}
                        {results.length > 0 && (
                            <div style={{ marginTop: '32px' }}>
                                <h3 style={{
                                    fontSize: '1.4rem',
                                    fontWeight: 600,
                                    color: '#1a1a1a',
                                    margin: '0 0 20px 0',
                                    textAlign: 'center'
                                }}>
                                    Generated Names
                                </h3>
                                <div style={{
                                    display: 'grid',
                                    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                                    gap: '16px'
                                }}>
                                    {results.map((result, index) => (
                                        <div key={index} style={{
                                            background: '#ffffff',
                                            border: '1px solid #e5e7eb',
                                            borderRadius: '12px',
                                            padding: '20px',
                                            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
                                        }}>
                                            <h4 style={{
                                                fontSize: '1.2rem',
                                                fontWeight: 600,
                                                color: '#1a1a1a',
                                                margin: '0 0 8px 0'
                                            }}>
                                                {result.name}
                                            </h4>
                                            <p style={{
                                                color: '#6b7280',
                                                margin: '0 0 16px 0',
                                                fontSize: '0.9rem'
                                            }}>
                                                {result.description}
                                            </p>
                                            <div style={{
                                                display: 'flex',
                                                gap: '8px',
                                                justifyContent: 'center'
                                            }}>
                                                <button
                                                    onClick={() => handleLike(index)}
                                                    style={{
                                                        padding: '8px 16px',
                                                        background: '#10b981',
                                                        color: 'white',
                                                        border: 'none',
                                                        borderRadius: '8px',
                                                        cursor: 'pointer'
                                                    }}
                                                >
                                                    👍 Like
                                                </button>
                                                <button
                                                    onClick={() => handleDislike(index)}
                                                    style={{
                                                        padding: '8px 16px',
                                                        background: '#f3f4f6',
                                                        color: '#374151',
                                                        border: '1px solid #d1d5db',
                                                        borderRadius: '8px',
                                                        cursor: 'pointer'
                                                    }}
                                                >
                                                    👎 Pass
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            );
        }

        // Render the component
        ReactDOM.render(<PodcastNameGenerator />, document.getElementById('root'));
    </script>
</body>
</html>

// Test script for the learning algorithm
// This simulates user feedback and tests the preference analysis

// Mock data structures (simplified versions of the TypeScript interfaces)
const mockFeedback = [
  // Liked names - short, professional style
  { name: "Business Brief", description: "Professional and concise name for business content", liked: true, timestamp: Date.now(), index: 0 },
  { name: "Pro Talk", description: "Professional discussion format", liked: true, timestamp: Date.now(), index: 1 },
  
  // Disliked names - long, playful style  
  { name: "The Amazing Super Fun Business Adventure Podcast", description: "Fun and playful approach to business", liked: false, timestamp: Date.now(), index: 2 },
  { name: "Giggles and Growth", description: "Playful take on personal development", liked: false, timestamp: Date.now(), index: 3 },
];

// Simplified learning functions (JavaScript versions)
function analyzeLength(liked, disliked) {
  if (liked.length === 0) return null;
  
  const likedLengths = liked.map(n => n.name.split(' ').length);
  const dislikedLengths = disliked.map(n => n.name.split(' ').length);
  const avgLikedLength = likedLengths.reduce((a, b) => a + b, 0) / likedLengths.length;
  
  const avgDislikedLength = dislikedLengths.length > 0 
    ? dislikedLengths.reduce((a, b) => a + b, 0) / dislikedLengths.length 
    : 0;
  
  console.log(`Average liked length: ${avgLikedLength}`);
  console.log(`Average disliked length: ${avgDislikedLength}`);
  
  if (avgLikedLength <= 2 && avgDislikedLength > 2) return 'short';
  if (avgLikedLength <= 4 && (avgDislikedLength <= 2 || avgDislikedLength > 4)) return 'medium';
  if (avgLikedLength > 4 && avgDislikedLength <= 4) return 'long';
  
  if (avgLikedLength <= 2) return 'short';
  if (avgLikedLength <= 4) return 'medium';
  return 'long';
}

function analyzeStyle(liked, disliked) {
  if (liked.length === 0) return null;
  
  const likedDescriptions = liked.map(n => n.description.toLowerCase()).join(' ');
  const dislikedDescriptions = disliked.map(n => n.description.toLowerCase()).join(' ');
  
  console.log(`Liked descriptions: ${likedDescriptions}`);
  console.log(`Disliked descriptions: ${dislikedDescriptions}`);
  
  if (likedDescriptions.includes('professional') || likedDescriptions.includes('business')) {
    if (!dislikedDescriptions.includes('professional') && !dislikedDescriptions.includes('business')) {
      return 'professional';
    }
  }
  if (likedDescriptions.includes('creative') || likedDescriptions.includes('unique')) {
    if (!dislikedDescriptions.includes('creative') && !dislikedDescriptions.includes('unique')) {
      return 'creative';
    }
  }
  if (likedDescriptions.includes('fun') || likedDescriptions.includes('playful')) {
    if (!dislikedDescriptions.includes('fun') && !dislikedDescriptions.includes('playful')) {
      return 'playful';
    }
  }
  return 'descriptive';
}

function extractKeywords(feedback) {
  const keywords = [];
  feedback.forEach(f => {
    const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    const nameWords = f.name.toLowerCase().split(/\s+/).filter(word => 
      word.length > 2 && !commonWords.includes(word)
    );
    keywords.push(...nameWords);
  });
  
  const frequency = {};
  keywords.forEach(word => frequency[word] = (frequency[word] || 0) + 1);
  
  return Object.entries(frequency)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .map(([word]) => word);
}

function generateAdaptivePrompt(input, preferences) {
  const basePrompt = `Create 5 high-converting and catchy podcast names based on the following user input: ${input}.`;
  
  let adaptiveInstructions = '';
  
  if (preferences.patterns.preferredLength) {
    const lengthMap = {
      'short': '1-2 words',
      'medium': '3-4 words', 
      'long': '5+ words'
    };
    adaptiveInstructions += `Focus on ${preferences.patterns.preferredLength} names (${lengthMap[preferences.patterns.preferredLength]}). `;
  }
  
  if (preferences.patterns.preferredStyle) {
    adaptiveInstructions += `Use a ${preferences.patterns.preferredStyle} style. `;
  }
  
  if (preferences.patterns.likedKeywords.length > 0) {
    adaptiveInstructions += `Incorporate concepts similar to: ${preferences.patterns.likedKeywords.join(', ')}. `;
  }
  
  if (preferences.patterns.dislikedKeywords.length > 0) {
    adaptiveInstructions += `Avoid concepts like: ${preferences.patterns.dislikedKeywords.join(', ')}. `;
  }
  
  return `${basePrompt} ${adaptiveInstructions}Return as JSON: {"podcast_names": [...]}`;
}

// Run the test
console.log('🧪 Testing Learning Algorithm\n');

const liked = mockFeedback.filter(f => f.liked === true);
const disliked = mockFeedback.filter(f => f.liked === false);

console.log('📊 Feedback Analysis:');
console.log(`Liked names: ${liked.map(n => n.name).join(', ')}`);
console.log(`Disliked names: ${disliked.map(n => n.name).join(', ')}\n`);

console.log('🔍 Pattern Analysis:');
const preferredLength = analyzeLength(liked, disliked);
const preferredStyle = analyzeStyle(liked, disliked);
const likedKeywords = extractKeywords(liked);
const dislikedKeywords = extractKeywords(disliked);

console.log(`Preferred Length: ${preferredLength}`);
console.log(`Preferred Style: ${preferredStyle}`);
console.log(`Liked Keywords: ${likedKeywords.join(', ')}`);
console.log(`Disliked Keywords: ${dislikedKeywords.join(', ')}\n`);

const mockPreferences = {
  likedNames: liked,
  dislikedNames: disliked,
  patterns: {
    preferredLength,
    preferredStyle,
    likedKeywords,
    dislikedKeywords,
  },
  generationRound: 2
};

console.log('🎯 Adaptive Prompt Generation:');
const adaptivePrompt = generateAdaptivePrompt('business podcast', mockPreferences);
console.log(adaptivePrompt);

console.log('\n✅ Learning Algorithm Test Complete!');
console.log('Expected: Short, professional names with business-related keywords');
console.log('Avoiding: Long, playful names with fun-related concepts');

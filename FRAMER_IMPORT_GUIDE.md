# 🎯 Framer.com Import Guide - Enhanced Podcast Name Generator

This guide will walk you through importing the **Enhanced Podcast Name Generator** component into your Framer.com website.

## ✨ What's New in Enhanced Version
- **🧠 Intelligent Feedback System**: Users can like/dislike names to teach the AI their preferences
- **🎯 Progressive Refinement**: "Generate Better Names" button appears after feedback
- **📊 Learning Algorithm**: Analyzes user preferences for length, style, and keywords
- **🎨 Visual Feedback**: Cards change appearance based on user preferences
- **📱 Mobile Optimized**: Touch-friendly feedback buttons for all devices

> **Note**: All existing functionality is preserved. The enhanced version is fully backward-compatible.

## 📋 Prerequisites

- A Framer.com account with a project
- Basic familiarity with Framer's Code Components feature

## 🚀 Step-by-Step Import Process

### Step 1: Access Code Components

1. Open your Framer project
2. In the left sidebar, click on **"Assets"**
3. Click on **"Code"** tab
4. Click **"Create Code Component"**

### Step 2: Create the Component

1. **Name your component**: `PodcastNameGenerator`
2. **Choose React** as the framework
3. **Replace the default code** with the component code (see below)

### Step 3: Component Code

Copy and paste this code into your Framer Code Component:

```tsx
import React, { useState } from 'react';
import { addPropertyControls, ControlType } from 'framer';

interface PodcastName {
  name: string;
  description: string;
}

interface ApiResponse {
  podcast_names: PodcastName[];
}

interface PodcastNameGeneratorProps {
  apiKey?: string;
  width?: number;
  height?: number;
}

export default function PodcastNameGenerator(props: PodcastNameGeneratorProps) {
  const [input, setInput] = useState('');
  const [results, setResults] = useState<PodcastName[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const generatePodcastNames = async () => {
    if (!input.trim()) {
      setError('Please describe what your podcast is about');
      return;
    }

    setLoading(true);
    setError(null);
    setResults([]);

    try {
      const prompt = `Create 5 high-converting and catchy podcast names based on the following user input: ${input}. Return the response as a valid JSON object with this exact structure: {"podcast_names": [{"name": "Podcast Name 1", "description": "Brief explanation of why this name works"}, {"name": "Podcast Name 2", "description": "Brief explanation of why this name works"}, ...]}. Ensure all 5 names are creative, memorable, and relevant to the input topic.`;

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${props.apiKey || 'AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM'}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error('Invalid response format from API');
      }

      const generatedText = data.candidates[0].content.parts[0].text;
      
      const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in API response');
      }

      const parsedResponse: ApiResponse = JSON.parse(jsonMatch[0]);
      
      if (!parsedResponse.podcast_names || !Array.isArray(parsedResponse.podcast_names)) {
        throw new Error('Invalid response structure');
      }

      setResults(parsedResponse.podcast_names);
    } catch (err) {
      console.error('Error generating podcast names:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    generatePodcastNames();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      generatePodcastNames();
    }
  };

  return (
    <div style={{
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif',
      maxWidth: '920px', // Increased by 15% from 800px
      margin: '0 auto',
      padding: '20px',
      background: '#ffffff',
      borderRadius: '16px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
      width: '100%',
      minHeight: '600px'
    }}>
      <div style={{ width: '100%' }}>
        <h2 style={{
          fontSize: '2.5rem',
          fontWeight: 700,
          color: '#1a1a1a',
          textAlign: 'center',
          margin: '0 0 12px 0',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        }}>
          Podcast Name Generator
        </h2>
        <p style={{
          fontSize: '1.1rem',
          color: '#666',
          textAlign: 'center',
          margin: '0 0 32px 0',
          lineHeight: 1.5
        }}>
          Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI
        </p>
        
        <form onSubmit={handleSubmit} style={{ marginBottom: '32px' }}>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '16px'
          }}>
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Describe what your podcast is about"
              style={{
                width: '100%',
                padding: '16px 20px',
                fontSize: '1rem',
                border: '2px solid #e1e5e9',
                borderRadius: '12px',
                resize: 'vertical',
                minHeight: '80px',
                fontFamily: 'inherit',
                transition: 'all 0.2s ease',
                boxSizing: 'border-box'
              }}
              rows={3}
              disabled={loading}
            />
            <button
              type="submit"
              disabled={loading || !input.trim()}
              style={{
                alignSelf: 'flex-start',
                padding: '14px 28px',
                fontSize: '1rem',
                fontWeight: 600,
                color: 'white',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '12px',
                cursor: loading || !input.trim() ? 'not-allowed' : 'pointer',
                transition: 'all 0.2s ease',
                minWidth: '160px',
                opacity: loading || !input.trim() ? 0.6 : 1
              }}
            >
              {loading ? 'Generating...' : 'Generate Names'}
            </button>
          </div>
        </form>

        {error && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '16px 20px',
            backgroundColor: '#fef2f2',
            border: '1px solid #fecaca',
            borderRadius: '12px',
            color: '#dc2626',
            fontWeight: 500,
            marginBottom: '24px'
          }}>
            <span style={{ fontSize: '1.2rem' }}>⚠️</span>
            {error}
          </div>
        )}

        {loading && (
          <div style={{
            textAlign: 'center',
            padding: '40px 20px',
            color: '#666'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '3px solid #f3f3f3',
              borderTop: '3px solid #667eea',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 16px'
            }}></div>
            <p>Generating creative podcast names...</p>
          </div>
        )}

        {results.length > 0 && (
          <div style={{ marginTop: '32px' }}>
            <h3 style={{
              fontSize: '1.5rem',
              fontWeight: 600,
              color: '#1a1a1a',
              margin: '0 0 24px 0',
              textAlign: 'center'
            }}>
              Your Podcast Name Suggestions
            </h3>
            <div style={{ display: 'grid', gap: '20px' }}>
              {results.map((result, index) => (
                <div key={index} style={{
                  background: '#f8f9fa',
                  border: '1px solid #e9ecef',
                  borderRadius: '12px',
                  padding: '20px',
                  transition: 'all 0.2s ease'
                }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    gap: '16px',
                    marginBottom: '12px'
                  }}>
                    <h4 style={{
                      fontSize: '1.25rem',
                      fontWeight: 600,
                      color: '#1a1a1a',
                      margin: 0,
                      flex: 1,
                      lineHeight: 1.3
                    }}>
                      {result.name}
                    </h4>
                    <button
                      onClick={() => copyToClipboard(result.name, index)}
                      style={{
                        padding: '8px 12px',
                        fontSize: '0.875rem',
                        fontWeight: 500,
                        background: '#667eea',
                        color: 'white',
                        border: 'none',
                        borderRadius: '8px',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        whiteSpace: 'nowrap',
                        flexShrink: 0
                      }}
                      title="Copy podcast name"
                    >
                      {copiedIndex === index ? '✓ Copied!' : '📋 Copy'}
                    </button>
                  </div>
                  <p style={{
                    color: '#666',
                    lineHeight: 1.5,
                    margin: 0,
                    fontSize: '0.95rem'
                  }}>
                    {result.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Framer property controls
addPropertyControls(PodcastNameGenerator, {
  apiKey: {
    type: ControlType.String,
    title: "API Key",
    description: "Google Gemini API Key (optional - uses default if not provided)",
    defaultValue: "AIzaSyCypHME7OIJxwwkUCTGxa93jI9bF_kjiTM",
    displayTextArea: true,
  },
});

// Set default props for Framer
PodcastNameGenerator.defaultProps = {
  width: 920, // Increased by 15% from 800px
  height: 800,
};
```

### Step 4: Add CSS Animation

Add this CSS to your Framer project's custom CSS (in Project Settings > General > Custom Code > Head):

```css
<style>
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
```

### Step 5: Use the Component

1. **Save the component** in Framer
2. **Drag it onto your canvas** from the Code Components section
3. **Resize as needed** (recommended: 920px wide, 800px+ tall)
4. **Configure the API key** in the component properties panel (optional)

## 🎛️ Component Properties

In Framer, you'll see these configurable properties:

- **API Key**: Your Google Gemini API key (optional, uses default if empty)

## 📐 Recommended Dimensions

- **Minimum Width**: 920px
- **Minimum Height**: 800px
- **Responsive**: Component will adapt to smaller screens automatically

## 🔧 Customization Tips

1. **Background**: The component has a white background with shadow - adjust your page background accordingly
2. **Spacing**: Leave some margin around the component for best visual appearance
3. **Mobile**: Component is fully responsive and will work on mobile devices

## ✅ Testing Your Component

1. **Preview your site** in Framer
2. **Enter a test description** like "A podcast about entrepreneurship and business tips"
3. **Click Generate Names** and verify the API response
4. **Test the copy functionality** by clicking the copy buttons

## 🚨 Troubleshooting

**Component not working?**
- Check browser console for errors
- Verify internet connection
- Try a different input description

**API errors?**
- The default API key should work for testing
- For production, get your own Google Gemini API key

**Styling issues?**
- Component uses inline styles for maximum compatibility
- All styles are self-contained

## 🎉 You're Done!

Your Podcast Name Generator is now ready to use in your Framer website! Visitors can generate creative, AI-powered podcast names that will help them launch their shows with compelling titles.
